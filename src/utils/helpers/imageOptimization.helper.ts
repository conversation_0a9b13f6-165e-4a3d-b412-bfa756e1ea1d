import { buildUrl } from "build-url-ts";
import { Platform } from "react-native";
import { scale } from "./dimensionScale.helper";

/**
 * Image Optimization Helper
 *
 * This utility helps optimize image URLs by adding query parameters for resizing,
 * formatting, and quality control.
 *
 * @example
 * // Basic usage with preset
 * const url = optimizeImageUrl('https://example.com/image.jpg', 'OPTIMIZED');
 * // Result: https://example.com/image.jpg?width=480&height=270&fit=crop&crop=entropy&auto=format,compress&quality=60
 *
 * // Custom options
 * const url = optimizeImageUrl('https://example.com/image.jpg', {
 *   width: 800,
 *   height: 600,
 *   quality: 80,
 *   fit: 'crop'
 * });
 *
 * // With existing query parameters
 * const url = optimizeImageUrl('https://example.com/image.jpg?version=2', 'TV');
 * // Preserves existing parameters and adds optimization parameters
 * // Result: https://example.com/image.jpg?version=2&width=1280&height=720&fit=crop&crop=entropy&auto=format,compress&quality=75&dpr=2
 */

// Types for image optimization options
export interface ImageOptimizationOptions {
	width?: number;
	height?: number;
	fit?: "crop" | "clip" | "fill" | "scale" | "max";
	crop?:
		| "entropy"
		| "edges"
		| "faces"
		| "focalpoint"
		| "top"
		| "bottom"
		| "left"
		| "right";
	auto?: string;
	quality?: number;
	dpr?: number;
}

// Platform-specific image dimensions (base values before scaling)
const TV_DIMENSIONS = {
	THUMBNAIL: {
		width: 480,
		height: 270,
	},
	FEATURED: {
		width: 600,
		height: 337,
	},
};

// Preset configurations with TV-specific optimizations
export const PRESETS = {
	DEFAULT: {
		fit: "crop",
		crop: "entropy",
		auto: "format,compress",
		quality: 60,
	},
	OPTIMIZED: {
		width: Platform.isTV ? scale(TV_DIMENSIONS.THUMBNAIL.width) : 480,
		height: Platform.isTV
			? scale(TV_DIMENSIONS.THUMBNAIL.height)
			: 270,
		fit: "crop",
		crop: "entropy",
		auto: "format,compress",
		quality: 60,
	},
	TV: {
		width: Platform.isTV
			? scale(TV_DIMENSIONS.FEATURED.width)
			: TV_DIMENSIONS.FEATURED.width,
		height: Platform.isTV
			? scale(TV_DIMENSIONS.FEATURED.height)
			: TV_DIMENSIONS.FEATURED.height,
		fit: "crop",
		crop: "entropy",
		auto: "format,compress",
		quality: 75,
		dpr: 2,
	},
	CLUB_LOGO: {
		width: Platform.isTV ? scale(190) : 190,
		height: Platform.isTV ? scale(270) : 270,
		fit: "crop",
		crop: "entropy",
		auto: "format,compress",
		quality: 90,
	},
	// Added for CategoryCard component
	CATEGORY_THUMBNAIL: {
		width: Platform.isTV ? scale(150) : 150,
		height: Platform.isTV ? scale(100) : 100,
		fit: "crop",
		crop: "entropy",
		auto: "format,compress",
		quality: 80,
	},
} as const;

/**
 * Optimizes an image URL by adding transformation parameters
 * Prevents duplicate parameters by checking existing URL parameters first
 *
 * @example
 * optimizeImageUrl('http://example.com/image.jpg', 'OPTIMIZED');
 * // Result: http://example.com/image.jpg?width=480&height=270&fit=crop&crop=entropy&auto=format%2Ccompress&quality=60
 *
 * optimizeImageUrl('http://example.com/image.jpg?width=800', 'OPTIMIZED');
 * // Result: http://example.com/image.jpg?width=800&height=270&fit=crop&crop=entropy&auto=format%2Ccompress&quality=60
 * // (preserves existing width, adds missing parameters)
 */
export const optimizeImageUrl = (
	imageUrl: string | null | undefined,
	options?: ImageOptimizationOptions | keyof typeof PRESETS
): string | null => {
	// Early return if URL is invalid
	if (!imageUrl || typeof imageUrl !== "string") {
		console.log("[imageOptimization] Invalid URL:", imageUrl);
		return null;
	}

	// Check if it's already a file:// URL
	if (imageUrl.startsWith("file://")) {
		console.log(
			"[imageOptimization] Local file URL, skipping optimization:",
			imageUrl
		);
		return imageUrl;
	}

	try {
		// Check if URL already has optimization parameters using simple string matching
		// This is more reliable in React Native environment than URL parsing
		const hasOptimizationParams =
			imageUrl.includes("width=") ||
			imageUrl.includes("height=") ||
			imageUrl.includes("fit=") ||
			imageUrl.includes("quality=");

		// If URL already has optimization parameters, return as-is to prevent duplication
		if (hasOptimizationParams) {
			// console.log("[imageOptimization] URL already optimized, skipping:", imageUrl);
			return imageUrl;
		}

		// Determine which options to use
		let finalOptions: ImageOptimizationOptions;
		if (typeof options === "string" && options in PRESETS) {
			finalOptions = PRESETS[options as keyof typeof PRESETS];
		} else {
			finalOptions = {
				...PRESETS.DEFAULT,
				...((options as ImageOptimizationOptions) || {}),
			};
		}

		// Filter out undefined values
		const queryParams = Object.fromEntries(
			Object.entries(finalOptions).filter(
				([_, value]) => value !== undefined
			)
		);

		// Only apply optimization for remote URLs
		if (imageUrl.startsWith("http")) {
			const result = buildUrl(imageUrl, {
				queryParams,
				disableCSV: true,
			});
			// console.log("[imageOptimization] Optimized URL:", result);
			return result;
		}

		return imageUrl;
	} catch (error) {
		console.error("[imageOptimization] Error:", error);
		return imageUrl; // Return original URL if there's an error
	}
};

/**
 * Calculates height maintaining aspect ratio
 *
 * @param width - Desired width in pixels
 * @param ratio - Aspect ratio (default: 16/9)
 * @returns Calculated height in pixels
 */
export const calculateAspectRatioHeight = (
	width: number,
	ratio: number = 16 / 9
): number => {
	return Math.round(width * (1 / ratio));
};
