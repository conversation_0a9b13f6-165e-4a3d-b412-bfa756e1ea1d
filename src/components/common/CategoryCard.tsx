import React from "react";
import {
	View,
	Text,
	Image,
	TouchableOpacity,
	StyleSheet,
	StyleProp,
	ViewStyle,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import {
	optimizeImageUrl,
	PRESETS,
} from "../../utils/helpers/imageOptimization.helper";

// Default placeholder image
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");

interface CategoryCardProps {
	id: string;
	title: string;
	imageUrl?: string;
	onPress: () => void;
	style?: StyleProp<ViewStyle>;
}

/**
 * CategoryCard Component
 *
 * Displays a category with an image and title
 * Used in grid layouts for categories, clubs, etc.
 */
const CategoryCard: React.FC<CategoryCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	style,
}) => {
	// Optimize image URL if available
	const optimizedImageUrl = imageUrl
		? optimizeImageUrl(imageUrl, PRESETS.CATEGORY_THUMBNAIL)
		: null;

	return (
		<TouchableOpacity
			style={[styles.container, style, { opacity: 0.7 }]}
			onPress={onPress}
			activeOpacity={1}
			accessible={true}
			accessibilityLabel={`Category: ${title}`}
			accessibilityRole="button"
		>
			<View style={styles.imageContainer}>
				<Image
					source={
						optimizedImageUrl
							? { uri: optimizedImageUrl }
							: PLACEHOLDER_IMAGE
					}
					style={styles.image}
					resizeMode="cover"
				/>
			</View>
			<Text
				style={styles.title}
				numberOfLines={2}
				ellipsizeMode="tail"
			>
				{title}
			</Text>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		width: scale(200), // Increased width for bigger cards
		borderRadius: scale(10), // Slightly increased border radius
		overflow: "hidden",
		backgroundColor: "transparent", // Removed background color for cleaner look
	},
	imageContainer: {
		width: "100%",
		height: scale(140),
		borderRadius: scale(10), // Consistent border radius for the entire card
		overflow: "hidden",
		backgroundColor: "#1a3c6b", // Background color when no image is available
	},
	image: {
		width: "100%",
		height: "100%",
	},
	title: {
		fontSize: scale(18), // Increased font size
		fontWeight: "500",
		color: "white",
		padding: scale(12), // Increased padding
		textAlign: "left", // Left-aligned text as requested
		marginTop: scale(6), // Added top margin
	},
});

export default React.memo(CategoryCard);
