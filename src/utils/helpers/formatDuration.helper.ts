/**
 * Format Duration Helper
 *
 * Utility functions for formatting time durations in various formats.
 */

/**
 * Formats a video duration string into a user-friendly format
 *
 * @param duration - Duration string in format "HH:MM:SS" or "MM:SS"
 * @returns Formatted duration string (e.g., "08:36" or "1:24:30")
 */
export const formatVideoDuration = (duration?: string): string => {
	if (!duration) return "";

	// If duration is in format "HH:MM:SS"
	const parts = duration.split(":");
	if (parts.length === 3) {
		// If hours is 00, just show minutes and seconds
		if (parts[0] === "00") {
			return `${parts[1]}:${parts[2]}`;
		}
		// Remove leading zero from hours if present (e.g., "01:24:30" -> "1:24:30")
		if (parts[0].startsWith("0") && parts[0].length > 1) {
			return `${parseInt(parts[0], 10)}:${parts[1]}:${parts[2]}`;
		}
		// Otherwise show hours:minutes:seconds
		return duration;
	}

	// If duration is already in MM:SS format, return as is
	return duration;
};

/**
 * Converts seconds to a formatted duration string
 *
 * @param seconds - Duration in seconds
 * @returns Formatted duration string (e.g., "08:36" or "1:24:30")
 */
export const secondsToFormattedDuration = (
	seconds: number
): string => {
	if (!seconds || seconds <= 0) return "";

	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	const remainingSeconds = Math.floor(seconds % 60);

	// Format with leading zeros
	const formattedMinutes = minutes.toString().padStart(2, "0");
	const formattedSeconds = remainingSeconds
		.toString()
		.padStart(2, "0");

	// Only include hours if there are any, without leading zeros
	if (hours > 0) {
		return `${hours}:${formattedMinutes}:${formattedSeconds}`;
	}

	return `${formattedMinutes}:${formattedSeconds}`;
};

/**
 * Universal duration formatter that handles multiple input types
 *
 * @param duration - Duration as string ("HH:MM:SS", "MM:SS") or number (seconds)
 * @returns Formatted duration string or null if invalid input
 */
export const formatDuration = (
	duration: string | number | null | undefined
): string | null => {
	if (!duration) return null;

	// If duration is already a string in MM:SS or HH:MM:SS format, format it
	if (typeof duration === "string") {
		// Check if it's already in time format with colons
		if (duration.includes(":")) {
			// Use formatVideoDuration to handle all cases consistently
			return formatVideoDuration(duration);
		}

		// Try to convert string to number if it represents seconds
		const seconds = parseInt(duration, 10);
		if (isNaN(seconds)) return null;
		duration = seconds;
	}

	// Convert duration in seconds to HH:MM:SS format
	if (typeof duration === "number") {
		return secondsToFormattedDuration(duration);
	}

	return null;
};
