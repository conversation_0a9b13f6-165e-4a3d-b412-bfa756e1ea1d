import React, { useEffect, useState } from "react";
import { StyleSheet, View, Text } from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { useLanguage } from "../../contexts/LanguageContext";
import { getTranslation } from "../../utils/translations";

interface UpcomingLiveProps {
	title: string;
	startTime: string;
}

/**
 * Pads single digit numbers with leading zero
 */
const padNumber = (num: number): string =>
	num.toString().padStart(2, "0");

const UpcomingLiveModalTimer = ({ startTime }: UpcomingLiveProps) => {
	// Get current language from context
	const { currentLanguage } = useLanguage();

	const [timeLeft, setTimeLeft] = useState<{
		days: number;
		hours: number;
		minutes: number;
	}>({
		days: 0,
		hours: 0,
		minutes: 0,
	});

	useEffect(() => {
		const calculateTimeLeft = () => {
			const targetDate = new Date(startTime);
			const now = new Date();
			const difference = targetDate.getTime() - now.getTime();

			if (difference > 0) {
				const days = Math.floor(difference / (1000 * 60 * 60 * 24));
				const hours = Math.floor(
					(difference / (1000 * 60 * 60)) % 24
				);
				const minutes = Math.floor((difference / 1000 / 60) % 60);

				setTimeLeft({ days, hours, minutes });
			}
		};

		calculateTimeLeft();
		const timer = setInterval(calculateTimeLeft, 60000);

		return () => clearInterval(timer);
	}, [startTime]);

	// Use current language for date and time formatting
	const locale = currentLanguage === "fr" ? "fr-FR" : "en-US";

	const formattedDate = new Date(startTime).toLocaleDateString(
		locale,
		{
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
		}
	);

	const formattedTime = new Date(startTime).toLocaleTimeString(
		locale,
		{
			hour: "2-digit",
			minute: "2-digit",
		}
	);

	return (
		<View style={styles.container}>
			<View style={styles.countdownContainer}>
				<View style={styles.timeUnitsContainer}>
					{timeLeft.days > 0 && (
						<>
							<View style={styles.timeUnit}>
								<Text style={styles.timeValue}>
									{padNumber(timeLeft.days)}
								</Text>
								<Text style={styles.timeLabel}>
									{getTranslation("days", currentLanguage)}
								</Text>
							</View>
							<View style={styles.separator} />
						</>
					)}
					<View style={styles.timeUnit}>
						<Text style={styles.timeValue}>
							{padNumber(timeLeft.hours)}
						</Text>
						<Text style={styles.timeLabel}>
							{getTranslation("hours", currentLanguage)}
						</Text>
					</View>
					<View style={styles.separator} />
					<View style={styles.timeUnit}>
						<Text style={styles.timeValue}>
							{padNumber(timeLeft.minutes)}
						</Text>
						<Text style={styles.timeLabel}>
							{getTranslation("minutes", currentLanguage)}
						</Text>
					</View>
				</View>
				<View style={styles.dateContainer}>
					<Text style={styles.date}>{formattedDate}</Text>
					<Text style={styles.time}>⏰ {formattedTime}</Text>
				</View>
			</View>
		</View>
	);
};

export default UpcomingLiveModalTimer;

const styles = StyleSheet.create({
	container: {
		width: "100%",
		alignItems: "center",
		marginBottom: scale(30),
	},
	countdownContainer: {
		width: "90%",
		maxWidth: scale(750),
		backgroundColor: "#E61E1E",
		borderRadius: scale(12),
	},
	timeUnitsContainer: {
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "center",
		paddingVertical: scale(18),
	},
	timeUnit: {
		alignItems: "center",
		paddingHorizontal: scale(18),
	},
	separator: {
		width: scale(2),
		height: scale(60),
		backgroundColor: "#fff",
		marginHorizontal: scale(12),
	},
	timeValue: {
		color: "#FFFFFF",
		fontSize: scale(48),
		fontWeight: "bold",
		lineHeight: scale(54),
	},
	timeLabel: {
		color: "#FFFFFF",
		fontSize: scale(18),
		fontWeight: "500",
		marginTop: scale(3),
		opacity: 0.9,
	},
	dateContainer: {
		width: "100%",
		backgroundColor: "#1E2A4A",
		paddingVertical: scale(12),
		paddingHorizontal: scale(18),
		borderBottomLeftRadius: scale(12),
		borderBottomRightRadius: scale(12),
		flexDirection: "row",
		alignItems: "center",
		justifyContent: "space-between",
	},
	date: {
		color: "#FFFFFF",
		fontSize: scale(21),
		fontWeight: "500",
	},
	time: {
		color: "#FFFFFF",
		fontSize: scale(21),
		fontWeight: "500",
	},
});
