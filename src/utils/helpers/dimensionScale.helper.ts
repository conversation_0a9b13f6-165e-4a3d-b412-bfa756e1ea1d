import { Dimensions } from "react-native";

/**
 * Scaling utility to dynamically adjust UI elements based on screen width
 * Uses a reference width of 1920px (standard tvOS resolution) as base
 * This ensures consistent UI appearance across different TV devices and resolutions
 */

// Get the current screen dimensions
const { width } = Dimensions.get("window");

// Define reference width (standard tvOS resolution)
const baseWidth = 1920;

// Calculate scaling factor based on actual screen width
const scaleFactor = width / baseWidth;

/**
 * Scales a size value based on the device's screen width
 * @param size - Base size value (using tvOS values as reference)
 * @returns Scaled size value appropriate for current screen dimensions
 */
export const scale = (size: number): number => {
	// Apply scaling factor and round to nearest integer to avoid sub-pixel rendering issues
	return Math.round(size * scaleFactor);
};

/**
 * Usage examples:
 *
 * // Instead of:
 * // fontSize: Platform.OS === "android" ? 14 : 22
 *
 * // Use:
 * // fontSize: scale(22)
 *
 * // Instead of:
 * // borderRadius: Platform.OS === "android" ? 7 : 14
 *
 * // Use:
 * // borderRadius: scale(14)
 */
