import React, {
	createContext,
	useContext,
	useState,
	useEffect,
	ReactNode,
} from "react";
import { Platform } from "react-native";

// Simple storage solution optimized for TV platforms
// Uses localStorage for web platforms and in-memory storage for TV platforms

// Storage key for persisting language preference
const LANGUAGE_STORAGE_KEY = "app_language";

// Global variable to store language preference in memory as fallback
let globalLanguagePreference: string | null = null;

// Simple storage utilities for TV platforms
const StorageUtils = {
	// Try localStorage (for web platforms)
	tryLocalStorage(
		action: "get" | "set",
		value?: string
	): string | null {
		try {
			if (typeof window !== "undefined" && window.localStorage) {
				if (action === "get") {
					return window.localStorage.getItem(LANGUAGE_STORAGE_KEY);
				} else if (action === "set" && value) {
					window.localStorage.setItem(LANGUAGE_STORAGE_KEY, value);
					return value;
				}
			}
		} catch (error) {
			// localStorage not available
			console.log(" localStorage not available:", error);
		}
		return null;
	},

	// Fallback to global variable (in-memory storage)
	useGlobalFallback(
		action: "get" | "set",
		value?: string
	): string | null {
		if (action === "get") {
			return globalLanguagePreference;
		} else if (action === "set" && value) {
			globalLanguagePreference = value;
			return value;
		}
		return null;
	},

	// Main storage functions that try multiple methods
	async saveLanguage(language: string): Promise<void> {
		try {
			// Try localStorage first (for web platforms)
			const localResult = this.tryLocalStorage("set", language);
			if (localResult) {
				console.log(`Language saved to localStorage: ${language}`);
				return;
			}

			// Fallback to global variable (for TV platforms)
			this.useGlobalFallback("set", language);
			console.log(`Language saved to memory: ${language}`);
		} catch (error) {
			console.error("Error saving language:", error);
			// Still save to memory as last resort
			this.useGlobalFallback("set", language);
		}
	},

	async loadLanguage(): Promise<string | null> {
		try {
			// Try localStorage first (for web platforms)
			const localResult = this.tryLocalStorage("get");
			if (localResult) {
				console.log(
					`Language loaded from localStorage: ${localResult}`
				);
				// Also save to global fallback for faster access
				this.useGlobalFallback("set", localResult);
				return localResult;
			}

			// Try global fallback (for TV platforms)
			const globalResult = this.useGlobalFallback("get");
			if (globalResult) {
				console.log(`Language loaded from memory: ${globalResult}`);
				return globalResult;
			}

			console.log("No saved language found, using default");
			return null;
		} catch (error) {
			console.error("Error loading language:", error);
			return null;
		}
	},
};

// Supported languages
export type SupportedLanguage = "en" | "fr";

// Language context interface
interface LanguageContextType {
	currentLanguage: SupportedLanguage;
	setLanguage: (language: SupportedLanguage) => Promise<void>;
	isLoading: boolean;
}

// Create the context
const LanguageContext = createContext<
	LanguageContextType | undefined
>(undefined);

// Language provider props
interface LanguageProviderProps {
	children: ReactNode;
}

/**
 * Language Provider Component
 * Manages global language state with platform-appropriate persistence
 *
 * Features:
 * - Persists language preference during app session (TV platforms)
 * - Persists language preference across restarts (web platforms via localStorage)
 * - Provides global language state to all components
 * - Handles loading state during initialization
 */
export const LanguageProvider: React.FC<LanguageProviderProps> = ({
	children,
}) => {
	const [currentLanguage, setCurrentLanguage] =
		useState<SupportedLanguage>("en");
	const [isLoading, setIsLoading] = useState(true);

	/**
	 * Load saved language preference from AsyncStorage on app start
	 */
	useEffect(() => {
		const loadSavedLanguage = async () => {
			try {
				const savedLanguage = await StorageUtils.loadLanguage();
				if (
					savedLanguage &&
					(savedLanguage === "en" || savedLanguage === "fr")
				) {
					setCurrentLanguage(savedLanguage as SupportedLanguage);
					console.log(`Loaded saved language: ${savedLanguage}`);
				} else {
					console.log("No saved language found, using default: en");
				}
			} catch (error) {
				console.error("Error loading saved language:", error);
				// Keep default language (en) if loading fails
			} finally {
				setIsLoading(false);
			}
		};

		loadSavedLanguage();
	}, []);

	/**
	 * Set new language and persist to AsyncStorage
	 * @param language - The language to set
	 */
	const setLanguage = async (language: SupportedLanguage) => {
		try {
			// Update state immediately for responsive UI
			setCurrentLanguage(language);

			// Persist using our multi-platform storage utility
			await StorageUtils.saveLanguage(language);
		} catch (error) {
			console.error("Error saving language preference:", error);
			// Language change still works in current session even if saving fails
		}
	};

	const contextValue: LanguageContextType = {
		currentLanguage,
		setLanguage,
		isLoading,
	};

	return (
		<LanguageContext.Provider value={contextValue}>
			{children}
		</LanguageContext.Provider>
	);
};

/**
 * Custom hook to use the Language Context
 * @returns Language context value
 * @throws Error if used outside of LanguageProvider
 */
export const useLanguage = (): LanguageContextType => {
	const context = useContext(LanguageContext);
	if (context === undefined) {
		throw new Error(
			"useLanguage must be used within a LanguageProvider"
		);
	}
	return context;
};
