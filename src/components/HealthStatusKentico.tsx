import React, { useEffect, useState } from "react";
import {
	StyleSheet,
	Text,
	View,
	ActivityIndicator,
} from "react-native";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import { scale } from "../utils/helpers/dimensionScale.helper";

const HealthStatusKentico = () => {
	const [healthStatus, setHealthStatus] = useState<number | null>(
		null
	); // State to hold the health status
	const [loading, setLoading] = useState<boolean>(true); // State to manage loading state
	const [error, setError] = useState<string | null>(null); // State to hold any error messages

	// Fetch health status from the API
	useEffect(() => {
		const fetchHealthStatus = async () => {
			try {
				const response = await kenticoAPIClient.health.getHealth(); // Call the getHealth method
				setHealthStatus(response.status);
			} catch (err) {
				setError("Failed to fetch health status");
			} finally {
				setLoading(false); // Set loading to false after fetching
			}
		};

		fetchHealthStatus();
	}, []);

	// populated view
	return (
		<View style={styles.container}>
			<Text style={styles.title}>Health Status kentico api</Text>

			{loading ? (
				<ActivityIndicator
					size="large"
					color="#000"
				/> // Show loading indicator while fetching
			) : error ? (
				<Text style={styles.error}>{error}</Text> // Show error message if any
			) : (
				<Text style={styles.status}>
					Health Status: {healthStatus}
				</Text> // Display the health status
			)}
		</View>
	);
};

export default HealthStatusKentico;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#262626",
	},
	title: {
		fontSize: scale(24),
		marginBottom: scale(20),
	},
	status: {
		fontSize: scale(18),
		color: "#f9f8f0",
	},
	error: {
		color: "red",
	},
});
