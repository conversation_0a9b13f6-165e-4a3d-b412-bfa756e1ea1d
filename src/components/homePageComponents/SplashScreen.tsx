import React from "react";
import {
	View,
	Image,
	StyleSheet,
	ActivityIndicator,
} from "react-native";

const SplashScreen = () => {
	return (
		<View style={styles.container}>
			<Image
				source={require("../../assets/images/splash-logo.png")}
				style={styles.logo}
				resizeMode="contain"
			/>
			<View style={styles.spinnerContainer}>
				<ActivityIndicator
					size="large"
					color="#fff"
				/>
			</View>
		</View>
	);
};

export default SplashScreen;

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: "#102e55",
		justifyContent: "center",
		alignItems: "center",
	},
	logo: {
		width: "80%",
		height: "40%",
	},
	spinnerContainer: {
		position: "absolute",
		bottom: "25%",
	},
});
