/**
 * HomePage Component
 *
 * This is the main landing page of the application that displays:
 * - Video grid layout with different sections
 * - Live and upcoming events
 * - Categories and playlists
 * - Advertisement banners
 * - Language selector
 * - Authentication status
 *
 * The component uses:
 * - useHomePageData hook for data fetching
 * - HomePageSectionRenderer for rendering different section types
 */

// React and core functionality
import React, { useState, useCallback, useEffect } from "react";
// React Native UI components
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	TouchableOpacity,
	Image,
} from "react-native";
// Navigation utilities
import {
	useNavigation,
	useFocusEffect,
} from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
// Authentication service
// Language Context
import { useLanguage } from "../contexts/LanguageContext";
// Components
import LanguageSelector from "../components/homePageComponents/LanguageSelector";
import { AuthService } from "../services/authService";
import SplashScreen from "../components/homePageComponents/SplashScreen";
// Custom hooks
import { useHomePageData } from "../hooks/useHomePageData";
// Section renderer
import HomePageSectionRenderer from "../components/sections/pageRenderers/HomePageSectionRenderer";
// Styling
import { scale } from "../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";

/**
 * Constants
 */
// Additional delay after loading completes before hiding splash screen (in milliseconds)
const ADDITIONAL_SPLASH_DELAY = 150;

/**
 * HomePage Component
 * Main component for the home page that uses the HomePageSectionRenderer
 */
const HomePage = () => {
	// Navigation hook for screen transitions
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();

	// Language context hook
	const {
		currentLanguage,
		setLanguage,
		isLoading: languageLoading,
	} = useLanguage();

	// UI states
	const [isAuthenticated, setIsAuthenticated] = useState(false); // Start as false, will be checked on mount
	const [showSplash, setShowSplash] = useState(true);
	const [isAuthButtonFocused, setIsAuthButtonFocused] =
		useState(false);
	const [isLoginButtonFocused, setIsLoginButtonFocused] =
		useState(false);

	// Fetch home page data using the custom hook
	const {
		sections,
		liveEvents,
		upcomingEvents,
		error,
		refetch,
		isLoading,
	} = useHomePageData(currentLanguage);

	/**
	 * Initialize authentication on app start
	 * Check if user is already authenticated and set tokens accordingly
	 */
	useEffect(() => {
		const initializeAuth = async () => {
			try {
				const isUserAuthenticated =
					await AuthService.initializeAuth();
				setIsAuthenticated(isUserAuthenticated);
				console.log(
					"Authentication initialized:",
					isUserAuthenticated
						? "User is logged in"
						: "User is not logged in"
				);
			} catch (error) {
				console.error("Error initializing authentication:", error);
				setIsAuthenticated(false);
			}
		};

		initializeAuth();
	}, []); // Empty dependency array - runs once on mount

	/**
	 * Re-check authentication status when screen comes into focus
	 * This ensures the authentication state is updated when user returns from login page
	 */
	useFocusEffect(
		useCallback(() => {
			const checkAuthOnFocus = async () => {
				try {
					console.log(
						"HomePage focused - checking authentication status..."
					);
					const isUserAuthenticated =
						await AuthService.initializeAuth();
					setIsAuthenticated(isUserAuthenticated);
					console.log(
						"Authentication status updated:",
						isUserAuthenticated
							? "User is logged in"
							: "User is not logged in"
					);
				} catch (error) {
					console.error(
						"Error checking authentication on focus:",
						error
					);
					setIsAuthenticated(false);
				}
			};

			checkAuthOnFocus();
		}, [])
	);

	/**
	 * Language Change Handler
	 * Updates current language and triggers data refresh
	 */
	const handleLanguageChange = (language: string) => {
		// Use the context's setLanguage function which handles persistence
		setLanguage(language as "en" | "fr");
	};

	/**
	 * Login/Logout Button Handler
	 * If not authenticated, navigates to login page
	 * If authenticated, logs out the user
	 */
	const handleLoginLogoutPress = async () => {
		if (isAuthenticated) {
			// User is logged in, perform logout
			try {
				await AuthService.logout();
				setIsAuthenticated(false);
				console.log("User logged out successfully");
				// Refresh data after logout
				refetch();
			} catch (error) {
				console.error("Error during logout:", error);
			}
		} else {
			// User is not logged in, navigate to login page
			navigation.navigate("LoginPage");
		}
	};

	/**
	 * Authentication Toggle Handler (for development/testing)
	 * Toggles authentication state and refreshes data
	 */
	const toggleAuth = useCallback(async () => {
		if (isAuthenticated) {
			// Disable auth - logout user
			await AuthService.logout();
			setIsAuthenticated(false);
		} else {
			// Enable auth - check if user has stored tokens
			const isUserAuthenticated = await AuthService.initializeAuth();
			setIsAuthenticated(isUserAuthenticated);
		}
		// Refresh all data with new auth state
		refetch();
	}, [isAuthenticated, refetch]);

	/**
	 * Handle item press
	 * Navigates to the appropriate page based on the item type
	 */
	const handleItemPress = useCallback(
		(id: string, type: string, eventData?: any) => {
			// Handle competition items
			if (type === "competition") {
				// Make sure we have a valid pageCodename
				let pageCodename = eventData?.pageCodename || id;

				// If we still don't have a valid pageCodename, try to create one from the title
				if (!pageCodename && eventData?.name) {
					pageCodename = `page_${eventData.name
						.toLowerCase()
						.replace(/\s+/g, "_")}`;
				}

				console.log(
					`Navigating to competition page: ${pageCodename}`
				);

				// Log the full eventData for debugging
				console.log(
					"Competition eventData:",
					JSON.stringify(eventData)
				);

				// Only navigate if we have a valid pageCodename
				if (pageCodename) {
					navigation.navigate("CompetitionsPage", {
						pageCodename: pageCodename,
					});
				} else {
					console.error(
						"Cannot navigate to competition: no pageCodename available"
					);
				}
				return;
			}

			// Handle category items
			if (type === "category") {
				// Navigate to Categories page with category data
				navigation.navigate("Categories", {
					categoryId: id,
					categoryName: eventData?.name || "",
					categoryImage: eventData?.poster || "",
				});
				return;
			}

			// Handle video and event items
			if (
				type === "video" ||
				type === "event" ||
				type === "live-event"
			) {
				// Navigate to video player with required data
				navigation.navigate("VideoDetailsPage", {
					video: {
						videoId: id,
						title: eventData?.name || "",
						thumbnail:
							eventData?.poster || eventData?.thumbnail || "",
						description: eventData?.description || "",
						isLive: type === "live-event" || eventData?.isLive,
						startTime: eventData?.startDate,
					},
				});
			}
		},
		[navigation]
	);

	/**
	 * Loading and Error States
	 */
	// Effect to hide splash screen after loading completes plus additional delay
	React.useEffect(() => {
		if (!isLoading && showSplash) {
			// Add additional delay after loading completes before hiding splash screen
			const timer = setTimeout(
				() => setShowSplash(false),
				ADDITIONAL_SPLASH_DELAY
			);
			return () => clearTimeout(timer); // Cleanup timeout on unmount
		}
	}, [isLoading, showSplash]);

	// Show splash screen during initial load or while data is loading
	if (showSplash || isLoading) {
		return <SplashScreen />;
	}

	if (error) {
		return (
			<View style={styles.container}>
				<Text style={styles.error}>{error}</Text>
			</View>
		);
	}

	return (
		<View style={styles.container}>
			<View style={styles.headerContainer}>
				<View style={styles.headerLeft}>
					<Image
						source={require("../assets/images/logo_HandballTV_header.png")}
						style={styles.logo}
						resizeMode="contain"
					/>
					<TouchableOpacity
						style={[
							styles.authButton,
							isAuthButtonFocused && styles.authButtonFocused,
						]}
						onPress={toggleAuth}
						activeOpacity={1}
						onFocus={() => setIsAuthButtonFocused(true)}
						onBlur={() => setIsAuthButtonFocused(false)}
					>
						<Text
							style={[
								styles.authButtonText,
								isAuthButtonFocused && styles.authButtonTextFocused,
							]}
						>
							{isAuthenticated ? "Disable Auth" : "Enable Auth"}
						</Text>
					</TouchableOpacity>
				</View>
				<View style={styles.headerRight}>
					<LanguageSelector
						currentLanguage={currentLanguage}
						onLanguageChange={handleLanguageChange}
					/>
					<TouchableOpacity
						style={[
							styles.loginButton,
							isAuthenticated && styles.logoutButton, // Different style when logged in
							isLoginButtonFocused && styles.loginButtonFocused,
						]}
						onPress={handleLoginLogoutPress}
						activeOpacity={1}
						onFocus={() => setIsLoginButtonFocused(true)}
						onBlur={() => setIsLoginButtonFocused(false)}
					>
						<Text
							style={[
								styles.loginButtonText,
								isLoginButtonFocused && styles.loginButtonTextFocused,
							]}
						>
							{isAuthenticated ? "Logout" : "Login"}
						</Text>
					</TouchableOpacity>
				</View>
			</View>

			{/* Render the sections */}
			<FlatList
				data={sections}
				renderItem={({ item }) => (
					<HomePageSectionRenderer
						section={item}
						onItemPress={handleItemPress}
						upcomingEvents={[...liveEvents, ...upcomingEvents]} // Pass both live and upcoming events
					/>
				)}
				keyExtractor={(item) =>
					item._kenticoId || item._kenticoCodename
				}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={styles.contentContainer}
				// Performance optimizations for TV platforms
				removeClippedSubviews={true} // Unmounts items that are off screen to reduce memory usage
				maxToRenderPerBatch={5} // Limits number of items rendered per batch to prevent frame drops
				windowSize={5} // Number of items kept mounted for smooth scrolling
				initialNumToRender={5} // Initial number of items to render on mount
				updateCellsBatchingPeriod={50} // Time in ms between batch updates for smoother rendering
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	/**
	 * Core Layout Styles
	 */
	container: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		paddingTop: 12,
		paddingHorizontal: GLOBAL_STYLES.PAGE_HORIZONTAL_PADDING,
	},
	contentContainer: {
		paddingBottom: 120, // Increased bottom padding to prevent content from being cut off at the bottom of the screen
	},

	/**
	 * Header Component Styles
	 */
	headerContainer: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		paddingVertical: 10,
		paddingHorizontal: 20,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		marginHorizontal: -20,
	},
	headerLeft: {
		flexDirection: "row",
		alignItems: "center",
		gap: 16,
	},
	headerRight: {
		flexDirection: "row",
		alignItems: "center",
		gap: scale(16), // Add spacing between login button and language selector
	},
	logo: {
		width: 180,
	},

	/**
	 * Authentication Button Styles
	 */
	authButton: {
		padding: 8,
		backgroundColor: "#102e55",
		borderRadius: 5,
		borderWidth: 2,
		borderColor: "#2A5A99",
		transform: [{ scale: 1.0 }],
	},
	authButtonFocused: {
		backgroundColor: "#2A5A99",
		borderColor: "#f9f9f9",
		transform: [{ scale: 1.1 }],
	},
	authButtonText: {
		color: "#f9f9f9",
		fontSize: scale(24),
	},
	authButtonTextFocused: {
		color: "#ffffff",
		fontWeight: "bold",
	},

	/**
	 * Login Button Styles
	 */
	loginButton: {
		paddingVertical: scale(8),
		paddingHorizontal: scale(16),
		backgroundColor: GLOBAL_STYLES.COLORS.ACCENT,
		borderRadius: GLOBAL_STYLES.BORDER_RADIUS,
		borderWidth: scale(2),
		borderColor: GLOBAL_STYLES.COLORS.ACCENT,
		opacity: 0.7,
	},
	logoutButton: {
		backgroundColor: "#4CAF50", // Green background for logout (different from red login)
		borderColor: "#4CAF50",
	},
	loginButtonFocused: {
		opacity: 1,
		borderColor: "#f9f9f9",
		transform: [{ scale: 1.05 }],
	},
	loginButtonText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		fontWeight: "600",
	},
	loginButtonTextFocused: {
		color: "#ffffff",
		fontWeight: "bold",
	},

	/**
	 * Typography Styles
	 */
	error: {
		color: "red",
		textAlign: "center",
		fontSize: scale(24),
	},
});

export default React.memo(HomePage);
