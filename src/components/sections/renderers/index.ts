import AdSection<PERSON>ender<PERSON> from "./AdSectionRenderer";
import StaticCarouselSectionRenderer from "./StaticCarouselSectionRenderer";
import LiveSectionRenderer from "./LiveSectionRenderer";
import DynamicCarouselSectionRenderer from "./DynamicCarouselSectionRenderer";
import DynamicGridWithCategorySectionRenderer from "./DynamicGridWithCategorySectionRenderer";
import ClubsSectionRenderer from "./ClubsSectionRenderer";
import FeaturedVideoSectionRenderer from "./FeaturedVideoSectionRenderer";

/**
 * Export only the section renderers needed for the Competition Page:
 * - StaticCarouselSectionRenderer: For section_static_carousel (featured videos, clubs)
 * - LiveSectionRenderer: For section_dynamic_live (live matches)
 * - DynamicCarouselSectionRenderer: For section_dynamic_carousel (dynamic content like "Débrief & réactions")
 * - DynamicGridWithCategorySectionRenderer: For section_dynamic_grid_with_category (content with category filtering)
 * - AdSectionRenderer: For section_static_ad (advertisements)
 * - ClubsSectionRenderer: For the specialized clubs section
 * - FeaturedVideoSectionRenderer: For the featured video section at the top of competition pages
 */
export {
	AdSectionRenderer,
	StaticCarouselSectionRenderer,
	LiveSectionRenderer,
	DynamicCarouselSectionRenderer,
	DynamicGridWithCategorySectionRenderer,
	ClubsSectionRenderer,
	FeaturedVideoSectionRenderer,
};
