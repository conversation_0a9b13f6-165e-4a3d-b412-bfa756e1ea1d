import React from "react";
import {
	View,
	Text,
	Modal,
	TouchableOpacity,
	StyleSheet,
	Image,
	Pressable,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

interface QRCodeModalProps {
	visible: boolean;
	onClose: () => void;
	title: string;
	subtitle: string;
	qrCodeUrl?: string; // URL that the QR code should point to
	showCancelButton?: boolean;
}

/**
 * QRCodeModal Component
 *
 * Displays a modal with QR code for subscription management.
 * Used in two scenarios:
 * 1. Login page - for account management and subscription
 * 2. Video access denial - for subscription to access paid content
 */
const QRCodeModal: React.FC<QRCodeModalProps> = ({
	visible,
	onClose,
	title,
	subtitle,
	qrCodeUrl = "https://handballtv.fr/subscription", // Default subscription URL
	showCancelButton = true,
}) => {
	// For now, we'll create a visual QR code representation using text
	// In a real implementation, you would use actual QR code images
	const renderQRCodePattern = () => {
		// Simple QR code-like pattern using Unicode characters
		const qrPattern = [
			"██████████████████████████████████████████",
			"██    ██████████████████████████    ██",
			"██ ██ ██                    ██ ██ ██",
			"██ ██ ██  ████████████████  ██ ██ ██",
			"██ ██ ██  ██          ██  ██ ██ ██",
			"██ ██ ██  ██ ████████ ██  ██ ██ ██",
			"██ ██ ██  ██ ██    ██ ██  ██ ██ ██",
			"██ ██ ██  ██ ██ ██ ██ ██  ██ ██ ██",
			"██ ██ ██  ██ ██████ ██ ██  ██ ██ ██",
			"██ ██ ██  ██          ██  ██ ██ ██",
			"██ ██ ██  ████████████████  ██ ██ ██",
			"██ ██ ██                    ██ ██ ██",
			"██    ██████████████████████████    ██",
			"██████████████████████████████████████████",
			"        ██    ██████    ██    ██        ",
			"██████  ██████    ████████  ████  ██████",
			"    ██████  ██████████████████  ██████  ",
			"██████  ████  ██    ██████  ████  ██████",
			"  ██████████████████████████████████████  ",
			"██████████████████████████████████████████",
		];

		return qrPattern.map((line, index) => (
			<Text
				key={index}
				style={styles.qrCodeLine}
			>
				{line}
			</Text>
		));
	};

	return (
		<Modal
			visible={visible}
			transparent={true}
			animationType="fade"
			onRequestClose={onClose}
		>
			<View style={styles.modalOverlay}>
				<View style={styles.modalContainer}>
					{/* Title Section */}
					<View style={styles.titleSection}>
						<Text style={styles.title}>{title}</Text>
						<Text style={styles.subtitle}>{subtitle}</Text>
					</View>

					{/* QR Code Section */}
					<View style={styles.qrCodeSection}>
						<View style={styles.qrCodeContainer}>
							{/* QR Code Pattern */}
							<View style={styles.qrCodePattern}>
								{renderQRCodePattern()}
							</View>

							{/* HandballTV Logo overlay on QR code */}
							<View style={styles.logoOverlay}>
								<Image
									source={require("../../assets/images/logo_HandballTV_header.png")}
									style={styles.logoImage}
									resizeMode="contain"
								/>
							</View>
						</View>
					</View>

					{/* Cancel Button (if enabled) */}
					{showCancelButton && (
						<View style={styles.buttonSection}>
							<Pressable
								style={styles.cancelButton}
								onPress={onClose}
								hasTVPreferredFocus={true}
								focusable={true}
								accessible={true}
								accessibilityRole="button"
								accessibilityLabel="Cancel and close QR code"
							>
								<Text style={styles.cancelButtonText}>CANCEL</Text>
							</Pressable>
						</View>
					)}
				</View>
			</View>
		</Modal>
	);
};

export default QRCodeModal;

const styles = StyleSheet.create({
	modalOverlay: {
		flex: 1,
		backgroundColor: "rgba(0, 0, 0, 0.8)", // Dark overlay
		justifyContent: "center",
		alignItems: "center",
	},
	modalContainer: {
		backgroundColor: GLOBAL_STYLES.COLORS.BACKGROUND_PRIMARY,
		borderRadius: scale(12),
		padding: scale(40),
		alignItems: "center",
		maxWidth: scale(600),
		width: "90%",
	},
	titleSection: {
		alignItems: "center",
		marginBottom: scale(30),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(24),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(10),
		lineHeight: scale(30),
	},
	subtitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(18),
		textAlign: "center",
		lineHeight: scale(24),
		maxWidth: scale(400),
	},
	qrCodeSection: {
		alignItems: "center",
		marginBottom: scale(30),
	},
	qrCodeContainer: {
		position: "relative",
		backgroundColor: "#FFFFFF",
		padding: scale(20),
		borderRadius: scale(8),
		// Add subtle shadow for depth
		shadowColor: "#000",
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.25,
		shadowRadius: 3.84,
		elevation: 5,
	},
	qrCodePattern: {
		alignItems: "center",
		justifyContent: "center",
	},
	qrCodeLine: {
		fontSize: scale(4), // Very small font size to create dense pattern
		lineHeight: scale(4),
		color: "#000000",
		fontFamily: "monospace", // Use monospace font for consistent character spacing
		letterSpacing: -scale(0.5), // Reduce letter spacing for denser pattern
	},
	logoOverlay: {
		position: "absolute",
		top: "50%",
		left: "50%",
		transform: [
			{ translateX: -scale(25) }, // Half of logo width
			{ translateY: -scale(12) }, // Half of logo height
		],
		backgroundColor: "#FFFFFF",
		borderRadius: scale(4),
		padding: scale(4),
		// Add border to make logo stand out on QR code
		borderWidth: 1,
		borderColor: "#E0E0E0",
	},
	logoImage: {
		width: scale(50),
		height: scale(24),
	},
	buttonSection: {
		width: "100%",
		alignItems: "center",
	},
	cancelButton: {
		backgroundColor: "#E61E1E", // Red background matching the design
		paddingHorizontal: scale(40),
		paddingVertical: scale(12),
		borderRadius: scale(8),
		minWidth: scale(200),
		alignItems: "center",
	},
	cancelButtonText: {
		color: "#FFFFFF",
		fontSize: scale(16),
		fontWeight: "bold",
		textAlign: "center",
	},
});
