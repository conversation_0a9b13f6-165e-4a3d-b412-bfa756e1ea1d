{"openapi": "3.0.0", "info": {"title": "Payment-service", "description": "This is the Payment-service OpenAPI 3.0 specification.\n\nSome useful links:\n  - [The source API definition](/docs/spec)", "version": "1.0.0"}, "tags": [{"name": "fans/back-office", "description": "Endpoints to manage fans from the back-office"}, {"name": "fans/platforms", "description": "Endpoints to manage fans from the frontend"}, {"name": "fans/_internal", "description": "Internal endpoint used for the backend"}, {"name": "auth", "description": "Everything about authentication"}], "paths": {"/_internal/{fanId}/cleanup": {"post": {"tags": ["fans/_internal"], "description": "Deletes all products attached to the fan and the related stripe subscriptions if any", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Fan cleaned up successfuly", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageOK"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/deleteFanFromStripe": {"post": {"tags": ["fans/_internal"], "description": "Deletes all products attached to the fan and the related stripe subscriptions if any. It also removes the stripe customer", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Fan cleaned up successfuly", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageOK"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/products": {"post": {"tags": ["fans/_internal"], "description": "Associate a product and a Fan", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"201": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanProduct"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateFanProductRequest"}}}}}, "get": {"tags": ["fans/_internal"], "description": "Get all the related fan products", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/FanProductStatus"}], "responses": {"200": {"description": "Returns the paginated fan products", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanProducts"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/products/{productId}": {"get": {"tags": ["fans/_internal"], "description": "Retrieve a fan product given the productId and the fanId", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The product universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return the fan product", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanProduct"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}, "delete": {"tags": ["fans/_internal"], "description": "Remove a specific fan product given the productId and the fanId", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "productId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The product universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"204": {"description": "Fan product sucessfully deleted"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/cancelStripeOnScheduleDestroy": {"put": {"tags": ["fans/_internal"], "description": "Cancel a stripe subscription at the end of the period", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription scheduled to be cancelled", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageOK"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/stripe-subscription": {"post": {"tags": ["fans/_internal"], "description": "Create a stripe subscription in stripe and attempt a payment, creating the customer if not exists", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionEvent"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/stripe-subscription/{subscriptionId}": {"put": {"tags": ["fans/_internal"], "description": "Cancel a stripe subscription manually", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The stripe subscription unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionEvent"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/_internal/{fanId}/stripe-checkout-session": {"post": {"tags": ["fans/_internal"], "description": "Retrieves a link that can be used to check out a payment", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeSession"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/back-office/fans/{fanId}/products": {"get": {"tags": ["fans/back-office"], "description": "Returns the products attached to a fan", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/FanProductStatus"}], "responses": {"200": {"description": "Returns the paginated fan products", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanProducts"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/back-office/fans/{fanId}/events": {"get": {"tags": ["fans/back-office"], "description": "Returns the products attached to a fan", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}, {"name": "productId", "in": "query", "description": "The product universally unique identifier, used as filter", "schema": {"type": "string"}}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/FanProductStatus"}], "responses": {"200": {"description": "Returns the paginated fan events", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanEvents"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}}}, "/back-office/stripe-subscriptions": {"delete": {"tags": ["fans/back-office"], "description": "Cancel the stripe subscriptions given its ids", "parameters": [{"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelStripeSubscriptionsOutput"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelStripeSubscriptionsRequest"}}}}}}, "/back-office/subscriptions/count": {"get": {"tags": ["fans/back-office"], "description": "Returns the number of current active fan subscriptions", "parameters": [{"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Counter"}}}, "description": "OK"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/back-office/subscriptions/count/new": {"get": {"tags": ["fans/back-office"], "description": "Returns the number of active subscriptions created in the last 7 days", "parameters": [{"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Counter"}}}, "description": "OK"}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/fans/{fanId}/products": {"get": {"tags": ["fans/platforms"], "description": "Returns the products attached to a fan", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/FanProductStatus"}], "responses": {"200": {"description": "Returns the paginated fan products", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanProducts"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/fans/{fanId}/stripe-subscription": {"post": {"tags": ["fans/platforms"], "description": "Create a stripe subscription in stripe and attempt a payment, creating the customer if not exists", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionEvent"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/fans/{fanId}/stripe-subscription/{subscriptionId}": {"put": {"tags": ["fans/platforms"], "description": "Cancel a stripe subscription manually", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "subscriptionId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The stripe subscription unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubscriptionEvent"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/fans/{fanId}/stripe-checkout-session": {"post": {"tags": ["fans/platforms"], "description": "Retrieves a link that can be used to check out a payment", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeSession"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/fans/{fanId}/stripe-customer-portal-session": {"post": {"tags": ["fans/platforms"], "description": "Retrieves a link that can be used access the stripe customer portal", "parameters": [{"name": "fanId", "in": "path", "required": true, "schema": {"type": "string"}, "description": "The fan universally unique identifier"}, {"name": "x-account-key", "in": "header", "description": "The account key if it is not set in query", "schema": {"type": "string"}}, {"name": "accountKey", "in": "query", "description": "The account key if it is not set in headers", "schema": {"type": "string"}}], "responses": {"200": {"description": "Stripe subscription created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StripeSession"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateStripeCustomerPortalSessionRequest"}}}}}}, "/auth/health": {"get": {"tags": ["auth"], "description": "", "parameters": [], "responses": {"200": {"description": "The Service is up and healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusOK"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "An token issued by users-service"}}, "parameters": {"Limit": {"name": "limit", "in": "query", "description": "limit the number of items in the response, allowing getting paginated items", "required": false, "schema": {"type": "integer", "minimum": 1}}, "Before": {"name": "before", "in": "query", "description": "when using limit, allows getting the previous page", "required": false, "schema": {"type": "string"}}, "After": {"name": "after", "in": "query", "description": "when using limit, allows getting the next page", "required": false, "schema": {"type": "string"}}, "FanProductStatus": {"name": "status", "in": "query", "description": "filter fan products by status", "required": false, "schema": {"type": "string", "enum": ["active", "archived"]}}}, "schemas": {"Error": {"type": "string"}, "Exception": {"type": "object", "properties": {"status": {"type": "integer"}, "type": {"type": "string"}, "message": {"type": "string"}}}, "MessageOK": {"type": "object", "properties": {"message": {"type": "string", "enum": ["OK"], "default": "OK"}}}, "StatusOK": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok"], "default": "ok"}}}, "StripeStatus": {"type": "string", "enum": ["incomplete", "incomplete_expired", "trialing", "active", "past_due", "canceled", "unpaid", "paused"], "description": "Stripe subscription status"}, "ProductData": {"type": "object", "properties": {"description": {"type": "string"}, "weight": {"type": "string"}}, "description": "Custom product data"}, "ProductTranslationData": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string"}}, "required": ["name", "description"], "description": "Product translation data"}, "PaymentOfferData": {"type": "object", "properties": {"isLimitedDuration": {"type": "boolean"}, "population": {"type": "string"}, "weight": {"type": "string"}, "type": {"type": "string", "enum": ["one_time", "recurring"]}, "price": {"type": "number"}, "currency": {"type": "string"}, "interval": {"type": "number"}, "intervalCount": {"type": "number"}}, "description": "Custom payment offer data"}, "FanProductData": {"type": "object", "properties": {"fanUsername": {"type": "string"}, "productName": {"type": "string"}, "paymentType": {"type": "string", "enum": ["one_time", "recurring"]}, "paymentDate": {"type": "string", "pattern": "[0-9]{13}"}, "validityDate": {"type": "string", "pattern": "[0-9]{13}"}, "subscription": {"type": "object", "properties": {"subscriptionId": {"type": "string"}, "status": {"$ref": "#/components/schemas/StripeStatus"}, "startDate": {"type": "string", "pattern": "[0-9]{10}"}, "endDate": {"type": "string", "pattern": "[0-9]{10}"}, "periodStartDate": {"type": "string", "pattern": "[0-9]{10}"}, "periodEndDate": {"type": "string", "pattern": "[0-9]{10}"}, "canceled": {"type": "boolean"}, "cancelationDate": {"type": "string", "pattern": "[0-9]{10}"}, "cancelationSource": {"type": "string"}}}}, "description": "Custom fan product data"}, "FanEvent": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["FanEvent"], "default": "FanEvent"}, "accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "timestamp": {"type": "string", "pattern": "[0-9]{13}"}, "productId": {"type": "string", "format": "uuid"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "paymentOfferId": {"type": "string", "format": "uuid"}, "type": {"type": "string"}, "data": {"type": "object", "properties": {"paymentOfferName": {"type": "string"}}}}, "required": ["accountId", "fanId", "timestamp", "productId", "type"], "description": "Describes a Fan event"}, "ItemProductData": {"type": "object", "properties": {"itemName": {"type": "string"}}, "description": "Custom item product data"}, "Product": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["Product"], "default": "Product"}, "accountId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft"}, "name": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductData"}}, "required": ["accountId", "id", "productId", "name"], "description": "Represents a Product Entity"}, "ProductTranslation": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["ProductTranslation"], "default": "ProductTranslation"}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "languageCode": {"type": "string"}, "data": {"$ref": "#/components/schemas/ProductTranslationData"}}, "required": ["accountId", "productId", "languageCode", "data"], "description": "Represents a Translation for a Product"}, "PaymentOffer": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["PaymentOffer"], "default": "PaymentOffer"}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "providerPaymentOfferId": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "data": {"$ref": "#/components/schemas/PaymentOfferData"}, "isPPV": {"type": "boolean"}, "duration": {"type": "number"}, "expiryDate": {"type": "string", "format": "date-time"}}, "required": ["accountId", "productId", "paymentOfferId", "provider", "providerPaymentOfferId", "name"], "description": "Represents a Price of a product"}, "FanProduct": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["FanProduct"], "default": "FanProduct"}, "accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "paymentOfferId": {"type": "string", "format": "uuid"}, "validityDate": {"type": "string", "pattern": "[0-9]{13}"}, "startDate": {"type": "string", "pattern": "[0-9]{10}"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "data": {"$ref": "#/components/schemas/FanProductData"}}, "required": ["accountId", "fanId", "productId"], "description": "Represents a product attached to a fan"}, "ItemProduct": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["ItemProduct"], "default": "ItemProduct"}, "accountId": {"type": "string", "format": "uuid"}, "itemId": {"type": "string", "format": "uuid"}, "itemType": {"type": "string", "enum": ["video", "event"]}, "productId": {"type": "string", "format": "uuid"}, "data": {"$ref": "#/components/schemas/ItemProductData"}}, "required": ["accountId", "itemId", "itemType", "productId"], "description": "Represents a item attached to a product"}, "ProfileProduct": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["ProfileProduct"], "default": "ProfileProduct"}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}}, "required": ["accountId", "profileId", "productId"], "description": "Represents a association between a fan profile and a product"}, "ProfilePaymentOffer": {"type": "object", "properties": {"pk": {"type": "string"}, "sk": {"type": "string"}, "entityType": {"type": "string", "enum": ["ProfilePaymentOffer"], "default": "ProfilePaymentOffer"}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}}, "required": ["accountId", "profileId", "paymentOfferId"], "description": "Represents a association between a fan profile and a payment offer"}, "Cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"], "description": "Contains information related to a paginated entity"}, "PagedFanProducts": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanProduct"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items"], "description": "Represents a paginated collection of FanProduct"}, "PagedFanEvents": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanEvent"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items"], "description": "Represents a paginated collection of FanEvent"}, "CreateFanProductRequest": {"type": "object", "properties": {"productId": {"type": "string"}, "provider": {"type": "string"}, "paymentOfferId": {"type": "string"}, "eventType": {"type": "string"}, "data": {"type": "object"}}, "required": ["productId"], "additionalProperties": false, "description": "Input type required to create a fan product"}, "CreateStripeCustomerPortalSessionRequest": {"type": "object", "properties": {"returnUrl": {"type": "string"}}, "required": ["returnUrl"]}, "SubscriptionEvent": {"type": "object", "properties": {"type": {"type": "string"}, "status": {"type": "string", "enum": ["incomplete", "incomplete_expired", "trialing", "active", "past_due", "canceled", "unpaid", "paused", "succeeded", "requires_payment_method"]}, "message": {"type": "string", "nullable": true}}, "required": ["status", "message"], "description": "The stripe subscription event"}, "StripeSession": {"type": "object", "properties": {"id": {"type": "string"}, "url": {"type": "string", "nullable": true, "format": "uri"}}, "required": ["id", "url"], "description": "Check out session output"}, "CancelStripeSubscriptionsError": {"type": "object", "properties": {"type": {"type": "string"}, "subscriptionId": {"type": "string"}, "message": {"type": "string"}}, "required": ["type", "subscriptionId", "message"], "description": "Defines response for the cancel subscription endpoint"}, "CancelStripeSubscriptionsRequest": {"type": "object", "properties": {"subscriptions": {"type": "array", "items": {"type": "string"}}}, "required": ["subscriptions"], "description": "Cancel stripe subscription input data"}, "CancelStripeSubscriptionsOutput": {"anyOf": [{"$ref": "#/components/schemas/SubscriptionEvent"}, {"type": "array", "items": {"$ref": "#/components/schemas/CancelStripeSubscriptionsError"}}]}, "Counter": {"type": "object", "properties": {"count": {"type": "number"}}, "required": ["count"], "description": "a counter"}}}}