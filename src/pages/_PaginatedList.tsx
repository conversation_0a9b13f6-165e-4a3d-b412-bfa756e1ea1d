import React, { useState, useEffect, useCallback } from "react";
import {
	View,
	FlatList,
	Text,
	ActivityIndicator,
	StyleSheet,
	TouchableOpacity,
	Dimensions,
	Platform,
} from "react-native";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import { VideoCard } from "../utils/apis/generated/kentico";
import { useLanguage } from "../contexts/LanguageContext";

// Screen dimension calculations for responsive scaling
const { width } = Dimensions.get("window");
const baseWidth = 1920;
const scaleFactor = width / baseWidth;

/**
 * Scales sizes based on screen width for responsive design
 * @param size - Original size to scale
 * @returns Scaled size based on current screen width
 */
const scale = (size: number): number => size * scaleFactor;

// API Constants
const CATEGORY_ID = "9a7c2c19-2901-4a6f-906d-a031c9eb28a5";

/**
 * Paginated list component that fetches and displays video cards
 * Supports infinite scrolling with cursor-based pagination
 */
const _PaginatedList_Func = () => {
	// Get current language from context
	const { currentLanguage } = useLanguage();

	// State management for pagination and loading
	const [videos, setVideos] = useState<VideoCard[]>([]);
	const [loading, setLoading] = useState(false);
	const [loadingMore, setLoadingMore] = useState(false);
	const [cursor, setCursor] = useState<string | null>(null);
	const [hasMore, setHasMore] = useState(true);

	// Initial data fetching
	useEffect(() => {
		console.log(
			`[PaginatedList] Component mounted, fetching initial data...`
		);
		fetchInitialData();
	}, [currentLanguage]); // Re-fetch when language changes

	// Monitor videos array changes
	useEffect(() => {
		console.log(
			`[PaginatedList] Current items count: ${videos.length}`
		);
	}, [videos]);

	// Monitor hasMore state changes
	useEffect(() => {
		console.log(`[PaginatedList] hasMore updated: ${hasMore}`);
	}, [hasMore]);

	// Monitor cursor state changes
	useEffect(() => {
		console.log(
			`[PaginatedList] Pagination cursor updated: ${cursor || "null"}`
		);
	}, [cursor]);

	/**
	 * Fetches first page of videos from the category
	 * Initializes pagination state
	 */
	const fetchInitialData = async () => {
		if (loading) {
			console.log(
				`[PaginatedList] Initial fetch already in progress, skipping request`
			);
			return;
		}

		console.log(`[PaginatedList] Starting initial data fetch`);
		setLoading(true);

		try {
			// Fetch initial category videos
			console.log(`[PaginatedList] Calling API with limit: 10`);
			const response = await kenticoAPIClient.ott.getCategoryVideosV2(
				CATEGORY_ID,
				{
					language: currentLanguage,
					limit: 20,
				}
			);

			if ("items" in response.data) {
				const initialItems = response.data.items;
				console.log(
					`[PaginatedList] Initial fetch successful - received ${initialItems.length} items`
				);
				setVideos(initialItems);

				// Set cursor for next page if available
				const initialCursor = response.data.cursor?.after || null;
				setCursor(initialCursor);

				const hasMoreItems = !!response.data.cursor?.after;
				console.log(
					`[PaginatedList] Setting hasMore: ${hasMoreItems}, next cursor: ${
						initialCursor || "null"
					}`
				);
				setHasMore(hasMoreItems); // The "!!" operator converts something to a boolean (true or false)
			} else {
				console.log(
					`[PaginatedList] Response format unexpected:`,
					response.data
				);
			}
		} catch (error) {
			console.error(
				`[PaginatedList] Error loading initial data:`,
				error
			);
		} finally {
			console.log(
				`[PaginatedList] Initial fetch complete, resetting loading state`
			);
			setLoading(false);
			setLoadingMore(false);
		}
	};

	// Memoized load more handler to prevent multiple calls
	const handleLoadMore = useCallback(async () => {
		console.log(`[PaginatedList] handleLoadMore triggered`);
		console.log(
			`[PaginatedList] State check - loadingMore: ${loadingMore}, hasMore: ${hasMore}, cursor: ${
				cursor ? "exists" : "null"
			}, items count: ${videos.length}`
		);

		if (loadingMore || !hasMore || !cursor) {
			console.log(
				`[PaginatedList] Conditions not met for loading more, aborting`
			);
			return;
		}

		console.log(
			`[PaginatedList] Beginning load more operation on platform: ${Platform.OS}, isTV: ${Platform.isTV}`
		);
		setLoadingMore(true);

		try {
			console.log(
				`[PaginatedList] Fetching next page with cursor: ${cursor}`
			);
			const response = await kenticoAPIClient.ott.getCategoryVideosV2(
				CATEGORY_ID,
				{
					language: currentLanguage,
					limit: 10,
					after: cursor,
				}
			);

			if ("items" in response.data) {
				const newItems = response.data.items || [];
				console.log(
					`[PaginatedList] Load more successful - received ${newItems.length} additional items`
				);

				if (newItems.length > 0) {
					setVideos((prevVideos) => {
						const updatedVideos = [...prevVideos, ...newItems];
						console.log(
							`[PaginatedList] Updated videos array - previous: ${prevVideos.length}, new total: ${updatedVideos.length}`
						);
						return updatedVideos;
					});
				}

				const newCursor = response.data.cursor?.after || null;
				console.log(
					`[PaginatedList] Setting new cursor: ${newCursor || "null"}`
				);
				setCursor(newCursor);
				setHasMore(!!newCursor);
			}
		} catch (error) {
			console.error(
				`[PaginatedList] Error loading more data:`,
				error
			);
		} finally {
			setLoadingMore(false);
		}
	}, [loadingMore, hasMore, cursor, videos.length, currentLanguage]);

	// Handle scroll end reached with debounce for tvOS
	const onEndReached = useCallback(() => {
		console.log(
			`[PaginatedList] onEndReached triggered - current count: ${videos.length}, loadingMore: ${loadingMore}, hasMore: ${hasMore}`
		);

		// Prevent loading if we're already loading or at initial load
		if (videos.length === 0 || loadingMore) {
			console.log(
				`[PaginatedList] Skipping load more - initial load or already loading`
			);
			return;
		}

		handleLoadMore();
	}, [videos.length, loadingMore, hasMore, handleLoadMore]);

	/**
	 * Renders individual list item with highlight functionality
	 * Uses consistent opacity pattern from project (0.7 default, 1 on focus)
	 */
	const renderItem = ({ item }: { item: VideoCard }) => (
		<TouchableOpacity
			style={[styles.item, { opacity: 0.7 }]}
			activeOpacity={1}
			onPress={() => {
				/* Handle item selection if needed */
			}}
		>
			<Text style={styles.text}>{item.name}</Text>
		</TouchableOpacity>
	);

	return (
		<View style={styles.container}>
			<FlatList
				// Basic list configuration
				data={videos}
				keyExtractor={(item) => item.itemId}
				renderItem={renderItem}
				// Infinite scroll handling
				onEndReached={onEndReached} // Triggers when user scrolls near list end, used for infinite scrolling
				onEndReachedThreshold={0.1} // Triggers onEndReached when within % (1 to 0 values) of the end (optimized for TV)
				// Performance optimizations for TV platforms
				removeClippedSubviews={true} // Unmounts items that are off screen to reduce memory usage
				maxToRenderPerBatch={5} // Limits number of items rendered per batch to prevent frame drops
				updateCellsBatchingPeriod={50} // Time in ms between batch updates for smoother rendering
				windowSize={21} // Number of items kept mounted for smooth scrolling (21 = 10 screens above/below)
				// TV-specific scroll optimizations
				scrollEventThrottle={16} // Updates every 16ms (60fps) for smooth scrolling
				maintainVisibleContentPosition={{
					minIndexForVisible: 0, // Keeps first visible item stable when list updates
					// autoscrollToTopThreshold: 50, // Auto-scrolls when items are added above visible area
				}}
				// Loading indicator and status messages
				ListFooterComponent={
					loadingMore ? (
						<ActivityIndicator
							size="large"
							color="#f9f9f9"
							style={styles.loadingIndicator}
						/>
					) : hasMore ? (
						<Text style={styles.statusText}>
							{videos.length} items loaded - Scroll for more
						</Text>
					) : (
						<Text style={styles.statusText}>
							End of list - {videos.length} total items
						</Text>
					)
				}
			/>
		</View>
	);
};

export default _PaginatedList_Func;

const styles = StyleSheet.create({
	border: {
		borderWidth: scale(2),
		borderColor: "limegreen",
	},
	container: {
		flex: 1,
		backgroundColor: "#081629",
		padding: scale(24),
	},
	item: {
		padding: 20,
		borderBottomWidth: 1,
		borderBottomColor: "#cccccc",
	},
	text: {
		color: "#f9f9f9",
		fontSize: scale(36),
	},
	statusText: {
		color: "#f9f9f9",
		fontSize: scale(24),
		textAlign: "center",
		padding: scale(20),
		opacity: 0.7,
	},
	loadingIndicator: {
		marginVertical: scale(20),
		alignSelf: "center",
	},
});
