import React from "react";
import {
	View,
	Text,
	Modal,
	Pressable,
	StyleSheet,
	Image,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

interface AccessDeniedModalProps {
	visible: boolean;
	onClose: () => void;
	videoTitle?: string;
}

/**
 * AccessDeniedModal Component
 *
 * Displays a modal when users don't have access to paid content.
 * Shows a QR code for subscription management and a cancel button.
 * This matches the design from the provided image for "YOU DON'T HAVE ACCESS TO THIS VIDEO"
 */
const AccessDeniedModal: React.FC<AccessDeniedModalProps> = ({
	visible,
	onClose,
	videoTitle = "this video",
}) => {
	// QR code image for subscription
	const qrCodeImage = require("../../assets/images/qr-code-subscription.png");

	return (
		<Modal
			visible={visible}
			transparent={true}
			animationType="fade"
			onRequestClose={onClose}
		>
			<View style={styles.modalOverlay}>
				<View style={styles.modalContainer}>
					{/* Title Section */}
					<View style={styles.titleSection}>
						<Text style={styles.title}>
							YOU DON'T HAVE ACCESS{"\n"}TO THIS VIDEO.
						</Text>
						<Text style={styles.subtitle}>
							TO MANAGE THE SUBSCRIPTION{"\n"}PLEASE FOLLOW THE QR
							CODE
						</Text>
					</View>

					{/* QR Code Section */}
					<View style={styles.qrCodeSection}>
						<View style={styles.qrCodeContainer}>
							{/* QR Code Image */}
							<Image
								source={qrCodeImage}
								style={styles.qrCodeImage}
								resizeMode="contain"
							/>
						</View>
					</View>

					{/* Cancel Button */}
					<View style={styles.buttonSection}>
						<Pressable
							style={styles.cancelButton}
							onPress={onClose}
							hasTVPreferredFocus={true}
							focusable={true}
							accessible={true}
							accessibilityRole="button"
							accessibilityLabel="Cancel and go back"
						>
							<Text style={styles.cancelButtonText}>CANCEL</Text>
						</Pressable>
					</View>
				</View>
			</View>
		</Modal>
	);
};

export default AccessDeniedModal;

const styles = StyleSheet.create({
	modalOverlay: {
		flex: 1,
		backgroundColor: "rgba(0, 0, 0, 0.9)", // Darker overlay for better contrast
		justifyContent: "center",
		alignItems: "center",
	},
	modalContainer: {
		backgroundColor: GLOBAL_STYLES.COLORS.BACKGROUND_PRIMARY,
		borderRadius: scale(12),
		padding: scale(50),
		alignItems: "center",
		maxWidth: scale(700),
		width: "90%",
		minHeight: scale(600),
		justifyContent: "space-between",
	},
	titleSection: {
		alignItems: "center",
		marginBottom: scale(40),
	},
	title: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(32),
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: scale(20),
		lineHeight: scale(38),
	},
	subtitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(20),
		textAlign: "center",
		lineHeight: scale(26),
		maxWidth: scale(500),
	},
	qrCodeSection: {
		alignItems: "center",
		marginBottom: scale(40),
	},
	qrCodeContainer: {
		alignItems: "center",
		justifyContent: "center",
		// Remove background and padding since the image has its own background
	},
	qrCodeImage: {
		width: scale(300), // Larger size for better visibility
		height: scale(300),
	},
	buttonSection: {
		width: "100%",
		alignItems: "center",
	},
	cancelButton: {
		backgroundColor: "#E61E1E", // Red background matching the design
		paddingHorizontal: scale(60),
		paddingVertical: scale(16),
		borderRadius: scale(8),
		minWidth: scale(250),
		alignItems: "center",
	},
	cancelButtonText: {
		color: "#FFFFFF",
		fontSize: scale(18),
		fontWeight: "bold",
		textAlign: "center",
	},
});
