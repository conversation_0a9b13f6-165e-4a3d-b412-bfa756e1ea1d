import { NavigationProp } from "@react-navigation/native";
import { RootStackParamList } from "../../app/index";

/**
 * Navigate to the NotFoundPage
 *
 * @param navigation - The navigation object
 */
export const navigateToNotFound = (
	navigation: NavigationProp<RootStackParamList>
) => {
	// Navigate to the NotFoundPage
	navigation.navigate("NotFoundPage");
};

/**
 * Replace the current screen with the NotFoundPage
 * This is useful for handling 404 errors where we want to completely
 * replace the current screen rather than navigate to a new one
 *
 * @param navigation - The navigation object
 */
export const replaceWithNotFound = (
	navigation: NavigationProp<RootStackParamList>
) => {
	// Replace the current screen with the NotFoundPage
	navigation.reset({
		index: 0,
		routes: [
			{
				name: "NotFoundPage",
			},
		],
	});
};

/**
 * Determines if an error is a 404 Not Found error
 *
 * @param error - The error object or message to check
 * @returns boolean indicating if this is a 404 error
 */

export const is404Error = (error: any): boolean => {
	// Check of error contains 404 status code
	if (typeof error === "object") {
		// Check common API error patterns
		return (
			error.status === 404 ||
			error.statusCode === 404 ||
			error.code === 404 ||
			error.response?.status === 404 ||
			(typeof error.message === "string" &&
				error.message.includes("404"))
		);
	}

	// Check if error message string contains 404
	if (typeof error === "string") {
		return (
			error.includes("404") ||
			error.toLowerCase().includes("not found")
		);
	}

	return false;
};
