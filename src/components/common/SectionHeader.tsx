import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";

/**
 * SectionHeader Component
 *
 * A reusable header component for sections that includes:
 * - Title
 * - Optional subtitle
 *
 * @param title - The main title of the section
 * @param subtitle - Optional subtitle to display below the title
 */
interface SectionHeaderProps {
	title: string;
	subtitle?: string;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
	title,
	subtitle,
}) => {
	return (
		<View
			style={styles.container}
			// Make header non-focusable/non-selectable
			importantForAccessibility="no-hide-descendants"
			accessible={false}
		>
			<View style={styles.titleContainer}>
				<Text style={styles.title}>{title}</Text>
				{subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		paddingHorizontal: scale(1),
	},
	titleContainer: {
		flex: 1,
	},
	title: {
		fontSize: scale(36), // Increased font size for better visibility
		fontWeight: "bold",
		color: "#ffffff",
		letterSpacing: scale(0.5),
		textTransform: "uppercase", // Matching the website layout - all section headers are uppercase
	},
	subtitle: {
		fontSize: scale(16),
		color: "rgba(255, 255, 255, 0.7)",
		marginTop: scale(6),
	},
});

export default SectionHeader;
