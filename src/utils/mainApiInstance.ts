import { MainAPI } from "./apis/generated/main_api";

export const mainAPIClient = new MainAPI({
	baseURL: "https://api-gateway.onrewind.tv/main-api",
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"x-account-key": "ByAWCu-i5",
	},
});

export const setMainApiAuthToken = (token: string | null) => {
	mainAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};
