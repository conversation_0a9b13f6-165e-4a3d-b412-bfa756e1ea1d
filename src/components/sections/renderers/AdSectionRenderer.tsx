import React from "react";
import { View, StyleSheet, Dimensions } from "react-native";
import { AdSectionRendererProps } from "../../../types/sectionTypes";
import AdBanner from "../../common/AdBanner";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";
import { scale } from "../../../utils/helpers/dimensionScale.helper";

/**
 * AdSectionRenderer Component
 *
 * Renders an advertisement section using the common AdBanner component
 * Handles both desktop and mobile image variants from Kentico CMS
 * Uses negative margins to extend the banner to full screen width
 */
const AdSectionRenderer: React.FC<AdSectionRendererProps> = ({
	data,
	onPress,
	accessibilityLabel,
}) => {
	// Extract the first image from the array (per Kentico schema)
	const adImage = data.image[0];

	// Calculate screen width for proper sizing
	const screenWidth = Dimensions.get("window").width;

	return (
		<View style={styles.sectionContainer}>
			{/* This wrapper positions the banner to be full width by using negative margins */}
			<View
				style={[
					styles.fullWidthWrapper,
					{
						// Dynamically calculate the width to ensure it's exactly screen width
						width: screenWidth,
						// Position the wrapper to start from the left edge of the screen
						marginLeft: -GLOBAL_STYLES.PAGE_HORIZONTAL_PADDING,
					},
				]}
			>
				<AdBanner
					imageUrl={adImage.image?.url ?? ""}
					mobileImageUrl={adImage.mobileImage?.url}
					redirectionTarget={data.redirectionTarget}
					accessibilityLabel={
						accessibilityLabel ||
						`Advertisement: ${data.redirectionTarget}`
					}
				/>
			</View>
		</View>
	);
};

const styles = StyleSheet.create({
	sectionContainer: {
		width: "100%",
		// Calculate height based on the same ratio used in AdBanner (15% of screen width)
		height: Dimensions.get("window").width * 0.15,
		marginVertical: scale(8),
		marginBottom: scale(42), // add more space before next section
		overflow: "visible", // Allow content to overflow for full-width effect
	},
	fullWidthWrapper: {
		// The position and width are set dynamically in the component
		alignSelf: "flex-start", // Align to the left edge
	},
});

export default React.memo(AdSectionRenderer);
