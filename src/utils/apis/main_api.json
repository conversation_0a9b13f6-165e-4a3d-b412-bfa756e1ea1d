{"openapi": "3.0.0", "info": {"title": "Main-API", "description": "This is the Main-API OpenAPI 3.0 specification.\n\nSome useful links:\n  - [The source API definition](/docs/swagger.json)", "version": "1.0.0"}, "tags": [{"name": "Videos", "description": "Everything related to videos"}, {"name": "Events", "description": "Everything related to events"}], "paths": {"/platforms/videos": {"get": {"tags": ["Videos"], "description": "", "operationId": "getVideos", "parameters": [{"name": "x-account-key", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Video successfully obtained.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/videos/{id}": {"get": {"tags": ["Videos"], "description": "", "operationId": "getVideoById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-account-key", "in": "header", "schema": {"type": "string"}}, {"name": "Cache-Control", "in": "header", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Video successfully obtained.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Video"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/videos/{id}/access": {"get": {"tags": ["Videos"], "description": "", "operationId": "getVideoAccess", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-account-key", "in": "header", "schema": {"type": "string"}}, {"name": "Cache-Control", "in": "header", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Video successfully obtained.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Video"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/events": {"get": {"tags": ["Events"], "description": "", "operationId": "getEvents", "parameters": [{"name": "x-account-key", "in": "header", "schema": {"type": "string"}}], "responses": {"200": {"description": "Events successfully obtained.", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/events/{id}": {"get": {"tags": ["Events"], "description": "", "operationId": "getEventById", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-account-key", "in": "header", "schema": {"type": "string"}}, {"name": "Cache-Control", "in": "header", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Event successfully obtained.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Event"}}}}}, "security": [{"bearerAuth": []}]}}, "/platforms/events/{id}/access": {"get": {"tags": ["Events"], "description": "", "operationId": "getEventAccess", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "x-account-key", "in": "header", "schema": {"type": "string"}}, {"name": "origins-viewer-country", "in": "header", "schema": {"type": "string"}}, {"name": "CloudFront-Viewer-Country", "in": "header", "schema": {"type": "string"}}, {"name": "cloudfront-viewer-country", "in": "header", "schema": {"type": "string"}}, {"name": "x-service-name", "in": "header", "schema": {"type": "string"}}, {"name": "Cache-Control", "in": "header", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "Event successfully obtained.", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/Event"}, {"$ref": "#/components/schemas/GeoBlockItem"}]}}}}}, "security": [{"bearerAuth": []}]}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "An token issued by users-service"}}, "schemas": {"BusinessPlayer": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "organiserName": {"type": "string", "maxLength": 255}, "state": {"type": "string", "nullable": true, "enum": ["liveOn", "liveOff", "replay", "awsLive"]}, "placeholder": {}, "options": {}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "activatedModules": {"type": "array", "nullable": true, "items": {"type": "integer", "format": "int32"}}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private"]}, "tags": {}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "streamIds": {"type": "array", "nullable": true, "items": {"type": "string", "format": "uuid"}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Video": {"$ref": "#/components/schemas/Video"}, "Theme": {"$ref": "#/components/schemas/Theme"}, "Chat": {"$ref": "#/components/schemas/Chat"}, "Chapters": {"type": "array", "items": {"$ref": "#/components/schemas/Chapter"}}, "Jobs": {"type": "array", "items": {"$ref": "#/components/schemas/Job"}}, "Streams": {"type": "array", "items": {"$ref": "#/components/schemas/Stream"}}, "Clips": {"type": "array", "items": {"$ref": "#/components/schemas/Clip"}}}, "required": ["name", "organiserName", "createdAt", "updatedAt"]}, "Category": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "portraitThumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "heroPortrait": {"type": "string", "nullable": true, "maxLength": 255}, "heroLandscape": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategory"}}, "Playlists": {"type": "array", "items": {"$ref": "#/components/schemas/Playlist"}}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "ChallengerTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "shortName": {"type": "string", "nullable": true, "maxLength": 255}, "homeFieldName": {"type": "string", "nullable": true, "maxLength": 255}, "coachName": {"type": "string", "nullable": true, "maxLength": 255}, "role": {"type": "string", "nullable": true, "maxLength": 255}, "history": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "challenger": {"$ref": "#/components/schemas/Challenger"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "Challenger": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "shortName": {"type": "string", "nullable": true, "maxLength": 255}, "homeFieldName": {"type": "string", "nullable": true, "maxLength": 255}, "history": {"type": "string", "nullable": true}, "coachName": {"type": "string", "nullable": true, "maxLength": 255}, "type": {"type": "string", "nullable": true, "enum": ["standard", "team", "teammate"]}, "country": {"type": "string", "nullable": true, "maxLength": 255}, "picture": {"type": "string", "nullable": true, "maxLength": 255}, "pictureUrl": {"type": "string", "nullable": true, "maxLength": 255}, "smallPictureUrl": {"type": "string", "nullable": true, "maxLength": 255}, "jerseyNumber": {"type": "integer", "nullable": true, "format": "int32"}, "gender": {"type": "string", "nullable": true, "enum": ["F", "M", "MIXED"]}, "isJunior": {"type": "boolean", "nullable": true}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "profileOptions": {}, "role": {"type": "string", "nullable": true, "maxLength": 255}, "jerseyPicture": {"type": "string", "nullable": true, "maxLength": 255}, "firstName": {"type": "string", "nullable": true, "maxLength": 255}, "birthday": {"type": "string", "nullable": true, "format": "date-time"}, "statsId": {"type": "integer", "nullable": true, "format": "int32"}, "optaId": {"type": "integer", "nullable": true, "format": "int32"}, "linkShop": {"type": "string", "nullable": true, "maxLength": 255}, "linkStats": {"type": "string", "nullable": true, "maxLength": 255}, "height": {"type": "integer", "nullable": true, "format": "int32"}, "weight": {"type": "integer", "nullable": true, "format": "int32"}, "shots": {"type": "integer", "nullable": true, "format": "int32"}, "targetedShots": {"type": "integer", "nullable": true, "format": "int32"}, "gamesPlayed": {"type": "integer", "nullable": true, "format": "int32"}, "goals": {"type": "integer", "nullable": true, "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengerTranslation"}}, "Teammates": {"type": "array", "items": {"$ref": "#/components/schemas/Challenger"}}, "Team": {"$ref": "#/components/schemas/Challenger"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "Squads": {"type": "array", "items": {"$ref": "#/components/schemas/TeammateSquad"}}, "TeammateSquads": {"type": "array", "items": {"$ref": "#/components/schemas/TeammateSquad"}}, "Sport": {"$ref": "#/components/schemas/Sport"}, "Markers": {"type": "array", "items": {"$ref": "#/components/schemas/Marker"}}, "ChallengerCompetitions": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengerCompetitions"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}}, "required": ["name", "createdAt", "updatedAt"]}, "ChallengerCompetitions": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Season": {"$ref": "#/components/schemas/Season"}, "Competition": {"$ref": "#/components/schemas/Competition"}, "Challenger": {"$ref": "#/components/schemas/Challenger"}}, "required": ["createdAt", "updatedAt"]}, "Chapters_Speakers": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "Chapter": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "time": {"type": "number", "nullable": true, "format": "float"}, "transcript": {"type": "string", "nullable": true}, "level": {"type": "integer", "format": "int32"}, "attachments": {}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "BusinessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "Speakers": {"type": "array", "items": {"$ref": "#/components/schemas/Speaker"}}}, "required": ["level", "createdAt", "updatedAt"]}, "ChatLine": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "content": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Chat": {"$ref": "#/components/schemas/Chat"}, "ChatUser": {"$ref": "#/components/schemas/ChatUser"}}, "required": ["createdAt", "updatedAt"]}, "ChatUser": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "pseudonym": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Chat": {"$ref": "#/components/schemas/Chat"}}, "required": ["pseudonym", "createdAt", "updatedAt"]}, "Chat": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "BusinessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "ChatLines": {"type": "array", "items": {"$ref": "#/components/schemas/ChatLine"}}, "ChatUsers": {"type": "array", "items": {"$ref": "#/components/schemas/ChatUser"}}}, "required": ["createdAt", "updatedAt"]}, "Clip": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "slugName": {"type": "string", "nullable": true, "maxLength": 255}, "poster": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "state": {"type": "string", "nullable": true, "enum": ["none", "error", "ready", "in_progress"]}, "scope": {"type": "string", "nullable": true, "enum": ["customer", "viewer"]}, "url": {"type": "string", "nullable": true, "maxLength": 255}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "UserId": {"type": "string", "nullable": true, "format": "uuid"}, "shareUrl": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Event": {"$ref": "#/components/schemas/Event"}, "BusinessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "ClipSkin": {"$ref": "#/components/schemas/ClipSkin"}, "Jobs": {"type": "array", "items": {"$ref": "#/components/schemas/Job"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}}, "required": ["name", "meta", "createdAt", "updatedAt"]}, "ClipSkin": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "prerollUrl": {"type": "string", "nullable": true, "maxLength": 255}, "postrollUrl": {"type": "string", "nullable": true, "maxLength": 255}, "watermark": {}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["name", "createdAt", "updatedAt"]}, "CompetitionTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "competition": {"$ref": "#/components/schemas/Competition"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "Competition": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "slug": {"type": "string", "nullable": true, "maxLength": 255}, "displayOrder": {"type": "integer", "nullable": true, "format": "int32"}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/CompetitionTranslation"}}, "Rounds": {"type": "array", "items": {"$ref": "#/components/schemas/Round"}}, "ChallengerCompetitions": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengerCompetitions"}}, "Sport": {"$ref": "#/components/schemas/Sport"}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}}, "required": ["name", "createdAt", "updatedAt"]}, "DataProviderMapping": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "dataProviderName": {"type": "string", "enum": ["rugbyunion-api", "opta", "idalgo", "pandaos", "salzburg.opta.f9", "hbs.opta.f9", "asse.opta.f9", "gsports.paok", "beinconnect.opta.f9"]}, "dataProviderStatsId": {"type": "integer", "format": "int32"}, "weight": {"type": "integer", "nullable": true, "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "MarkerType": {"$ref": "#/components/schemas/MarkerType"}}, "required": ["dataProviderName", "dataProviderStatsId", "createdAt", "updatedAt"]}, "EventCategorySubCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Category": {"$ref": "#/components/schemas/Category"}, "SubCategory": {"$ref": "#/components/schemas/SubCategory"}, "Event": {"$ref": "#/components/schemas/Event"}}, "required": ["createdAt", "updatedAt"]}, "EventCategory": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Category": {"$ref": "#/components/schemas/Category"}, "Event": {"$ref": "#/components/schemas/Event"}}, "required": ["createdAt", "updatedAt"]}, "Events_Challengers": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "EventTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "refereeName": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true, "maxLength": 255}, "fullDescription": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "event": {"$ref": "#/components/schemas/Event"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "Event": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "organiserName": {"type": "string", "nullable": true, "maxLength": 255}, "location": {"type": "string", "nullable": true, "maxLength": 255}, "refereeName": {"type": "string", "nullable": true, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "state": {"type": "string", "nullable": true, "enum": ["liveOn", "liveOff", "replay", "liveDailymotion", "liveYoutube", "awsLive", "harmonic"]}, "placeholder": {}, "activatedModules": {"type": "array", "nullable": true, "items": {"type": "integer", "format": "int32"}}, "geoBlockingMapping": {}, "dailymotionLiveStreamId": {"type": "string", "nullable": true, "maxLength": 255}, "youtubeLiveStreamId": {"type": "string", "nullable": true, "maxLength": 255}, "hashtag": {"type": "string", "nullable": true, "maxLength": 255}, "options": {}, "stats": {}, "score": {}, "facebookPlaylistId": {"type": "string", "nullable": true, "maxLength": 255}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private"]}, "ChatId": {"type": "string", "nullable": true, "format": "uuid"}, "shareUrl": {"type": "string", "nullable": true, "maxLength": 255}, "calendarEventId": {"type": "string", "nullable": true, "maxLength": 255}, "streamIds": {"type": "array", "nullable": true, "items": {"type": "string", "format": "uuid"}}, "advertisingBanner": {}, "step": {"type": "string", "nullable": true, "enum": ["starting", "break", "end"]}, "fullDescription": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/EventTranslation"}}, "Sport": {"$ref": "#/components/schemas/Sport"}, "Markers": {"type": "array", "items": {"$ref": "#/components/schemas/Marker"}}, "Video": {"$ref": "#/components/schemas/Video"}, "VideoEvents": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEvent"}}, "Round": {"$ref": "#/components/schemas/Round"}, "Challengers": {"type": "array", "items": {"$ref": "#/components/schemas/Challenger"}}, "TeamClient": {"$ref": "#/components/schemas/Challenger"}, "Theme": {"$ref": "#/components/schemas/Theme"}, "Clips": {"type": "array", "items": {"$ref": "#/components/schemas/Clip"}}, "Jobs": {"type": "array", "items": {"$ref": "#/components/schemas/Job"}}, "EventAttachments": {"type": "array", "items": {"$ref": "#/components/schemas/EventAttachment"}}, "Streams": {"type": "array", "items": {"$ref": "#/components/schemas/Stream"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOffer"}}, "Parent": {"$ref": "#/components/schemas/Event"}, "Children": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategory"}}, "EventCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/EventCategorySubCategory"}}, "EventCategories": {"type": "array", "items": {"$ref": "#/components/schemas/EventCategory"}}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}, "required": ["name", "createdAt", "updatedAt"]}, "EventAttachment": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "type": {"type": "string", "nullable": true, "maxLength": 255}, "fileName": {"type": "string", "nullable": true, "maxLength": 255}, "ext": {"type": "string", "nullable": true, "maxLength": 255}, "url": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["name", "url", "createdAt", "updatedAt"]}, "Job": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "state": {"type": "string", "nullable": true, "enum": ["waiting", "in_progress", "finished", "error", "cancelled"]}, "task": {"type": "string", "enum": ["encoding.tasks.convert", "encoding.tasks.vod_transcode", "encoding.tasks.transmux_to_mp4", "encoding.tasks.live_transcode", "encoding.tasks.extract_from_dvr", "encoding.tasks.transfer_resource_to_s3", "encoding.tasks.transfer_dailymotion_resource_to_s3", "encoding.tasks.concat", "encoding.tasks.simulcast_target", "encoding.tasks.revoke_task", "encoding.tasks.clean_dvr", "encoding.tasks.upload_record", "encoding.tasks.generate_thumbnails", "encoding.tasks.periodic_request", "encoding.tasks.periodic_rabbitmq_message", "encoding.tasks.periodic_sns_event", "encoding.tasks.sns_event", "encoding.tasks.clip", "encoding.tasks.stream_for_clip", "parser.tasks.markers", "parser.tasks.stats", "parser.tasks.match_stats", "trigger.tasks.parser"]}, "resourceType": {"type": "string", "nullable": true, "maxLength": 255}, "resourceId": {"type": "string", "nullable": true, "format": "uuid"}, "workerTaskId": {"type": "string", "format": "uuid"}, "payload": {}, "UserId": {"type": "string", "nullable": true, "format": "uuid"}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Event": {"$ref": "#/components/schemas/Event"}, "BusinessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "Target": {"$ref": "#/components/schemas/Target"}, "Video": {"$ref": "#/components/schemas/Video"}, "Clip": {"$ref": "#/components/schemas/Clip"}}, "required": ["task", "workerTaskId", "createdAt", "updatedAt"]}, "Language": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["name", "createdAt", "updatedAt"]}, "Markers_Children": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "Marker": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "startTime": {"type": "number", "format": "float"}, "endTime": {"type": "number", "format": "float"}, "description": {"type": "string", "nullable": true}, "dailymotionRepostId": {"type": "string", "nullable": true, "maxLength": 255}, "gameTime": {"type": "string", "nullable": true, "maxLength": 255}, "startTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "endTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "notifications": {"type": "array", "nullable": true, "items": {"type": "integer", "format": "int32"}}, "shouldDisplayChallengerProfile": {"type": "boolean", "nullable": true}, "facebookVideoId": {"type": "string", "nullable": true, "maxLength": 255}, "options": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Parent": {"$ref": "#/components/schemas/Marker"}, "Children": {"type": "array", "items": {"$ref": "#/components/schemas/Marker"}}, "MarkerType": {"$ref": "#/components/schemas/MarkerType"}, "Event": {"$ref": "#/components/schemas/Event"}, "Challenger": {"$ref": "#/components/schemas/Challenger"}}, "required": ["startTime", "endTime", "createdAt", "updatedAt"]}, "MarkerTypeTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "event": {"$ref": "#/components/schemas/Marker"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "MarkerType": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "ico": {"type": "string", "nullable": true, "maxLength": 255}, "icoDark": {"type": "string", "nullable": true, "maxLength": 255}, "icoSettings": {"type": "string", "nullable": true, "maxLength": 255}, "shortcut": {"type": "string", "nullable": true, "maxLength": 255}, "defaultTimeStart": {"type": "integer", "nullable": true, "format": "int32"}, "defaultTimeEnd": {"type": "integer", "nullable": true, "format": "int32"}, "optaOffsetStart": {"type": "integer", "nullable": true, "format": "int32"}, "optaOffsetEnd": {"type": "integer", "nullable": true, "format": "int32"}, "startGameMinute": {"type": "integer", "nullable": true, "format": "int32"}, "endGameMinute": {"type": "integer", "nullable": true, "format": "int32"}, "isMain": {"type": "boolean", "nullable": true}, "displayOrder": {"type": "integer", "nullable": true, "format": "int32"}, "pushNotification": {"type": "boolean", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/MarkerTypeTranslation"}}, "ChildrenMarkerTypes": {"type": "array", "items": {"$ref": "#/components/schemas/MarkerType"}}, "Sport": {"$ref": "#/components/schemas/Sport"}, "Markers": {"type": "array", "items": {"$ref": "#/components/schemas/Marker"}}}, "required": ["name", "createdAt", "updatedAt"]}, "RelatedMarkerTypes": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "PaymentOffers_Events": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "PaymentOffers_Tags": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "PaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["PaymentOffer"]}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "providerPaymentOfferId": {"type": "string"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"isLimitedDuration": {"type": "boolean"}, "population": {"type": "string"}, "weight": {"type": "number"}, "currency": {"type": "string"}, "interval": {"type": "string", "enum": ["day", "week", "month", "year"]}, "type": {"type": "string", "enum": ["one_time", "recurring"]}, "price": {"type": "number"}, "intervalCount": {"type": "integer"}}, "required": ["currency", "type", "price"]}, "_type": {"type": "string", "enum": ["PaymentOffer"]}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "duration": {"type": "number"}, "expiryDate": {"type": "string", "format": "date-time"}, "associatedProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/ProfilePaymentOffer"}}, "isPPV": {"type": "boolean"}}, "required": ["_type", "accountId", "entityType", "name", "paymentOfferId", "productId", "provider", "providerPaymentOfferId", "status", "isPPV"]}, "PlayerRoleTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "playerRole": {"$ref": "#/components/schemas/PlayerRole"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "PlayerRole": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/PlayerRoleTranslation"}}, "TeammateSquads": {"type": "array", "items": {"$ref": "#/components/schemas/TeammateSquad"}}, "StatsTypesPlayerRoles": {"type": "array", "items": {"$ref": "#/components/schemas/StatsTypesPlayerRoles"}}, "Sport": {"$ref": "#/components/schemas/Sport"}}, "required": ["name", "createdAt", "updatedAt"]}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "portraitThumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "heroImageDesktop": {"type": "string", "nullable": true, "maxLength": 255}, "heroImageMobile": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "currentVideoPlaylistRank": {"type": "integer", "nullable": true, "format": "int32"}, "newEpisodes": {"type": "boolean", "nullable": true}, "hasBeenViewed": {"type": "boolean", "nullable": true}, "numberOfVideos": {"type": "integer", "nullable": true, "format": "int32"}, "Category": {"$ref": "#/components/schemas/Category"}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "RoundTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "stage": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "round": {"$ref": "#/components/schemas/Round"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "Round": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "competitionOrder": {"type": "integer", "format": "int32"}, "stage": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/RoundTranslation"}}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "Season": {"$ref": "#/components/schemas/Season"}, "Competition": {"$ref": "#/components/schemas/Competition"}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}}, "required": ["name", "competitionOrder", "createdAt", "updatedAt"]}, "SeasonTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "season": {"$ref": "#/components/schemas/Season"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "Season": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "isCurrent": {"type": "boolean", "nullable": true}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/SeasonTranslation"}}, "Rounds": {"type": "array", "items": {"$ref": "#/components/schemas/Round"}}, "ChallengerCompetitions": {"type": "array", "items": {"$ref": "#/components/schemas/ChallengerCompetitions"}}, "Sport": {"$ref": "#/components/schemas/Sport"}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}}, "required": ["name", "createdAt", "updatedAt"]}, "ShortUrl": {"type": "object", "properties": {"shortId": {"type": "string", "nullable": true, "maxLength": 255}, "parentDomain": {"type": "string", "nullable": true, "maxLength": 255}, "consumedCounter": {"type": "integer", "nullable": true, "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Marker": {"$ref": "#/components/schemas/Marker"}, "Chapter": {"$ref": "#/components/schemas/Chapter"}}, "required": ["createdAt", "updatedAt"]}, "Simulcast": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "status": {"type": "string", "enum": ["1", "0"]}, "sourceIp": {"type": "string", "nullable": true, "maxLength": 255}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "streamIds": {"type": "array", "nullable": true, "items": {"type": "string", "format": "uuid"}}, "state": {"type": "string", "enum": ["legacy", "aws"]}, "EventId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Targets": {"type": "array", "items": {"$ref": "#/components/schemas/Target"}}, "Streams": {"type": "array", "items": {"$ref": "#/components/schemas/Stream"}}}, "required": ["name", "status", "state", "createdAt", "updatedAt"]}, "Target": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "platform": {"type": "string", "enum": ["facebook-live", "youtube-live", "dailymotion-live", "periscope-live", "onrewind-live", "rtmp", "rtmps"]}, "status": {"type": "string", "enum": ["2", "1", "0"]}, "streamUrl": {"type": "string", "maxLength": 255}, "streamKey": {"type": "string", "maxLength": 255}, "lastAiringTime": {"type": "string", "nullable": true, "format": "date-time"}, "airingTimeCounter": {"type": "integer", "nullable": true, "format": "int64"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Simulcast": {"$ref": "#/components/schemas/Simulcast"}, "Job": {"$ref": "#/components/schemas/Job"}}, "required": ["platform", "status", "streamUrl", "streamKey", "createdAt", "updatedAt"]}, "Speaker": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "lastName": {"type": "string", "maxLength": 255}, "firstName": {"type": "string", "maxLength": 255}, "politicalParty": {"type": "string", "nullable": true}, "job": {"type": "string", "nullable": true}, "biography": {"type": "string", "nullable": true}, "picture": {"type": "string", "nullable": true, "maxLength": 255}, "link": {"type": "string", "nullable": true, "maxLength": 255}, "twitter": {"type": "string", "nullable": true, "maxLength": 255}, "facebook": {"type": "string", "nullable": true, "maxLength": 255}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Chapters": {"type": "array", "items": {"$ref": "#/components/schemas/Chapter"}}}, "required": ["lastName", "firstName", "createdAt", "updatedAt"]}, "Sport": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "timelineType": {"type": "string", "nullable": true, "enum": ["single", "double"]}, "periods": {"type": "string", "nullable": true, "enum": ["multiple", "none"]}, "svgSpriteFilename": {"type": "string", "nullable": true, "maxLength": 255}, "sportsFieldFilename": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "Challengers": {"type": "array", "items": {"$ref": "#/components/schemas/Challenger"}}, "MarkerTypes": {"type": "array", "items": {"$ref": "#/components/schemas/MarkerType"}}, "Seasons": {"type": "array", "items": {"$ref": "#/components/schemas/Season"}}, "Competitions": {"type": "array", "items": {"$ref": "#/components/schemas/Competition"}}}, "required": ["name", "createdAt", "updatedAt"]}, "TeammateSquad": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "meta": {}, "picture": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Team": {"$ref": "#/components/schemas/Challenger"}, "Season": {"$ref": "#/components/schemas/Season"}, "Teammate": {"$ref": "#/components/schemas/Challenger"}, "Role": {"$ref": "#/components/schemas/PlayerRole"}}, "required": ["createdAt", "updatedAt"]}, "StatsTypeTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "statsType": {"$ref": "#/components/schemas/StatsType"}, "language": {"$ref": "#/components/schemas/Language"}}, "required": ["name", "createdAt", "updatedAt"]}, "StatsType": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "providerId": {"type": "string", "maxLength": 255}, "displayOrder": {"type": "integer", "nullable": true, "format": "int32"}, "type": {"type": "string", "maxLength": 255}, "aggregation": {"type": "string", "nullable": true, "enum": ["sum", "mean"]}, "AccountId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/StatsTypeTranslation"}}, "StatsTypesPlayerRoles": {"type": "array", "items": {"$ref": "#/components/schemas/StatsTypesPlayerRoles"}}}, "required": ["name", "providerId", "type", "AccountId", "createdAt", "updatedAt"]}, "StatsTypesPlayerRoles": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "StatsType": {"$ref": "#/components/schemas/StatsType"}, "PlayerRole": {"$ref": "#/components/schemas/PlayerRole"}}, "required": ["createdAt", "updatedAt"]}, "Stream": {"oneOf": [{"$ref": "#/components/schemas/MainApiStream"}, {"$ref": "#/components/schemas/AWSStream"}]}, "SubCategoryCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "SubCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "VideoCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategory"}}, "EventCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/EventCategorySubCategory"}}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "ItemTag": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "taggable": {"type": "string", "maxLength": 255}, "taggableId": {"type": "string", "format": "uuid"}, "Event": {"$ref": "#/components/schemas/Event"}, "Tag": {"$ref": "#/components/schemas/Tag"}, "Video": {"$ref": "#/components/schemas/Video"}, "Season": {"$ref": "#/components/schemas/Season"}, "Competition": {"$ref": "#/components/schemas/Competition"}, "Round": {"$ref": "#/components/schemas/Round"}, "Challenger": {"$ref": "#/components/schemas/Challenger"}}, "required": ["taggable", "taggableId"]}, "Tag": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "tagType": {"type": "string", "nullable": true, "maxLength": 255}, "name": {"type": "string", "maxLength": 255}, "options": {}, "AccountId": {"type": "string", "format": "uuid"}, "placeholders": {"type": "array", "nullable": true, "items": {"type": "string", "maxLength": 255}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "Clips": {"type": "array", "items": {"$ref": "#/components/schemas/Clip"}}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}, "Seasons": {"type": "array", "items": {"$ref": "#/components/schemas/Season"}}, "Competitions": {"type": "array", "items": {"$ref": "#/components/schemas/Competition"}}, "Rounds": {"type": "array", "items": {"$ref": "#/components/schemas/Round"}}, "Challengers": {"type": "array", "items": {"$ref": "#/components/schemas/Challenger"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOffer"}}}, "required": ["name", "AccountId", "createdAt", "updatedAt"]}, "Theme": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "style": {}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private"]}, "customMarkerTypes": {}, "customMarkerTypesSvgSprite": {"type": "string", "nullable": true, "maxLength": 255}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/Event"}}, "BusinessPlayers": {"type": "array", "items": {"$ref": "#/components/schemas/BusinessPlayer"}}}, "required": ["name", "style", "createdAt", "updatedAt"]}, "VideoCategorySubCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Category": {"$ref": "#/components/schemas/Category"}, "SubCategory": {"$ref": "#/components/schemas/SubCategory"}, "Video": {"$ref": "#/components/schemas/Video"}}, "required": ["createdAt", "updatedAt"]}, "VideoCategory": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Category": {"$ref": "#/components/schemas/Category"}, "Video": {"$ref": "#/components/schemas/Video"}}, "required": ["createdAt", "updatedAt"]}, "VideoEvent": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Video": {"$ref": "#/components/schemas/Video"}, "Event": {"$ref": "#/components/schemas/Event"}}, "required": ["createdAt", "updatedAt"]}, "VideoTranslation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "language": {"$ref": "#/components/schemas/Language"}, "video": {"$ref": "#/components/schemas/Video"}}, "required": ["name", "createdAt", "updatedAt"]}, "Video": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "filename": {"type": "string", "nullable": true, "maxLength": 255}, "description": {"type": "string", "nullable": true}, "duration": {"type": "number", "nullable": true, "format": "float"}, "poster": {"type": "string", "nullable": true, "maxLength": 255}, "portraitThumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "url": {"type": "string", "nullable": true, "maxLength": 255}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private"]}, "captions": {"type": "array", "nullable": true, "items": {}}, "status": {"type": "string", "nullable": true, "enum": ["none", "original", "in_progress", "encoded", "archived", "vendor"]}, "archiveData": {}, "vendorName": {"type": "string", "nullable": true, "enum": ["jwplayer", "awsplayer", "dailymotion", "youtube", "twitch"]}, "vendorVideoId": {"type": "string", "nullable": true, "maxLength": 255}, "vendorApiKey": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "fullDescription": {"type": "string", "nullable": true}, "technicalDescription": {"type": "string", "nullable": true}, "playlistRank": {"type": "integer", "nullable": true, "format": "int32"}, "liveSource": {"type": "boolean"}, "urlMp4": {"type": "string", "nullable": true}, "hasBeenViewed": {"type": "boolean", "nullable": true}, "isNew": {"type": "boolean", "nullable": true}, "marker": {"type": "number", "nullable": true}, "currentFanViews": {"type": "integer", "nullable": true, "format": "int32"}, "views": {"type": "integer", "nullable": true, "format": "int32"}, "type": {"type": "string", "nullable": true, "enum": ["360", "additionnal"]}, "geoBlockingMapping": {}, "ratio": {"type": "string", "enum": ["sixteen-nine", "nine-sixteen"]}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Event": {"$ref": "#/components/schemas/Event"}, "BusinessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "Playlist": {"$ref": "#/components/schemas/Playlist"}, "Jobs": {"type": "array", "items": {"$ref": "#/components/schemas/Job"}}, "translations": {"type": "array", "items": {"$ref": "#/components/schemas/VideoTranslation"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "ItemTags": {"type": "array", "items": {"$ref": "#/components/schemas/ItemTag"}}, "VideoEvents": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEvent"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategory"}}, "VideoCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategory"}}, "VideoCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategory"}}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}, "required": ["name", "liveSource", "ratio", "createdAt", "updatedAt"]}, "Product": {"type": "object", "properties": {"_type": {"type": "string", "enum": ["Product"], "default": "Product"}, "accountId": {"type": "string", "format": "uuid"}, "data": {"type": "object", "properties": {"description": {"type": "string"}, "weight": {"type": "number"}}}, "entityType": {"type": "string", "enum": ["Product"], "default": "Product"}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "productId": {"type": "string", "format": "uuid"}, "isStandard": {"type": "boolean"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "paymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOffer"}}}, "required": ["_type", "accountId", "entityType", "id", "paymentOffers", "productId", "status"]}, "ProfileProduct": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["ProfileProduct"]}, "_type": {"type": "string", "enum": ["ProfileProduct"]}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "data": {"type": "object"}}, "required": ["entityType", "accountId", "profileId", "productId"]}, "ProfilePaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["ProfilePaymentOffer"]}, "_type": {"type": "string", "enum": ["ProfilePaymentOffer"]}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "data": {"type": "object"}}, "required": ["entityType", "accountId", "profileId", "paymentOfferId"]}, "GeoBlockItem": {"type": "object", "properties": {"status": {"type": "integer"}, "message": {"type": "string"}, "code": {"type": "string"}}, "required": ["status"]}, "MainApiStream": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "streamType": {"type": "string", "nullable": true, "enum": ["main", "backup", "additionnal"]}, "key": {"type": "string", "maxLength": 255}, "token": {"type": "string", "maxLength": 255}, "recordName": {"type": "string", "nullable": true, "maxLength": 255}, "isRecordReady": {"type": "boolean"}, "startedAt": {"type": "string", "nullable": true, "format": "date-time"}, "offset": {"type": "integer", "nullable": true, "format": "int32"}, "name": {"type": "string", "nullable": true, "maxLength": 255}, "mapCoordinates": {}, "url": {"type": "string", "nullable": true, "maxLength": 255}, "options": {}, "streamable": {"type": "string", "nullable": true, "maxLength": 255}, "streamableId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "event": {"$ref": "#/components/schemas/Event"}, "businessPlayer": {"$ref": "#/components/schemas/BusinessPlayer"}, "simulcast": {"$ref": "#/components/schemas/Simulcast"}}, "required": ["key", "token", "isRecordReady", "createdAt", "updatedAt"]}, "AWSStream": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "url": {"type": "string", "nullable": true}, "streamType": {"type": "string", "nullable": true}, "awsStream": {"type": "string", "enum": ["STANDBY", "ONAIR", "ARCHIVED"]}}, "required": ["awsStream"]}}}}