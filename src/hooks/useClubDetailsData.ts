import { useState, useEffect } from "react";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import {
	PageLayout,
	SectionContent,
} from "../utils/apis/generated/kentico";

interface ClubDetailsData {
	pageData: PageLayout | null;
	sections: SectionContent[];
	isLoading: boolean;
	error: string | null;
}

/**
 * Custom hook for fetching club details data
 *
 * This hook fetches the club details page data from the Kentico API
 * and returns the page data, sections, loading state, and error state.
 *
 * @param clubId - The ID of the club to fetch
 * @param clubCodename - The codename of the club to fetch (e.g., "club_pays_d_aix_universite_club")
 * @param language - The language code for content (default: 'en')
 * @returns Object containing pageData, sections, loading state, and error state
 */
export const useClubDetailsData = (
	clubId: string,
	clubCodename?: string,
	language: string = "en"
): ClubDetailsData => {
	const [pageData, setPageData] = useState<PageLayout | null>(null);
	const [sections, setSections] = useState<SectionContent[]>([]);
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		// Reset state when clubId changes
		setIsLoading(true);
		setError(null);
		setPageData(null);
		setSections([]);

		// We don't need to check for clubId since it's always provided from the competition page
		// The real check is for clubCodename, which we do in the fetchData function

		const fetchData = async () => {
			try {
				if (!clubCodename) {
					// If no club codename is provided, set an error and return early
					console.error(
						`[ClubDetailsPage] No club codename provided for club ID: ${clubId}`
					);
					setError("This club is unavailable or doesn't exist");
					setIsLoading(false);
					return;
				}

				// Use the club codename provided from navigation params
				console.log(
					`[ClubDetailsPage] Using club codename from navigation params: ${clubCodename}`
				);

				console.log(
					`[ClubDetailsPage] Fetching club details for ID: ${clubId}`
				);

				// Log detailed information about the club request
				// This matches the curl command:
				// curl 'https://api-gateway.onrewind.tv/cms-service-api/ott/kentico/pages/club_pays_d_aix_universite_club?language=en&previewFlag=false' \
				// The difference is that our base URL is 'https://cms-service.onrewind.tv' and the path is slightly different
				console.log(`[ClubDetailsPage] Club Details Request:`, {
					clubId,
					clubCodename,
					endpoint: "getPage",
					language,
					previewFlag: false,
					apiUrl: `https://cms-service.onrewind.tv/ott/kentico/pages/${clubCodename}?language=${language}`,
					// The equivalent curl URL would be:
					curlUrl: `https://api-gateway.onrewind.tv/cms-service-api/ott/kentico/pages/${clubCodename}?language=${language}&previewFlag=false`,
				});

				// Try to fetch the club details using the club codename directly
				const response = await kenticoAPIClient.ott.getPage(
					clubCodename,
					{
						language,
						previewFlag: false,
					}
				);

				// If we get here, the request was successful
				// Set page data and sections
				setPageData(response.data);
				setSections(response.data.components || []);

				console.log(
					`[ClubDetailsPage] Successfully fetched club details with ${
						response.data.components?.length || 0
					} sections`
				);

				// End of try block
			} catch (err: any) {
				console.error(
					"[ClubDetailsPage] Error fetching club details:",
					err
				);

				// Set a generic error message for all errors
				setError(
					"Failed to load club details. Please try again later."
				);

				// Log detailed information about the error
				console.log("[ClubDetailsPage] Error Details:", {
					clubId,
					clubCodename,
					errorStatus: err.response?.status,
					errorMessage: err.message,
					errorData: err.response?.data,
				});
			} finally {
				setIsLoading(false);
			}
		};

		fetchData();
	}, [clubId, clubCodename, language]); // Re-fetch when clubId, clubCodename, or language changes

	return {
		pageData,
		sections,
		isLoading,
		error,
	};
};
