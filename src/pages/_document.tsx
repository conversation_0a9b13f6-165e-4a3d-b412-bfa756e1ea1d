import Document, {
	Html,
	Head,
	Main,
	NextScript,
} from "next/document";
import React from "react";

// This file allows you to customize the HTML document structure that wraps your Next.js application.
// This is useful for adding global elements that should be present on every page, such as meta tags, links to stylesheets, or scripts.
class MyDocument extends Document {
	render() {
		return (
			<Html lang="en">
				<Head>
					<meta name="description" />
					<link
						rel="icon"
						href="/images/favicon.ico"
						sizes="any"
					/>
				</Head>
				<body>
					<Main />
					<NextScript />
				</body>
			</Html>
		);
	}
}

export default MyDocument;
