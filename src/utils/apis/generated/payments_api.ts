/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type Error = string;

export interface Exception {
  status?: number;
  type?: string;
  message?: string;
}

export interface MessageOK {
  /** @default "OK" */
  message?: "OK";
}

export interface StatusOK {
  /** @default "ok" */
  status?: "ok";
}

/** Stripe subscription status */
export enum StripeStatus {
  Incomplete = "incomplete",
  IncompleteExpired = "incomplete_expired",
  Trialing = "trialing",
  Active = "active",
  PastDue = "past_due",
  Canceled = "canceled",
  Unpaid = "unpaid",
  Paused = "paused",
}

/** Custom product data */
export interface ProductData {
  description?: string;
  weight?: string;
}

/** Product translation data */
export interface ProductTranslationData {
  name: string;
  description: string;
}

/** Custom payment offer data */
export interface PaymentOfferData {
  isLimitedDuration?: boolean;
  population?: string;
  weight?: string;
  type?: "one_time" | "recurring";
  price?: number;
  currency?: string;
  interval?: number;
  intervalCount?: number;
}

/** Custom fan product data */
export interface FanProductData {
  fanUsername?: string;
  productName?: string;
  paymentType?: "one_time" | "recurring";
  /** @pattern [0-9]{13} */
  paymentDate?: string;
  /** @pattern [0-9]{13} */
  validityDate?: string;
  subscription?: {
    subscriptionId?: string;
    /** Stripe subscription status */
    status?: StripeStatus;
    /** @pattern [0-9]{10} */
    startDate?: string;
    /** @pattern [0-9]{10} */
    endDate?: string;
    /** @pattern [0-9]{10} */
    periodStartDate?: string;
    /** @pattern [0-9]{10} */
    periodEndDate?: string;
    canceled?: boolean;
    /** @pattern [0-9]{10} */
    cancelationDate?: string;
    cancelationSource?: string;
  };
}

/** Describes a Fan event */
export interface FanEvent {
  pk?: string;
  sk?: string;
  /** @default "FanEvent" */
  entityType?: "FanEvent";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  fanId: string;
  /** @pattern [0-9]{13} */
  timestamp: string;
  /** @format uuid */
  productId: string;
  provider?: "stripe" | "google" | "apple";
  /** @format uuid */
  paymentOfferId?: string;
  type: string;
  data?: {
    paymentOfferName?: string;
  };
}

/** Custom item product data */
export interface ItemProductData {
  itemName?: string;
}

/** Represents a Product Entity */
export interface Product {
  pk?: string;
  sk?: string;
  /** @default "Product" */
  entityType?: "Product";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  id: string;
  /** @format uuid */
  productId: string;
  /** @default "draft" */
  status?: "draft" | "published" | "archived";
  name: string;
  /** Custom product data */
  data?: ProductData;
}

/** Represents a Translation for a Product */
export interface ProductTranslation {
  pk?: string;
  sk?: string;
  /** @default "ProductTranslation" */
  entityType?: "ProductTranslation";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  productId: string;
  languageCode: string;
  /** Product translation data */
  data: ProductTranslationData;
}

/** Represents a Price of a product */
export interface PaymentOffer {
  pk?: string;
  sk?: string;
  /** @default "PaymentOffer" */
  entityType?: "PaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  productId: string;
  /** @format uuid */
  paymentOfferId: string;
  /** @default "active" */
  status?: "active" | "archived";
  provider: "stripe" | "google" | "apple";
  /** @format uuid */
  providerPaymentOfferId: string;
  name: string;
  /** Custom payment offer data */
  data?: PaymentOfferData;
  isPPV?: boolean;
  duration?: number;
  /** @format date-time */
  expiryDate?: string;
}

/** Represents a product attached to a fan */
export interface FanProduct {
  pk?: string;
  sk?: string;
  /** @default "FanProduct" */
  entityType?: "FanProduct";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  fanId: string;
  /** @format uuid */
  productId: string;
  provider?: "stripe" | "google" | "apple";
  /** @format uuid */
  paymentOfferId?: string;
  /** @pattern [0-9]{13} */
  validityDate?: string;
  /** @pattern [0-9]{10} */
  startDate?: string;
  /** @default "active" */
  status?: "active" | "archived";
  /** Custom fan product data */
  data?: FanProductData;
}

/** Represents a item attached to a product */
export interface ItemProduct {
  pk?: string;
  sk?: string;
  /** @default "ItemProduct" */
  entityType?: "ItemProduct";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  itemId: string;
  itemType: "video" | "event";
  /** @format uuid */
  productId: string;
  /** Custom item product data */
  data?: ItemProductData;
}

/** Represents a association between a fan profile and a product */
export interface ProfileProduct {
  pk?: string;
  sk?: string;
  /** @default "ProfileProduct" */
  entityType?: "ProfileProduct";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  productId: string;
}

/** Represents a association between a fan profile and a payment offer */
export interface ProfilePaymentOffer {
  pk?: string;
  sk?: string;
  /** @default "ProfilePaymentOffer" */
  entityType?: "ProfilePaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  paymentOfferId: string;
}

/** Contains information related to a paginated entity */
export interface Cursor {
  before: string | null;
  after: string | null;
}

/** Represents a paginated collection of FanProduct */
export interface PagedFanProducts {
  items: FanProduct[];
  /** Contains information related to a paginated entity */
  cursor?: Cursor;
}

/** Represents a paginated collection of FanEvent */
export interface PagedFanEvents {
  items: FanEvent[];
  /** Contains information related to a paginated entity */
  cursor?: Cursor;
}

/** Input type required to create a fan product */
export interface CreateFanProductRequest {
  productId: string;
  provider?: string;
  paymentOfferId?: string;
  eventType?: string;
  data?: object;
}

export interface CreateStripeCustomerPortalSessionRequest {
  returnUrl: string;
}

/** The stripe subscription event */
export interface SubscriptionEvent {
  type?: string;
  status:
    | "incomplete"
    | "incomplete_expired"
    | "trialing"
    | "active"
    | "past_due"
    | "canceled"
    | "unpaid"
    | "paused"
    | "succeeded"
    | "requires_payment_method";
  message: string | null;
}

/** Check out session output */
export interface StripeSession {
  id: string;
  /** @format uri */
  url: string | null;
}

/** Defines response for the cancel subscription endpoint */
export interface CancelStripeSubscriptionsError {
  type: string;
  subscriptionId: string;
  message: string;
}

/** Cancel stripe subscription input data */
export interface CancelStripeSubscriptionsRequest {
  subscriptions: string[];
}

export type CancelStripeSubscriptionsOutput = SubscriptionEvent | CancelStripeSubscriptionsError[];

/** a counter */
export interface Counter {
  count: number;
}

import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, HeadersDefaults, ResponseType } from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, "body" | "method" | "query" | "path">;

export interface ApiConfig<SecurityDataType = unknown> extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({ securityWorker, secure, format, ...axiosConfig }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: axiosConfig.baseURL || "" });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(params1: AxiosRequestConfig, params2?: AxiosRequestConfig): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method && this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === "object") {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== "string") {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Payment-service
 * @version 1.0.0
 *
 * This is the Payment-service OpenAPI 3.0 specification.
 *
 * Some useful links:
 *   - [The source API definition](/docs/spec)
 */
export class PaymentsAPI<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  internal = {
    /**
     * @description Deletes all products attached to the fan and the related stripe subscriptions if any
     *
     * @tags fans/_internal
     * @name CleanupCreate
     * @request POST:/_internal/{fanId}/cleanup
     */
    cleanupCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<MessageOK, Exception>({
        path: `/_internal/${fanId}/cleanup`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Deletes all products attached to the fan and the related stripe subscriptions if any. It also removes the stripe customer
     *
     * @tags fans/_internal
     * @name DeleteFanFromStripeCreate
     * @request POST:/_internal/{fanId}/deleteFanFromStripe
     */
    deleteFanFromStripeCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<MessageOK, Exception>({
        path: `/_internal/${fanId}/deleteFanFromStripe`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Associate a product and a Fan
     *
     * @tags fans/_internal
     * @name ProductsCreate
     * @request POST:/_internal/{fanId}/products
     */
    productsCreate: (
      fanId: string,
      data: CreateFanProductRequest,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanProduct, Exception>({
        path: `/_internal/${fanId}/products`,
        method: "POST",
        query: query,
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Get all the related fan products
     *
     * @tags fans/_internal
     * @name ProductsDetail
     * @request GET:/_internal/{fanId}/products
     */
    productsDetail: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
        /**
         * limit the number of items in the response, allowing getting paginated items
         * @min 1
         */
        limit?: number;
        /** when using limit, allows getting the previous page */
        before?: string;
        /** when using limit, allows getting the next page */
        after?: string;
        /** filter fan products by status */
        status?: "active" | "archived";
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanProducts, Exception>({
        path: `/_internal/${fanId}/products`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieve a fan product given the productId and the fanId
     *
     * @tags fans/_internal
     * @name ProductsDetail2
     * @request GET:/_internal/{fanId}/products/{productId}
     * @originalName productsDetail
     * @duplicate
     */
    productsDetail2: (
      fanId: string,
      productId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanProduct, Exception>({
        path: `/_internal/${fanId}/products/${productId}`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Remove a specific fan product given the productId and the fanId
     *
     * @tags fans/_internal
     * @name ProductsDelete
     * @request DELETE:/_internal/{fanId}/products/{productId}
     */
    productsDelete: (
      fanId: string,
      productId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<void, Exception>({
        path: `/_internal/${fanId}/products/${productId}`,
        method: "DELETE",
        query: query,
        ...params,
      }),

    /**
     * @description Cancel a stripe subscription at the end of the period
     *
     * @tags fans/_internal
     * @name CancelStripeOnScheduleDestroyUpdate
     * @request PUT:/_internal/{fanId}/cancelStripeOnScheduleDestroy
     */
    cancelStripeOnScheduleDestroyUpdate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<MessageOK, Exception>({
        path: `/_internal/${fanId}/cancelStripeOnScheduleDestroy`,
        method: "PUT",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a stripe subscription in stripe and attempt a payment, creating the customer if not exists
     *
     * @tags fans/_internal
     * @name StripeSubscriptionCreate
     * @request POST:/_internal/{fanId}/stripe-subscription
     */
    stripeSubscriptionCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<SubscriptionEvent, Exception>({
        path: `/_internal/${fanId}/stripe-subscription`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Cancel a stripe subscription manually
     *
     * @tags fans/_internal
     * @name StripeSubscriptionUpdate
     * @request PUT:/_internal/{fanId}/stripe-subscription/{subscriptionId}
     */
    stripeSubscriptionUpdate: (
      fanId: string,
      subscriptionId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<SubscriptionEvent, Exception>({
        path: `/_internal/${fanId}/stripe-subscription/${subscriptionId}`,
        method: "PUT",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves a link that can be used to check out a payment
     *
     * @tags fans/_internal
     * @name StripeCheckoutSessionCreate
     * @request POST:/_internal/{fanId}/stripe-checkout-session
     */
    stripeCheckoutSessionCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<StripeSession, Exception>({
        path: `/_internal/${fanId}/stripe-checkout-session`,
        method: "POST",
        query: query,
        format: "json",
        ...params,
      }),
  };
  backOffice = {
    /**
     * @description Returns the products attached to a fan
     *
     * @tags fans/back-office
     * @name FansProductsDetail
     * @request GET:/back-office/fans/{fanId}/products
     */
    fansProductsDetail: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
        /**
         * limit the number of items in the response, allowing getting paginated items
         * @min 1
         */
        limit?: number;
        /** when using limit, allows getting the previous page */
        before?: string;
        /** when using limit, allows getting the next page */
        after?: string;
        /** filter fan products by status */
        status?: "active" | "archived";
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanProducts, Exception>({
        path: `/back-office/fans/${fanId}/products`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns the products attached to a fan
     *
     * @tags fans/back-office
     * @name FansEventsDetail
     * @request GET:/back-office/fans/{fanId}/events
     */
    fansEventsDetail: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
        /** The product universally unique identifier, used as filter */
        productId?: string;
        /**
         * limit the number of items in the response, allowing getting paginated items
         * @min 1
         */
        limit?: number;
        /** when using limit, allows getting the previous page */
        before?: string;
        /** when using limit, allows getting the next page */
        after?: string;
        /** filter fan products by status */
        status?: "active" | "archived";
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanEvents, Exception>({
        path: `/back-office/fans/${fanId}/events`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description Cancel the stripe subscriptions given its ids
     *
     * @tags fans/back-office
     * @name StripeSubscriptionsDelete
     * @request DELETE:/back-office/stripe-subscriptions
     * @secure
     */
    stripeSubscriptionsDelete: (
      data: CancelStripeSubscriptionsRequest,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<CancelStripeSubscriptionsOutput, Exception>({
        path: `/back-office/stripe-subscriptions`,
        method: "DELETE",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns the number of current active fan subscriptions
     *
     * @tags fans/back-office
     * @name SubscriptionsCountList
     * @request GET:/back-office/subscriptions/count
     * @secure
     */
    subscriptionsCountList: (
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Counter, Exception>({
        path: `/back-office/subscriptions/count`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Returns the number of active subscriptions created in the last 7 days
     *
     * @tags fans/back-office
     * @name SubscriptionsCountNewList
     * @request GET:/back-office/subscriptions/count/new
     * @secure
     */
    subscriptionsCountNewList: (
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Counter, Exception>({
        path: `/back-office/subscriptions/count/new`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),
  };
  platforms = {
    /**
     * @description Returns the products attached to a fan
     *
     * @tags fans/platforms
     * @name FansProductsDetail
     * @request GET:/platforms/fans/{fanId}/products
     * @secure
     */
    fansProductsDetail: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
        /**
         * limit the number of items in the response, allowing getting paginated items
         * @min 1
         */
        limit?: number;
        /** when using limit, allows getting the previous page */
        before?: string;
        /** when using limit, allows getting the next page */
        after?: string;
        /** filter fan products by status */
        status?: "active" | "archived";
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanProducts, Exception>({
        path: `/platforms/fans/${fanId}/products`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Create a stripe subscription in stripe and attempt a payment, creating the customer if not exists
     *
     * @tags fans/platforms
     * @name FansStripeSubscriptionCreate
     * @request POST:/platforms/fans/{fanId}/stripe-subscription
     * @secure
     */
    fansStripeSubscriptionCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<SubscriptionEvent, Exception>({
        path: `/platforms/fans/${fanId}/stripe-subscription`,
        method: "POST",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Cancel a stripe subscription manually
     *
     * @tags fans/platforms
     * @name FansStripeSubscriptionUpdate
     * @request PUT:/platforms/fans/{fanId}/stripe-subscription/{subscriptionId}
     * @secure
     */
    fansStripeSubscriptionUpdate: (
      fanId: string,
      subscriptionId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<SubscriptionEvent, Exception>({
        path: `/platforms/fans/${fanId}/stripe-subscription/${subscriptionId}`,
        method: "PUT",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves a link that can be used to check out a payment
     *
     * @tags fans/platforms
     * @name FansStripeCheckoutSessionCreate
     * @request POST:/platforms/fans/{fanId}/stripe-checkout-session
     * @secure
     */
    fansStripeCheckoutSessionCreate: (
      fanId: string,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<StripeSession, Exception>({
        path: `/platforms/fans/${fanId}/stripe-checkout-session`,
        method: "POST",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description Retrieves a link that can be used access the stripe customer portal
     *
     * @tags fans/platforms
     * @name FansStripeCustomerPortalSessionCreate
     * @request POST:/platforms/fans/{fanId}/stripe-customer-portal-session
     * @secure
     */
    fansStripeCustomerPortalSessionCreate: (
      fanId: string,
      data: CreateStripeCustomerPortalSessionRequest,
      query?: {
        /** The account key if it is not set in headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<StripeSession, Exception>({
        path: `/platforms/fans/${fanId}/stripe-customer-portal-session`,
        method: "POST",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),
  };
  auth = {
    /**
     * No description
     *
     * @tags auth
     * @name HealthList
     * @request GET:/auth/health
     */
    healthList: (params: RequestParams = {}) =>
      this.request<StatusOK, any>({
        path: `/auth/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
}
