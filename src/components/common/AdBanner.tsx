import React, { useMemo } from "react";
import {
	StyleSheet,
	TouchableOpacity,
	Image,
	Linking,
	Dimensions,
	View,
} from "react-native";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";

interface AdBannerProps {
	imageUrl: string;
	mobileImageUrl?: string;
	redirectionTarget: string;
	accessibilityLabel?: string;
}

/**
 * AdBanner Component
 *
 * A reusable banner component for displaying advertisements
 * Supports both desktop and mobile image variants
 */
const AdBanner: React.FC<AdBannerProps> = ({
	imageUrl,
	mobileImageUrl,
	redirectionTarget,
	accessibilityLabel = "Advertisement",
}) => {
	const windowWidth = Dimensions.get("window").width;
	// Use a fixed height ratio that matches the reference image (purple banner with logos)
	const heightMultiplier = 0.15; // Banner height as percentage of screen width

	// Optimize the image URL to prevent memory issues
	const optimizedImageUrl = useMemo(() => {
		return imageUrl
			? optimizeImageUrl(imageUrl, {
					width: windowWidth, // Use full screen width
					height: Math.round(windowWidth * heightMultiplier),
					quality: 90, // High quality for ad banners
					fit: "crop", // Ensure the image covers the entire area
			  })
			: null;
	}, [imageUrl, windowWidth, heightMultiplier]);

	// Handle banner click/press events
	const handlePress = async () => {
		console.log(
			"[AdBanner] Banner pressed with target:",
			redirectionTarget
		);
		if (redirectionTarget) {
			try {
				await Linking.openURL(redirectionTarget);
			} catch (error) {
				console.error("[AdBanner] Error opening URL:", error);
			}
		}
	};

	// Calculate banner height for proper spacing
	const bannerHeight = windowWidth * heightMultiplier;

	return (
		<View
			style={[
				styles.container,
				{ height: bannerHeight, width: windowWidth },
			]}
		>
			<TouchableOpacity
				style={[
					styles.touchable,
					{
						height: bannerHeight,
						width: windowWidth, // Use exact screen width
						opacity: 0.7, // Standard opacity for non-focused state
					},
				]}
				onPress={handlePress}
				activeOpacity={1}
				accessible={true}
				accessibilityLabel={accessibilityLabel}
				accessibilityRole="button"
				disabled={true} // disabled press event for redirection error (TV doesn't support)
			>
				<Image
					source={{ uri: optimizedImageUrl || imageUrl }}
					style={[
						styles.image,
						{
							height: bannerHeight,
							width: windowWidth, // Use exact screen width
						},
					]}
					resizeMode="cover"
				/>
			</TouchableOpacity>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		// Width is set dynamically in the component
		marginVertical: 0, // Remove vertical margins to prevent spacing issues
		overflow: "hidden",
	},
	touchable: {
		// Width is set dynamically in the component
		overflow: "hidden", // Clip image to touchable bounds
		borderRadius: 0, // Ensure no rounded corners
	},
	image: {
		// Width is set dynamically in the component
	},
});

export default AdBanner;
