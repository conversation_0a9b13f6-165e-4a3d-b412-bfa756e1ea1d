# Section Renderer Implementation Guide

This guide provides a step-by-step approach to creating new section renderers for the app.

## Overview of Section Renderer Architecture

The app uses a component-based architecture where:

1. `<PERSON><PERSON><PERSON><PERSON>` acts as a navigator to specific section components
2. Each section type has its own dedicated renderer component
3. Section renderers receive data and configuration props to customize their appearance

## Step 1: Identify the Section Type

First, determine what type of section you need to create:

- Carousel (horizontal scrolling items)
- Grid (items arranged in rows and columns)
- Live content (special styling for live content)
- Ad banner
- Other specialized layouts

## Step 2: Create the Section Renderer Component

1. Duplicate the `TemplateSectionRenderer.tsx` file
2. Rename it to match your section type (e.g., `NewFeatureSectionRenderer.tsx`)
3. Update the component name and props interface

```typescript
interface NewFeatureSectionRendererProps extends BaseSectionRendererProps<any> {
  // Add specific props for this renderer
  layoutProps?: SectionLayoutProps;
  showSpecialFeature?: boolean;
}

const NewFeatureSectionRenderer: React.FC<NewFeatureSectionRendererProps> = ({...
```

## Step 3: Implement the Rendering Logic

Replace the placeholder content with your specific rendering logic:

```typescript
// Extract data from the section
const items = data.items || [];

// Map the items to the format needed for your component
const mappedItems = items.map((item) => ({
	id: item.itemId,
	title: item.name,
	// Add other properties as needed
}));

return (
	<View style={[styles.container, { backgroundColor }]}>
		{sectionTitle && (
			<Text style={styles.sectionTitle}>{sectionTitle}</Text>
		)}

		{/* Your specific component implementation */}
		<YourCustomComponent
			data={mappedItems}
			onPress={(id) => onPress?.(id, "your_item_type")}
		/>
	</View>
);
```

## Step 4: Add to the Section Renderer Switch Case

Update the appropriate page-specific section renderer to include your new component:

```typescript
// In CompetitionPageSectionRenderer.tsx or other page renderer
switch (section._kenticoItemType) {
	// Existing cases...

	case SectionType.YOUR_NEW_SECTION_TYPE:
		return (
			<NewFeatureSectionRenderer
				data={section}
				onPress={onItemPress}
				// Pass any additional props
			/>
		);
}
```

## Step 5: Add Configuration (if needed)

If your section needs specific configuration, add it to the section configs:

```typescript
// In CompetitionPageSectionRenderer.tsx
const COMPETITION_SECTION_CONFIGS = {
	// Existing configs...

	your_section_codename: {
		renderer: "your_renderer_type",
		cardProps: {
			// Card-specific props
		},
		layoutProps: {
			// Layout-specific props
		},
	},
};
```

## Common Patterns and Best Practices

1. **Data Extraction**: Always check if data exists before trying to render
2. **Conditional Rendering**: Return null for empty sections
3. **Accessibility**: Include accessibility props for all interactive elements
4. **Performance**: Use React.memo for components that don't need frequent re-renders
5. **Styling**: Follow the existing style patterns for consistency
6. **Logging**: Add console logs for debugging but remove them in production
7. **Pagination**: Implement cursor-based pagination for sections with many items
8. **Loading States**: Show loading indicators when fetching more data

## Implementing Cursor-Based Pagination

For sections that may contain many items, implement cursor-based pagination:

```typescript
// State for pagination
const [videos, setVideos] = useState<VideoItem[]>([]);
const [loadingMore, setLoadingMore] = useState(false);
const [cursor, setCursor] = useState<string | null>(null);
const [hasMore, setHasMore] = useState(true);

// Initialize with data from props
useEffect(() => {
	// Set initial videos
	setVideos(data.Videos || []);

	// Set initial cursor if available
	if (data.cursor) {
		setCursor(data.cursor.after || null);
		setHasMore(!!data.cursor.after);
	}
}, [data]);

// Load more function
const handleLoadMore = useCallback(async () => {
	if (loadingMore || !hasMore || !cursor) return;

	setLoadingMore(true);
	try {
		const response = await apiClient.fetchMoreItems(cursor);

		// Add new items to state
		setVideos((prev) => [...prev, ...response.data.items]);

		// Update cursor
		setCursor(response.data.cursor?.after || null);
		setHasMore(!!response.data.cursor?.after);
	} catch (error) {
		console.error("Error loading more items:", error);
	} finally {
		setLoadingMore(false);
	}
}, [cursor, hasMore, loadingMore]);

// Add to FlatList
<FlatList
	data={videos}
	renderItem={renderItem}
	onEndReached={handleLoadMore}
	onEndReachedThreshold={0.5}
	ListFooterComponent={
		loadingMore ? (
			<View style={styles.loadingContainer}>
				<ActivityIndicator
					size="large"
					color="#fff"
				/>
			</View>
		) : null
	}
/>;

// Style for loading indicator (for horizontal lists)
const styles = StyleSheet.create({
	// Other styles...
	loadingContainer: {
		padding: 0,
		height: scale(160), // Match the height of your items
		width: scale(80),
		position: "relative",
		justifyContent: "center",
		alignItems: "center",
	},
});
```

## Example: Simple Carousel Section Renderer

```typescript
// Extract data
const items = data.items || [];
if (!items.length) return null;

// Render
return (
	<View style={styles.container}>
		{sectionTitle && (
			<Text style={styles.sectionTitle}>{sectionTitle}</Text>
		)}

		<FlatList
			data={items}
			horizontal
			renderItem={({ item }) => (
				<TouchableOpacity
					onPress={() => onPress?.(item.itemId, "video")}
				>
					<Image
						source={{ uri: item.thumbnail }}
						style={styles.thumbnail}
					/>
					<Text style={styles.itemTitle}>{item.name}</Text>
				</TouchableOpacity>
			)}
			keyExtractor={(item) => item.itemId}
		/>
	</View>
);
```
