import React from "react";
import {
	View,
	Text,
	Image,
	TouchableOpacity,
	StyleSheet,
} from "react-native";

// Import placeholder image from assets
const PLACEHOLDER_IMAGE = require("../../assets/images/placeholder.jpg");
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { optimizeImageUrl } from "../../utils/helpers/imageOptimization.helper";

/**
 * LiveVideoCard Component
 *
 * A specialized card component for displaying live/upcoming events with:
 * - White border around the card
 * - Full-width thumbnail image inside the border
 * - White bar at the bottom with date and time metadata
 * - Title below the card
 *
 * @param id - Unique identifier for the video
 * @param title - Title of the video
 * @param imageUrl - URL of the thumbnail image
 * @param onPress - Callback for when the card is pressed
 * @param width - Width of the card
 * @param height - Height of the thumbnail
 * @param date - Date text to display (e.g., "Friday 2", "Today", "Tomorrow")
 * @param time - Time text to display (e.g., "19:50")
 * @param isLive - Whether this is a live event
 */
interface LiveVideoCardProps {
	id: string;
	title: string;
	imageUrl?: string | null;
	onPress: (id: string, type?: string, eventData?: any) => void;
	width?: number;
	height?: number;
	date?: string;
	time?: string;
	isLive?: boolean;
}

const LiveVideoCard: React.FC<LiveVideoCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	width = scale(280),
	height = scale(160),
	date,
	time,
	isLive = false,
}) => {
	// Optimize the image URL to prevent memory issues
	const optimizedImageUrl = imageUrl
		? optimizeImageUrl(imageUrl, "OPTIMIZED")
		: null;

	// Extract date and time from metadata if provided
	const dateText = date || "";
	const timeText = time || "";

	return (
		<TouchableOpacity
			style={[styles.container, { width, opacity: 0.7 }]}
			onPress={() =>
				onPress(id, isLive ? "live-event" : "event", {
					name: title,
					poster: imageUrl,
					isLive,
				})
			}
			activeOpacity={1}
			// Add TV specific props
			hasTVPreferredFocus={false}
			tvParallaxProperties={{ enabled: true }}
			accessible={true}
			accessibilityRole="button"
			accessibilityLabel={`${isLive ? "Live" : "Upcoming"} match: ${
				title || "Untitled Match"
			}`}
		>
			{/* Card with white border */}
			<View
				style={[
					styles.cardContainer,
					{ width, height: height + scale(40) },
				]}
			>
				{/* Thumbnail container - full width of inner area */}
				<View style={styles.thumbnailContainer}>
					{optimizedImageUrl ? (
						<Image
							source={{ uri: optimizedImageUrl }}
							style={styles.thumbnail}
							resizeMode="cover"
							onError={(error) => {
								console.warn(
									`Image load error for ${title}:`,
									error.nativeEvent
								);
							}}
						/>
					) : (
						<Image
							source={PLACEHOLDER_IMAGE}
							style={styles.thumbnail}
							resizeMode="cover"
						/>
					)}

					{/* Live indicator badge removed - redundant since users already know it's live */}
				</View>

				{/* Metadata bar (thicker bottom border) */}
				<View style={styles.metadataBar}>
					<Text style={styles.dateText}>{dateText}</Text>
					<Text style={styles.timeText}>{timeText}</Text>
				</View>
			</View>

			{/* Title below the card */}
			<View style={styles.titleContainer}>
				<Text
					style={styles.title}
					numberOfLines={1}
					ellipsizeMode="tail"
				>
					{title || "Untitled Match"}
				</Text>
			</View>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		marginRight: scale(16),
		marginBottom: scale(20),
	},
	cardContainer: {
		borderWidth: scale(6),
		borderColor: "#ffffff",
		borderRadius: scale(6),
		overflow: "hidden",
		flexDirection: "column",
	},
	thumbnailContainer: {
		width: "100%",
		flex: 1, // Use flex to take remaining space
		position: "relative", // For absolute positioning of overlays
	},
	thumbnail: {
		width: "100%",
		height: "100%",
	},
	// Live indicator styles removed - no longer needed
	metadataBar: {
		width: "100%",
		height: scale(40),
		backgroundColor: "#ffffff",
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "center",
		paddingHorizontal: scale(5),
	},
	dateText: {
		fontSize: scale(22),
		color: "#000000",
		fontWeight: "500",
	},
	timeText: {
		fontSize: scale(20),
		color: "#000000",
		fontWeight: "500",
	},
	titleContainer: {
		marginTop: scale(8),
		width: "100%",
	},
	title: {
		fontSize: scale(28),
		paddingVertical: scale(12),
		paddingLeft: scale(4),
		color: "#ffffff",
		fontWeight: "bold",
		lineHeight: scale(24),
	},
});

export default LiveVideoCard;
