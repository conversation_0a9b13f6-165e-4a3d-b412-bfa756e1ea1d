import React from "react";
import {
	View,
	TouchableOpacity,
	Text,
	StyleSheet,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../styles/globalStyles";

interface LanguageSelectorProps {
	currentLanguage: string;
	onLanguageChange: (language: string) => void;
	style?: any;
}

const LanguageSelector = ({
	currentLanguage,
	onLanguageChange,
}: LanguageSelectorProps) => {
	return (
		<View style={styles.container}>
			<TouchableOpacity
				style={[
					styles.button,
					currentLanguage === "en" && styles.activeButton,
				]}
				onPress={() => onLanguageChange("en")}
				// hasTVPreferredFocus={true}
			>
				<Text
					style={[
						styles.text,
						currentLanguage === "en" && styles.activeText,
					]}
				>
					EN
				</Text>
			</TouchableOpacity>
			<TouchableOpacity
				style={[
					styles.button,
					currentLanguage === "fr" && styles.activeButton,
				]}
				onPress={() => onLanguageChange("fr")}
			>
				<Text
					style={[
						styles.text,
						currentLanguage === "fr" && styles.activeText,
					]}
				>
					FR
				</Text>
			</TouchableOpacity>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flexDirection: "row",
		justifyContent: "flex-end",
		alignItems: "center",
		gap: scale(8), // Use scale helper for consistent spacing
	},
	button: {
		paddingVertical: scale(8),
		paddingHorizontal: scale(12),
		borderRadius: scale(4),
		borderWidth: scale(1),
		borderColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		backgroundColor: "transparent",
		opacity: 0.7,
	},
	activeButton: {
		backgroundColor: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		opacity: 1,
	},
	text: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(20), // Use scale helper for font size
		fontWeight: "600",
		textAlign: "center",
		minWidth: scale(32), // Ensure consistent button width
	},
	activeText: {
		color: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		fontWeight: "bold",
	},
});

export default LanguageSelector;
