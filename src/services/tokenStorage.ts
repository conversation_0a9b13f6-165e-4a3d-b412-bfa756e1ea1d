/**
 * Token Storage Service
 * Handles secure storage and retrieval of authentication tokens
 * Uses platform-appropriate storage methods (localStorage for web, memory for TV platforms)
 */

// Storage keys for persisting tokens
const ACCESS_TOKEN_KEY = "access_token";
const REFRESH_TOKEN_KEY = "refresh_token";
const TOKEN_EXPIRY_KEY = "token_expiry";
const USER_ID_KEY = "user_id";

// Global variables for in-memory storage as fallback
let globalAccessToken: string | null = null;
let globalRefreshToken: string | null = null;
let globalTokenExpiry: number | null = null;
let globalUserId: string | null = null;

// Token data interface
export interface TokenData {
	accessToken: string;
	refreshToken: string;
	expiresIn: number; // seconds
	userId: string;
}

/**
 * Token Storage Utilities
 * Provides cross-platform token storage with fallback mechanisms
 */
export class TokenStorage {
	/**
	 * Try localStorage (for web platforms)
	 */
	private static tryLocalStorage(
		action: "get" | "set" | "remove",
		key: string,
		value?: string
	): string | null {
		try {
			if (typeof window !== "undefined" && window.localStorage) {
				if (action === "get") {
					return window.localStorage.getItem(key);
				} else if (action === "set" && value) {
					window.localStorage.setItem(key, value);
					return value;
				} else if (action === "remove") {
					window.localStorage.removeItem(key);
					return null;
				}
			}
		} catch (error) {
			console.log("localStorage not available:", error);
		}
		return null;
	}

	/**
	 * Fallback to global variables (in-memory storage)
	 */
	private static useGlobalFallback(
		action: "get" | "set" | "remove",
		key: string,
		value?: string
	): string | null {
		const globalMap: { [key: string]: string | null } = {
			[ACCESS_TOKEN_KEY]: globalAccessToken,
			[REFRESH_TOKEN_KEY]: globalRefreshToken,
			[TOKEN_EXPIRY_KEY]: globalTokenExpiry?.toString() || null,
			[USER_ID_KEY]: globalUserId,
		};

		if (action === "get") {
			return globalMap[key];
		} else if (action === "set" && value) {
			switch (key) {
				case ACCESS_TOKEN_KEY:
					globalAccessToken = value;
					break;
				case REFRESH_TOKEN_KEY:
					globalRefreshToken = value;
					break;
				case TOKEN_EXPIRY_KEY:
					globalTokenExpiry = parseInt(value);
					break;
				case USER_ID_KEY:
					globalUserId = value;
					break;
			}
			return value;
		} else if (action === "remove") {
			switch (key) {
				case ACCESS_TOKEN_KEY:
					globalAccessToken = null;
					break;
				case REFRESH_TOKEN_KEY:
					globalRefreshToken = null;
					break;
				case TOKEN_EXPIRY_KEY:
					globalTokenExpiry = null;
					break;
				case USER_ID_KEY:
					globalUserId = null;
					break;
			}
			return null;
		}
		return null;
	}

	/**
	 * Store token data securely
	 */
	static async storeTokens(tokenData: TokenData): Promise<void> {
		try {
			const expiryTime = Date.now() + tokenData.expiresIn * 1000; // Convert to milliseconds

			// Try localStorage first, fallback to memory
			this.tryLocalStorage("set", ACCESS_TOKEN_KEY, tokenData.accessToken) ||
				this.useGlobalFallback("set", ACCESS_TOKEN_KEY, tokenData.accessToken);

			this.tryLocalStorage("set", REFRESH_TOKEN_KEY, tokenData.refreshToken) ||
				this.useGlobalFallback("set", REFRESH_TOKEN_KEY, tokenData.refreshToken);

			this.tryLocalStorage("set", TOKEN_EXPIRY_KEY, expiryTime.toString()) ||
				this.useGlobalFallback("set", TOKEN_EXPIRY_KEY, expiryTime.toString());

			this.tryLocalStorage("set", USER_ID_KEY, tokenData.userId) ||
				this.useGlobalFallback("set", USER_ID_KEY, tokenData.userId);

			console.log("Tokens stored successfully");
		} catch (error) {
			console.error("Error storing tokens:", error);
			throw error;
		}
	}

	/**
	 * Retrieve stored access token
	 */
	static async getAccessToken(): Promise<string | null> {
		try {
			return (
				this.tryLocalStorage("get", ACCESS_TOKEN_KEY) ||
				this.useGlobalFallback("get", ACCESS_TOKEN_KEY)
			);
		} catch (error) {
			console.error("Error retrieving access token:", error);
			return null;
		}
	}

	/**
	 * Retrieve stored refresh token
	 */
	static async getRefreshToken(): Promise<string | null> {
		try {
			return (
				this.tryLocalStorage("get", REFRESH_TOKEN_KEY) ||
				this.useGlobalFallback("get", REFRESH_TOKEN_KEY)
			);
		} catch (error) {
			console.error("Error retrieving refresh token:", error);
			return null;
		}
	}

	/**
	 * Check if access token is expired
	 */
	static async isTokenExpired(): Promise<boolean> {
		try {
			const expiryStr =
				this.tryLocalStorage("get", TOKEN_EXPIRY_KEY) ||
				this.useGlobalFallback("get", TOKEN_EXPIRY_KEY);

			if (!expiryStr) return true;

			const expiryTime = parseInt(expiryStr);
			const currentTime = Date.now();
			const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

			return currentTime >= expiryTime - bufferTime;
		} catch (error) {
			console.error("Error checking token expiry:", error);
			return true;
		}
	}

	/**
	 * Get stored user ID
	 */
	static async getUserId(): Promise<string | null> {
		try {
			return (
				this.tryLocalStorage("get", USER_ID_KEY) ||
				this.useGlobalFallback("get", USER_ID_KEY)
			);
		} catch (error) {
			console.error("Error retrieving user ID:", error);
			return null;
		}
	}

	/**
	 * Clear all stored tokens
	 */
	static async clearTokens(): Promise<void> {
		try {
			this.tryLocalStorage("remove", ACCESS_TOKEN_KEY) ||
				this.useGlobalFallback("remove", ACCESS_TOKEN_KEY);

			this.tryLocalStorage("remove", REFRESH_TOKEN_KEY) ||
				this.useGlobalFallback("remove", REFRESH_TOKEN_KEY);

			this.tryLocalStorage("remove", TOKEN_EXPIRY_KEY) ||
				this.useGlobalFallback("remove", TOKEN_EXPIRY_KEY);

			this.tryLocalStorage("remove", USER_ID_KEY) ||
				this.useGlobalFallback("remove", USER_ID_KEY);

			console.log("All tokens cleared");
		} catch (error) {
			console.error("Error clearing tokens:", error);
		}
	}
}
