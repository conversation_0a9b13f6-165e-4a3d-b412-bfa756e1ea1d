/**
 * Firebase REST API Authentication Service
 * Handles Firebase authentication using REST API
 * Provides Firebase JWT token that gets exchanged for app access tokens via the OnRewind API
 */

import { FIREBASE_API_KEY } from "../config/firebase";

export interface FirebaseUser {
	uid: string;
	email: string;
	displayName?: string;
	emailVerified: boolean;
}

export interface FirebaseAuthResult {
	user: FirebaseUser;
	token: string; // Firebase JWT token for OnRewind API
}

export interface FirebaseAuthError {
	code: string;
	message: string;
}

// Firebase REST API response interface
interface FirebaseRestAuthResponse {
	kind: string;
	localId: string;
	email: string;
	displayName?: string;
	idToken: string;
	registered: boolean;
	profilePicture?: string;
	refreshToken: string;
	expiresIn: string;
}

// Firebase REST API error response interface
interface FirebaseRestErrorResponse {
	error: {
		code: number;
		message: string;
		errors: Array<{
			message: string;
			domain: string;
			reason: string;
		}>;
	};
}

/**
 * Firebase Authentication Service using REST API
 * Performs Firebase authentication via REST API and returns a Firebase JWT token
 * that will be exchanged for app tokens via OnRewind API
 */
export class FirebaseAuth {
	private static readonly FIREBASE_AUTH_URL =
		"https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword";

	/**
	 * Sign in with email and password using Firebase REST API
	 */
	static async signInWithEmailAndPassword(
		email: string,
		password: string
	): Promise<FirebaseAuthResult> {
		try {
			console.log(
				"Firebase REST API: Starting authentication for:",
				email
			);

			// Prepare the request payload
			const requestBody = {
				email: email,
				password: password,
				returnSecureToken: true,
			};

			console.log(
				"Firebase REST API: Sending authentication request"
			);

			// Make the REST API call to Firebase
			const response = await fetch(
				`${this.FIREBASE_AUTH_URL}?key=${FIREBASE_API_KEY}`,
				{
					method: "POST",
					headers: {
						"Content-Type": "application/json",
					},
					body: JSON.stringify(requestBody),
				}
			);

			const responseData = await response.json();

			if (!response.ok) {
				// Handle Firebase REST API errors
				const errorResponse =
					responseData as FirebaseRestErrorResponse;
				const firebaseError: FirebaseAuthError = {
					code: this.mapRestErrorToCode(errorResponse.error.message),
					message: this.getReadableErrorMessage(
						this.mapRestErrorToCode(errorResponse.error.message)
					),
				};
				throw firebaseError;
			}

			const authResponse = responseData as FirebaseRestAuthResponse;

			console.log("Firebase REST API: Authentication successful");
			console.log("Firebase REST API: Token obtained");

			// Create user object from REST API response
			const user: FirebaseUser = {
				uid: authResponse.localId,
				email: authResponse.email,
				displayName: authResponse.displayName || undefined,
				emailVerified: authResponse.registered, // REST API uses 'registered' field
			};

			return {
				user,
				token: authResponse.idToken,
			};
		} catch (error: any) {
			console.log("Firebase REST API authentication error:", error);

			// If it's already a FirebaseAuthError, re-throw it
			if (error.code && error.message) {
				throw error;
			}

			// Handle network or other errors
			const firebaseError: FirebaseAuthError = {
				code: "unknown",
				message: error.message || "Authentication failed",
			};

			throw firebaseError;
		}
	}

	/**
	 * Map Firebase REST API error messages to error codes
	 */
	private static mapRestErrorToCode(errorMessage: string): string {
		if (errorMessage.includes("EMAIL_NOT_FOUND")) {
			return "auth/user-not-found";
		}
		if (errorMessage.includes("INVALID_PASSWORD")) {
			return "auth/wrong-password";
		}
		if (errorMessage.includes("USER_DISABLED")) {
			return "auth/user-disabled";
		}
		if (errorMessage.includes("TOO_MANY_ATTEMPTS_TRY_LATER")) {
			return "auth/too-many-requests";
		}
		if (errorMessage.includes("INVALID_EMAIL")) {
			return "auth/invalid-email";
		}
		if (errorMessage.includes("WEAK_PASSWORD")) {
			return "auth/weak-password";
		}
		if (errorMessage.includes("EMAIL_EXISTS")) {
			return "auth/email-already-in-use";
		}
		if (errorMessage.includes("INVALID_API_KEY")) {
			return "auth/api-key-not-valid";
		}
		return "auth/unknown";
	}

	/**
	 * Convert Firebase error codes to user-friendly messages
	 */
	private static getReadableErrorMessage(errorCode: string): string {
		switch (errorCode) {
			case "auth/user-not-found":
				return "No account found with this email address.";
			case "auth/wrong-password":
				return "Incorrect password. Please try again.";
			case "auth/user-disabled":
				return "This account has been disabled.";
			case "auth/too-many-requests":
				return "Too many failed attempts. Please try again later.";
			case "auth/invalid-email":
				return "Please enter a valid email address.";
			case "auth/weak-password":
				return "Password must be at least 6 characters long.";
			case "auth/email-already-in-use":
				return "An account with this email already exists.";
			case "auth/api-key-not-valid":
				return "Authentication configuration error. Please contact support.";
			default:
				return "Authentication failed. Please try again.";
		}
	}
}
