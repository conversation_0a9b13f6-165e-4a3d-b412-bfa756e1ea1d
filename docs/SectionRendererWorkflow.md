# Section Renderer Workflow

This document visualizes the workflow for creating and using section renderers in the application.

## Section Renderer Architecture

```
┌─────────────────────┐
│                     │
│   CompetitionsPage  │
│                     │
└──────────┬──────────┘
           │
           │ Renders sections
           ▼
┌─────────────────────────────────┐
│                                 │
│  CompetitionPageSectionRenderer │
│                                 │
└──────────────┬──────────────────┘
               │
               │ Determines renderer based on section type
               ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    Section Renderers                        │
│                                                             │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────────┐  │
│  │               │ │               │ │                   │  │
│  │  Carousel     │ │  Dynamic      │ │  Dynamic Grid     │  │
│  │  Section      │ │  Carousel     │ │  With Category    │  │
│  │  Renderer     │ │  Renderer     │ │  Renderer         │  │
│  │               │ │               │ │                   │  │
│  └───────┬───────┘ └───────┬───────┘ └─────────┬─────────┘  │
│          │                 │                   │            │
└──────────┼─────────────────┼───────────────────┼────────────┘
           │                 │                   │
           │ Renders         │ Renders           │ Renders
           ▼                 ▼                   ▼
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    UI Components                            │
│                                                             │
│  ┌───────────────┐ ┌───────────────┐ ┌───────────────────┐  │
│  │               │ │               │ │                   │  │
│  │  StandardCard │ │  TopCarousel  │ │  CategoryCard     │  │
│  │               │ │  Card         │ │                   │  │
│  └───────────────┘ └───────────────┘ └───────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Section Renderer Creation Process

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  Identify       │     │  Create         │     │  Implement      │
│  Section Type   │────▶│  Component      │────▶│  Rendering      │
│                 │     │  Structure      │     │  Logic          │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └────────┬────────┘
                                                         │
                                                         │
┌─────────────────┐     ┌─────────────────┐     ┌────────▼────────┐
│                 │     │                 │     │                 │
│  Test and       │     │  Add to         │     │  Handle         │
│  Refine         │◀────│  Section        │◀────│  Interactions   │
│                 │     │  Renderer       │     │                 │
│                 │     │                 │     │                 │
└─────────────────┘     └─────────────────┘     └─────────────────┘
```

## Data Flow in Section Renderers

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    Section Renderer                         │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐  │
│  │                                                       │  │
│  │  Props                                                │  │
│  │  ┌─────────────────┐  ┌─────────────────────────┐     │  │
│  │  │                 │  │                         │     │  │
│  │  │  Section Data   │  │  Configuration Props    │     │  │
│  │  │  - title        │  │  - layoutProps          │     │  │
│  │  │  - items        │  │  - cardProps            │     │  │
│  │  │  - metadata     │  │  - other customizations │     │  │
│  │  │                 │  │                         │     │  │
│  │  └─────────────────┘  └─────────────────────────┘     │  │
│  │                                                       │  │
│  └───────────────────────────────────────────────────────┘  │
│                              │                              │
│                              ▼                              │
│  ┌───────────────────────────────────────────────────────┐  │
│  │                                                       │  │
│  │  Processing                                           │  │
│  │  - Extract data                                       │  │
│  │  - Apply configurations                               │  │
│  │  - Map items to component format                      │  │
│  │  - Handle empty states                                │  │
│  │  - Process pagination data (cursors)                  │  │
│  │  - Manage loading states                              │  │
│  │                                                       │  │
│  └───────────────────────────────────────────────────────┘  │
│                              │                              │
│                              ▼                              │
│  ┌───────────────────────────────────────────────────────┐  │
│  │                                                       │  │
│  │  Rendering                                            │  │
│  │  - Section container                                  │  │
│  │  - Section header                                     │  │
│  │  - Item components (cards, etc.)                      │  │
│  │  - Interactive elements                               │  │
│  │  - Pagination controls                                │  │
│  │  - Loading indicators                                 │  │
│  │                                                       │  │
│  └───────────────────────────────────────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## Section Configuration System

```
┌─────────────────────────────────────────────────────────────┐
│                                                             │
│                    Section Configuration                    │
│                                                             │
│  ┌───────────────────────────────────────────────────────┐  │
│  │                                                       │  │
│  │  COMPETITION_SECTION_CONFIGS                          │  │
│  │                                                       │  │
│  │  {                                                    │  │
│  │    "section_codename": {                              │  │
│  │      renderer: "carousel",                            │  │
│  │      cardProps: {                                     │  │
│  │        imageRatio: "16:9",                            │  │
│  │        showTitle: true,                               │  │
│  │        ...                                            │  │
│  │      },                                               │  │
│  │      layoutProps: {                                   │  │
│  │        spacing: 15,                                   │  │
│  │        backgroundColor: "#102e55",                    │  │
│  │        ...                                            │  │
│  │      }                                                │  │
│  │    },                                                 │  │
│  │    ...                                                │  │
│  │  }                                                    │  │
│  │                                                       │  │
│  └───────────────────────────────────────────────────────┘  │
│                              │                              │
│                              ▼                              │
│  ┌───────────────────────────────────────────────────────┐  │
│  │                                                       │  │
│  │  CompetitionPageSectionRenderer                       │  │
│  │  - Looks up section by codename                       │  │
│  │  - Gets configuration                                 │  │
│  │  - Determines which renderer to use                   │  │
│  │  - Passes configuration props to renderer             │  │
│  │                                                       │  │
│  └───────────────────────────────────────────────────────┘  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```
