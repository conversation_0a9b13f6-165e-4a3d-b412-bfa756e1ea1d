name: build tvos

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ios_node_m1
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup node
        uses: actions/setup-node@v3
        with:
          node-version-file: ".nvmrc"

      - name: Install the provisioning profile and connect api key
        env:
          TVOS_PROVISIONING_PROFILE_BASE64: ${{ secrets.TVOS_PROVISIONING_PROFILE }}
          TVOS_CONNECT_API_KEY_BASE64: ${{ secrets.TVOS_CONNECT_API_KEY }}
        run: |
          PROVISIONING_PROFILE_PATH=$RUNNER_TEMP/pp.mobileprovision
          CONNECT_API_KEY_PATH=$RUNNER_TEMP/key.json

          echo -n "$TVOS_PROVISIONING_PROFILE_BASE64" | base64 --decode -o $PROVISIONING_PROFILE_PATH
          echo -n "$TVOS_CONNECT_API_KEY_BASE64" | base64 --decode -o $CONNECT_API_KEY_PATH

          echo "APP_STORE_CONNECT_API_FILE_PATH=$CONNECT_API_KEY_PATH" >> $GITHUB_ENV

          cp $PROVISIONING_PROFILE_PATH ~/Library/MobileDevice/Provisioning\ Profiles

      - name: Set UTF-8 Encoding
        run: |
          echo "LANG=en_US.UTF-8" >> $GITHUB_ENV
          echo "LC_ALL=en_US.UTF-8" >> $GITHUB_ENV

      - name: Install dependencies
        run: |
          echo "DEVELOPER_DIR=$XCODES_ROOT_FOLDER/Xcode-16.1.0.app/Contents/Developer" >> $GITHUB_ENV
          npm install
          bundle install

      - name: Export binary
        run: |
          npx rnv export -p tvos -s appstore

      - name: Upload binary
        run: |
          bundle exec fastlane pilot upload --api_key_path $APP_STORE_CONNECT_API_FILE_PATH --skip_submission true --skip_waiting_for_build_processing true --ipa platformBuilds/app_tvos/release/RNVApp-tvOS.ipa
