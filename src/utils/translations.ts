import { SupportedLanguage } from "../contexts/LanguageContext";

// Translation keys for type safety
export type TranslationKey =
	| "today"
	| "tomorrow"
	| "days"
	| "hours"
	| "minutes"
	| "monday"
	| "tuesday"
	| "wednesday"
	| "thursday"
	| "friday"
	| "saturday"
	| "sunday";

// Translation dictionaries
const translations = {
	en: {
		today: "Today",
		tomorrow: "Tomorrow",
		days: "DAYS",
		hours: "HOURS",
		minutes: "MINUTES",
		monday: "Monday",
		tuesday: "Tuesday",
		wednesday: "Wednesday",
		thursday: "Thursday",
		friday: "Friday",
		saturday: "Saturday",
		sunday: "Sunday",
	},
	fr: {
		today: "Aujourd'hui",
		tomorrow: "Demain",
		days: "JOURS",
		hours: "HEURES",
		minutes: "MINUTES",
		monday: "Lundi",
		tuesday: "Mardi",
		wednesday: "Mercredi",
		thursday: "Jeudi",
		friday: "Vendredi",
		saturday: "Samedi",
		sunday: "Dimanche",
	},
} as const;

/**
 * Get translated text for a given key and language
 * @param key - Translation key
 * @param language - Target language
 * @returns Translated text
 */
export const getTranslation = (
	key: TranslationKey,
	language: SupportedLanguage
): string => {
	return translations[language][key];
};

/**
 * Format date with proper localization
 * Returns "Today", "Tomorrow", or formatted date based on language
 * @param date - Date to format
 * @param language - Target language
 * @returns Formatted date string
 */
export const formatLocalizedDate = (
	date: Date,
	language: SupportedLanguage
): string => {
	const now = new Date();
	const tomorrow = new Date(now);
	tomorrow.setDate(tomorrow.getDate() + 1);

	// Check if it's today
	if (date.toDateString() === now.toDateString()) {
		return getTranslation("today", language);
	}

	// Check if it's tomorrow
	if (date.toDateString() === tomorrow.toDateString()) {
		return getTranslation("tomorrow", language);
	}

	// For other dates, format as "Wednesday 7" (day of week + day number)
	const weekdayKey = getWeekdayKey(date.getDay());
	const weekdayName = getTranslation(weekdayKey, language);
	const dayNumber = date.getDate();

	return `${weekdayName} ${dayNumber}`;
};

/**
 * Get weekday translation key from day number
 * @param dayNumber - Day number (0 = Sunday, 1 = Monday, etc.)
 * @returns Translation key for the weekday
 */
const getWeekdayKey = (dayNumber: number): TranslationKey => {
	const weekdays: TranslationKey[] = [
		"sunday", // 0
		"monday", // 1
		"tuesday", // 2
		"wednesday", // 3
		"thursday", // 4
		"friday", // 5
		"saturday", // 6
	];
	return weekdays[dayNumber];
};

/**
 * Format time with proper localization
 * @param date - Date to format time from
 * @param language - Target language
 * @returns Formatted time string (24-hour format)
 */
export const formatLocalizedTime = (
	date: Date,
	language: SupportedLanguage
): string => {
	// Use 24-hour format for both languages
	const hours = date.getHours();
	const minutes = date.getMinutes();
	return `${hours}:${minutes < 10 ? "0" + minutes : minutes}`;
};

/**
 * Format date and time together
 * @param dateString - ISO date string
 * @param language - Target language
 * @returns Object with formatted date text and time
 */
export const formatEventDateTime = (
	dateString: string | null | undefined,
	language: SupportedLanguage
): { text: string; time: string } => {
	// default text and time placeholders
	if (!dateString) {
		return {
			text: getTranslation("tomorrow", language),
			time: "15:55",
		};
	}

	const date = new Date(dateString);
	const text = formatLocalizedDate(date, language);
	const time = formatLocalizedTime(date, language);

	return { text, time };
};
