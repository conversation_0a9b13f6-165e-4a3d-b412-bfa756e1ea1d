{"openapi": "3.0.0", "info": {"description": "This is the Users service OpenAPI 3.0 specification.\n\nSome useful links:\n  - [The source API definition](/docs/swagger.json)", "version": "1.0.0", "title": "Users-service-api"}, "tags": [{"name": "default", "description": "Default endpoints not grouped"}, {"name": "auth", "description": "Everything about authentication"}, {"name": "fans", "description": "Everything related to Fans"}, {"name": "fans/firebase", "description": "Firebase account utilities"}, {"name": "favorites", "description": "Everything related to Fan's Favorites"}, {"name": "profiles", "description": "Everything related to Profiles"}], "paths": {"/auth/health": {"get": {"tags": ["auth"], "summary": "Check the status of the service after the middlewares", "description": "If the service is up and running correctly the response will be 'ok'", "operationId": "getAuthHealth", "parameters": [{"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Service is up and healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Health"}}}}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}}}, "/auth/login": {"post": {"tags": ["auth"], "summary": "Allows a User of the backoffice to get a JWT token that can be used as authorization to access other resources", "description": "", "operationId": "loginUser", "parameters": [{"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Login success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLoginResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserLogin"}}}}}}, "/auth/logout": {"delete": {"tags": ["auth"], "summary": "Allows a User of the backoffice to invalidate his token", "description": "", "operationId": "logoutUser", "parameters": [{"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Logout successfuly", "content": {"application/text": {"schema": {"$ref": "#/components/schemas/UserLogoutResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/auth/fan/login": {"post": {"tags": ["auth"], "summary": "Allows a Fan to get a JWT token that can be used as authorization to access other resources", "description": "The body could have differents fields depending on the Customer's SSO, it allows to exchange a token from Firebase, RSCA, ASSE or FFG SSOs", "operationId": "loginFan", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/MainAPIKey"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Login success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanLoginResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanLoginInput"}}}}}}, "/auth/fan/logout": {"delete": {"tags": ["auth"], "summary": "Invalidates a JWT token created previously with login", "description": "", "operationId": "logoutFan", "parameters": [{"$ref": "#/components/parameters/MainAPIKey"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Logout success", "content": {"application/text": {"schema": {"$ref": "#/components/schemas/FanLogoutResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/auth/fan/refresh-token": {"post": {"tags": ["auth"], "summary": "Exchange a refresh token by a new valid access token", "description": "", "operationId": "refreshTokenFan", "parameters": [{"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Refresh token success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanLoginResponse"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenBody"}}}}}}, "/health": {"get": {"summary": "Check the status of the service before the middlewares", "description": "If the service is up and running correctly the response will be 'OK'", "operationId": "getHealth", "parameters": [{"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Service is up and healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Health"}}}}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}}}, "/platform/fans": {"get": {"tags": ["fans"], "summary": "Returns whether the fan exists or not", "description": "", "operationId": "getFanByEmail", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Email"}], "responses": {"204": {"description": "The Fan Exists"}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "404": {"$ref": "#/components/responses/NotFound", "description": "Not Found"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}}}, "/platform/fans/me": {"get": {"tags": ["fans"], "summary": "Returns all the Fan's Data, including profiles, favorites and products", "description": "", "operationId": "getFanMe", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/ExpandFan"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fan"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}, "put": {"tags": ["fans"], "summary": "Returns all the Fan's Data, including profiles, favorites and products", "description": "", "operationId": "updateFanMe", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanWithoutRefs"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanInputBody"}}}}}, "delete": {"tags": ["fans"], "summary": "Schedule the fan for deletion after predefined delay, default to 15 days", "description": "This method also updates all the fan subscriptions to be cancelled at the period end, the fan should be removed after the delay period is reached. It works only for Firebase based SSOs", "operationId": "deleteFanMe", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanWithoutRefs"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/platform/fans/me/profiles": {"put": {"tags": ["fans"], "summary": "Set the marketing profiles to the current fan", "description": "This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to set there is a profile club or payment it is omitted.", "operationId": "updateFanMeProfiles", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fan"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfilesIdArray"}}}}}, "patch": {"tags": ["fans"], "summary": "Add new marketing profiles to the Fan", "description": "This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to add there is a profile club or payment it is omitted.", "operationId": "patchFanMeProfiles", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fan"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfilesIdArray"}}}}}, "delete": {"tags": ["fans"], "summary": "Add new marketing profiles to the Fan", "description": "This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to add there is a profile club or payment it is omitted.", "operationId": "removeFanMeProfiles", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/ProfileIdArrayQuery"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fan"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/platform/fans/me/profiles/clubs": {"put": {"tags": ["fans"], "summary": "Set the club profiles to the current fan", "description": "This endpoint does not allow setting profiles of type payment or marketing. If in the list of profiles Ids to set there is a profile marketing or payment it is omitted. Only 1 profile club men and 1 profile club women can be set, and they cannot be changed after that", "operationId": "updateFanMeProfilesClub", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Fan"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileClubBody"}}}}, "deprecated": true}}, "/platform/fans/me/abort": {"post": {"tags": ["fans"], "summary": "Abort the fan deletion if it is make before the account is definitely removed", "description": "", "operationId": "abortFanDelete", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanWithoutRefs"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AbortDeleteFan"}}}}}}, "/platform/fans/firebase/delete": {"post": {"tags": ["fans/firebase"], "summary": "Remove this fan from firebase only if it doesn't exists in the internal storage", "description": "", "operationId": "removeFirebaseFanAccount", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"204": {"description": "Success"}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteFirebaseAccount"}}}}}}, "/platform/favorites": {"get": {"tags": ["favorites"], "summary": "Returns the fan's favorites by type (Videos, Categories, Playlists)", "description": "", "operationId": "getFanFavorites", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Aggregate"}, {"$ref": "#/components/parameters/Type"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanFavorite"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["favorites"], "summary": "Add a fan favorite item by type", "description": "", "operationId": "addFanFavorite", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Type"}], "responses": {"201": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanFavoriteItem"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteItemBody"}}}}}, "delete": {"tags": ["favorites"], "summary": "Remove a fan favorite item by type", "description": "", "operationId": "deleteFanFavorite", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Type"}], "responses": {"204": {"description": "Success"}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FavoriteItemBody"}}}}}}, "/platform/favorites/{itemId}": {"get": {"tags": ["favorites"], "summary": "Returns the fan's favorites by type (Videos, Categories, Playlists)", "description": "", "operationId": "getFanFavoriteById", "parameters": [{"$ref": "#/components/parameters/ItemID"}, {"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Type"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanFavoriteItem"}}}}, "204": {"description": "This entity is not a favorite of this fan"}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/platform/profiles": {"get": {"tags": ["profiles"], "summary": "Returns the list of profiles for this account, this list can be filtered by type & category", "description": "", "operationId": "getProfiles", "parameters": [{"$ref": "#/components/parameters/Account"}, {"$ref": "#/components/parameters/AccountQuery"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/ProfileType"}, {"$ref": "#/components/parameters/ProfileCategory"}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProfileArray"}}}}, "400": {"$ref": "#/components/responses/BadRequest", "description": "Bad Request"}, "401": {"$ref": "#/components/responses/Unauthorized", "description": "Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden", "description": "Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError", "description": "Internal Server Error"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "An token issued by users-service"}}, "parameters": {"MainAPIKey": {"name": "x-api-key", "in": "header", "description": "API key", "required": false, "schema": {"type": "string"}}, "ServiceName": {"name": "x-service-name", "in": "header", "description": "name of the entity that makes the request", "required": false, "schema": {"type": "string"}}, "Account": {"name": "x-account-key", "in": "header", "description": "The customer's account key", "required": true, "schema": {"type": "string"}}, "AccountQuery": {"name": "accountKey", "in": "query", "description": "The customer's account key passed as query param, if it is not already set in the headers", "required": false, "schema": {"type": "string"}}, "ExpandFan": {"name": "expand", "in": "query", "description": "expand related entities. ex: [Product,Profile,FanFavoriteVideo,FanFavoriteCategory,FanFavoritePlaylist]", "schema": {"type": "string"}}, "Aggregate": {"name": "aggregate", "in": "query", "description": "if true it fetches aussi the entity data from main-api", "required": false, "schema": {"type": "string", "enum": ["true", "false"]}}, "Type": {"name": "type", "in": "query", "description": "The type of favorite being processed", "required": false, "schema": {"type": "string", "enum": ["video", "playlist", "category"]}}, "ProfileType": {"name": "type", "in": "query", "description": "filter profiles by type", "required": false, "schema": {"type": "string", "enum": ["payment", "marketing", "men-club", "women-club", "club-subscriber"]}}, "Email": {"name": "email", "in": "query", "description": "filter by email", "required": true, "schema": {"type": "string", "format": "email"}}, "ProfileCategory": {"name": "category", "in": "query", "description": "filter profiles by category", "required": false, "schema": {"type": "string", "enum": ["communication", "competition", "favorite-men-club", "favorite-women-club", "premium"]}}, "Limit": {"name": "limit", "in": "query", "description": "limit the number of items in the response, allowing getting paginated items", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 99}}, "Before": {"name": "before", "in": "query", "description": "when using limit, allows getting the previous page", "required": false, "schema": {"type": "string", "format": "byte"}}, "After": {"name": "after", "in": "query", "description": "when using limit, allows getting the next page", "required": false, "schema": {"type": "string", "format": "byte"}}, "ItemID": {"name": "itemId", "in": "path", "description": "id of the item", "required": true, "schema": {"type": "string", "format": "uuid"}}, "ProfileIdArrayQuery": {"name": "Profiles", "in": "query", "description": "The list of profiles to detach to a Fan", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}, "responses": {"BadRequest": {"description": "Bad request", "content": {"application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "NotFound": {"description": "Resource does not found", "content": {"application/text": {"schema": {"$ref": "#/components/schemas/Exception"}}}}, "Unauthorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}, "Forbidden": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}}, "InternalServerError": {"description": "Internal Server Error", "content": {"application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "schemas": {"Health": {"type": "object", "properties": {"status": {"type": "string", "enum": ["OK", "KO"]}}}, "Error": {"type": "string"}, "Exception": {"type": "object", "properties": {"status": {"type": "integer"}, "type": {"type": "string"}, "message": {"type": "string"}}}, "FanLoginInput": {"oneOf": [{"$ref": "#/components/schemas/Firebase"}, {"$ref": "#/components/schemas/RSCA"}, {"$ref": "#/components/schemas/ASSE"}, {"$ref": "#/components/schemas/FFG"}]}, "Firebase": {"type": "object", "properties": {"vendorSSO": {"type": "object", "properties": {"name": {"type": "string", "enum": ["firebase"], "default": "firebase"}, "token": {"type": "string", "description": "a JWT from firebase"}, "userDetails": {"type": "object", "properties": {"firstname": {"type": "string"}, "lastname": {"type": "string"}, "providerOrigin": {"type": "string"}}}}, "required": ["name", "token"]}}, "required": ["vendorSSO"]}, "DeleteFirebaseAccount": {"type": "object", "properties": {"firebaseToken": {"type": "string", "description": "A JWT from firebase"}}, "required": ["firebaseToken"]}, "RSCA": {"type": "object", "properties": {"vendorSSO": {"type": "object", "properties": {"name": {"type": "string", "enum": ["rsca"], "default": "rsca"}, "token": {"type": "string", "description": "a JWT from RSCA's SSO"}}, "required": ["name", "token"]}}, "required": ["vendorSSO"]}, "ASSE": {"type": "object", "properties": {"vendorSSO": {"type": "object", "properties": {"name": {"type": "string", "enum": ["asse"], "default": "asse"}, "token": {"type": "string"}, "tokenMeta": {"type": "string"}}, "required": ["name", "token", "tokenMeta"]}}, "required": ["vendorSSO"]}, "FFG": {"type": "object", "properties": {"vendorSSO": {"type": "object", "properties": {"name": {"type": "string", "enum": ["ffgolf.tv"], "default": "ffgolf.tv"}, "token": {"type": "string", "description": "a JWT from FFG's SSO"}}, "required": ["name", "token"]}}, "required": ["vendorSSO"]}, "FanFavoriteVideoRequestBody": {"type": "object", "properties": {"VideoId": {"type": "string", "nullable": false, "format": "uuid"}}, "required": ["VideoId"]}, "FanFavoritePlaylistRequestBody": {"type": "object", "properties": {"PlaylistId": {"type": "string", "nullable": false, "format": "uuid"}}, "required": ["PlaylistId"]}, "FanFavoriteCategoryRequestBody": {"type": "object", "properties": {"CategoryId": {"type": "string", "nullable": false, "format": "uuid"}}, "required": ["CategoryId"]}, "FanLoginResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accessToken": {"type": "string"}, "refreshToken": {"type": "string"}, "tokenType": {"type": "string", "enum": ["Bearer"], "default": "Bearer"}, "expiresIn": {"type": "integer"}, "action": {"type": "string", "enum": ["updated", "created"]}}, "required": ["id", "accessToken", "refreshToken"]}, "FanLogoutResponse": {"type": "string"}, "UserLogin": {"type": "object", "properties": {"username": {"type": "string"}, "password": {"type": "string"}}, "required": ["username", "password"]}, "UserLoginResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accessToken": {"type": "string"}, "tokenType": {"type": "string", "enum": ["Bearer"], "default": "Bearer"}, "expiresIn": {"type": "integer"}}, "required": ["id", "accessToken"]}, "UserLogoutResponse": {"type": "string"}, "ProfilesIdArray": {"type": "object", "properties": {"Profiles": {"type": "array", "items": {"type": "string", "format": "uuid"}}}}, "ProfileClubBody": {"type": "object", "properties": {"womenClubProfileId": {"type": "string", "format": "uuid"}, "menClubProfileId": {"type": "string", "format": "uuid"}}}, "Account": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "key": {"type": "string", "maxLength": 255}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "meta": {}, "paymentConfig": {}, "SportId": {"type": "string", "nullable": true, "format": "uuid"}, "cachingData": {}, "sendMailOptions": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Modules": {"type": "array", "items": {"$ref": "#/components/schemas/Module"}}, "Groups": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}, "Fans": {"type": "array", "items": {"$ref": "#/components/schemas/Fan"}}}, "required": ["id", "key", "name", "sendMailOptions", "createdAt", "updatedAt"]}, "Fan": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "maxLength": 255}, "username": {"type": "string", "nullable": true, "maxLength": 255}, "firstname": {"type": "string", "nullable": true, "maxLength": 255}, "lastname": {"type": "string", "nullable": true, "maxLength": 255}, "gender": {"type": "string", "nullable": true, "enum": ["male", "female"]}, "country": {"type": "string", "nullable": true, "maxLength": 255}, "birthYear": {"type": "integer", "nullable": true, "format": "int32"}, "birthdate": {"type": "string", "nullable": true, "format": "date-time"}, "address": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 255}, "imageUrl": {"type": "string", "nullable": true, "maxLength": 255}, "status": {"type": "string", "nullable": true, "enum": ["active", "inactive"]}, "password": {"type": "string", "nullable": true, "maxLength": 255}, "preferredLanguage": {"type": "string", "nullable": true, "maxLength": 2}, "deleteAt": {"type": "string", "nullable": true, "format": "date-time"}, "resetPasswordToken": {"type": "string", "nullable": true, "maxLength": 255}, "confirmationToken": {"type": "string", "nullable": true, "maxLength": 255}, "abortToken": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "menClubProfileId": {"type": "string", "nullable": true, "format": "uuid"}, "womenClubProfileId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Account": {"$ref": "#/components/schemas/Account"}, "FanActivities": {"type": "array", "items": {"$ref": "#/components/schemas/FanActivity"}}, "FanSessions": {"type": "array", "items": {"$ref": "#/components/schemas/FanSession"}}, "Profiles": {"type": "array", "items": {"$ref": "#/components/schemas/Profile"}}, "Fans_Profiles": {"type": "array", "items": {"$ref": "#/components/schemas/Fans_Profile"}}, "FanFavoriteVideos": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteVideo"}}, "FanFavoriteCategories": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteCategory"}}, "FanFavoritePlaylists": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoritePlaylist"}}, "AccountId": {"type": "string", "format": "uuid"}, "Products": {"type": "array", "items": {"$ref": "#/components/schemas/Product"}}}, "required": ["id", "email", "createdAt", "updatedAt"]}, "FanActivity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"type": "string", "enum": ["watch", "bookmark", "like"]}, "meta": {}, "resourceType": {"type": "string", "enum": ["event", "article", "video", "challenger", "competition", "team", "teammate", "marker", "message", "tag", "season", "round"]}, "resourceId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Fan": {"$ref": "#/components/schemas/Fan"}}, "required": ["id", "type", "resourceType", "resourceId", "createdAt", "updatedAt"]}, "FanFavoriteCategory": {"type": "object", "properties": {"FanId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["FanId", "CategoryId", "createdAt", "updatedAt"]}, "FanFavoritePlaylist": {"type": "object", "properties": {"FanId": {"type": "string", "format": "uuid"}, "PlaylistId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["FanId", "PlaylistId", "createdAt", "updatedAt"]}, "FanFavoriteVideo": {"type": "object", "properties": {"FanId": {"type": "string", "format": "uuid"}, "VideoId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["FanId", "VideoId", "createdAt", "updatedAt"]}, "Fans_Profile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Fan": {"$ref": "#/components/schemas/Fan"}, "Profile": {"$ref": "#/components/schemas/Profile"}}, "required": ["id", "createdAt", "updatedAt"]}, "FanSession": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "accessToken": {"type": "string", "maxLength": 384}, "refreshToken": {"type": "string", "maxLength": 384}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Fan": {"$ref": "#/components/schemas/Fan"}}, "required": ["id", "accessToken", "refreshToken", "createdAt", "updatedAt"]}, "Group": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "accessLevel": {"type": "integer", "nullable": true, "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Accounts": {"type": "array", "items": {"$ref": "#/components/schemas/Account"}}, "Roles": {"type": "array", "items": {"$ref": "#/components/schemas/Role"}}, "Users": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}, "required": ["id", "name", "createdAt", "updatedAt"]}, "Module": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "integer", "format": "int32"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Accounts": {"type": "array", "items": {"$ref": "#/components/schemas/Account"}}}, "required": ["id", "code", "name", "createdAt", "updatedAt"]}, "Profile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["payment", "marketing", "men-club", "women-club", "club-subscriber"]}, "category": {"type": "string", "nullable": true, "enum": ["communication", "competition", "favorite-men-club", "favorite-women-club", "premium"]}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "retainOnLogin": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "AccountId": {"type": "string", "format": "uuid"}}, "required": ["id", "name", "retainOnLogin", "createdAt", "updatedAt", "AccountId"]}, "Role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "accessLevel": {"type": "integer", "nullable": true, "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Groups": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}, "required": ["id", "name", "createdAt", "updatedAt"]}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "username": {"type": "string", "maxLength": 255}, "email": {"type": "string", "maxLength": 255}, "lastName": {"type": "string", "nullable": true, "maxLength": 255}, "firstName": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 255}, "type": {"type": "string", "nullable": true, "enum": ["active", "inactive", "system"]}, "password": {"type": "string", "nullable": true, "maxLength": 255}, "resetPasswordToken": {"type": "string", "nullable": true, "maxLength": 255}, "confirmationToken": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "oauth": {}, "dashboard": {}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Groups": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}, "required": ["id", "username", "email", "createdAt", "updatedAt"]}, "Accounts_Modules": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "Accounts_Groups": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "Groups_Role": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "Groups_Users": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "updatedAt"]}, "FanWithoutRefs": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "maxLength": 255}, "username": {"type": "string", "nullable": true, "maxLength": 255}, "firstname": {"type": "string", "nullable": true, "maxLength": 255}, "lastname": {"type": "string", "nullable": true, "maxLength": 255}, "gender": {"type": "string", "nullable": true, "enum": ["male", "female"]}, "country": {"type": "string", "nullable": true, "maxLength": 255}, "birthYear": {"type": "integer", "nullable": true, "format": "int32"}, "birthdate": {"type": "string", "nullable": true, "format": "date-time"}, "address": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 255}, "imageUrl": {"type": "string", "nullable": true, "maxLength": 255}, "status": {"type": "string", "nullable": true, "enum": ["active", "inactive"]}, "password": {"type": "string", "nullable": true, "maxLength": 255}, "preferredLanguage": {"type": "string", "nullable": true, "maxLength": 2}, "deleteAt": {"type": "string", "nullable": true, "format": "date-time"}, "resetPasswordToken": {"type": "string", "nullable": true, "maxLength": 255}, "confirmationToken": {"type": "string", "nullable": true, "maxLength": 255}, "abortToken": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "menClubProfileId": {"type": "string", "nullable": true, "format": "uuid"}, "womenClubProfileId": {"type": "string", "nullable": true, "format": "uuid"}, "AccountId": {"type": "string", "format": "uuid"}}, "required": ["id", "email", "AccountId"]}, "FanInputBody": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "email": {"type": "string", "maxLength": 255}, "username": {"type": "string", "nullable": true, "maxLength": 255}, "firstname": {"type": "string", "nullable": true, "maxLength": 255}, "lastname": {"type": "string", "nullable": true, "maxLength": 255}, "gender": {"type": "string", "nullable": true, "enum": ["male", "female"]}, "country": {"type": "string", "nullable": true, "maxLength": 255}, "birthYear": {"type": "integer", "nullable": true, "format": "int32"}, "birthdate": {"type": "string", "nullable": true, "format": "date-time"}, "address": {"type": "string", "nullable": true, "maxLength": 255}, "phone": {"type": "string", "nullable": true, "maxLength": 255}, "imageUrl": {"type": "string", "nullable": true, "maxLength": 255}, "status": {"type": "string", "nullable": true, "enum": ["active", "inactive"]}, "password": {"type": "string", "nullable": true, "maxLength": 255}, "preferredLanguage": {"type": "string", "nullable": true, "maxLength": 2}, "deleteAt": {"type": "string", "nullable": true, "format": "date-time"}, "resetPasswordToken": {"type": "string", "nullable": true, "maxLength": 255}, "confirmationToken": {"type": "string", "nullable": true, "maxLength": 255}, "abortToken": {"type": "string", "nullable": true, "maxLength": 255}, "meta": {}, "AccountId": {"type": "string", "format": "uuid"}}}, "ProfileArray": {"type": "array", "items": {"$ref": "#/components/schemas/Profile"}}, "Product": {"type": "object", "properties": {"_type": {"type": "string", "enum": ["Product"], "default": "Product"}, "accountId": {"type": "string", "format": "uuid"}, "data": {"type": "object", "properties": {"description": {"type": "string"}, "weight": {"type": "number"}}}, "entityType": {"type": "string", "enum": ["Product"], "default": "Product"}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "productId": {"type": "string", "format": "uuid"}, "isStandard": {"type": "boolean"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "paymentOffers": {"$ref": "#/components/schemas/PaymentOffer"}, "profiles": {"type": "array", "items": {"$ref": "#/components/schemas/Profile"}}}, "required": ["_type", "accountId", "entityType", "id", "paymentOffers", "productId", "status"]}, "PaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["PaymentOffer"]}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "providerPaymentOfferId": {"type": "string"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"isLimitedDuration": {"type": "boolean"}, "population": {"type": "string"}, "weight": {"type": "number"}, "currency": {"type": "string"}, "interval": {"type": "string", "enum": ["day", "week", "month", "year"]}, "type": {"type": "string", "enum": ["one_time", "recurring"]}, "price": {"type": "number"}, "intervalCount": {"type": "integer"}}, "required": ["currency", "type", "price"]}, "_type": {"type": "string", "enum": ["PaymentOffer"]}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "duration": {"type": "number"}, "expiryDate": {"type": "string", "format": "date-time"}, "associatedProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/ProfilePaymentOffer"}}, "isPPV": {"type": "boolean"}}, "required": ["_type", "accountId", "entityType", "name", "paymentOfferId", "productId", "provider", "providerPaymentOfferId", "status", "isPPV"]}, "ProfileProduct": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["ProfileProduct"]}, "_type": {"type": "string", "enum": ["ProfileProduct"]}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "data": {"type": "object"}}, "required": ["entityType", "accountId", "profileId", "productId"]}, "ProfilePaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["ProfilePaymentOffer"]}, "_type": {"type": "string", "enum": ["ProfilePaymentOffer"]}, "accountId": {"type": "string", "format": "uuid"}, "profileId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "data": {"type": "object"}}, "required": ["entityType", "accountId", "profileId", "paymentOfferId"]}, "AbortDeleteFan": {"type": "object", "properties": {"abortToken": {"type": "string", "format": "uuid"}}, "required": ["abortToken"]}, "RefreshTokenBody": {"type": "object", "properties": {"refreshToken": {"type": "string", "format": "application/jwt", "pattern": "^([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_=]+)\\.([a-zA-Z0-9_\\-\\+\\/=]+)$", "example": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.PiUUnm7_XM7rbCg-8D2bR3v82bzvWFWuiMWc6JMi9xQ"}}}, "Cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}}, "PaginatedFanFavoriteVideos": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteVideo"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}, "PaginatedFanFavoriteCategories": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteCategory"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}, "PaginatedFanFavoritePlaylists": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanFavoritePlaylist"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}, "FanFavorite": {"oneOf": [{"$ref": "#/components/schemas/OptionalPaginatedFanFavoriteVideos"}, {"$ref": "#/components/schemas/OptionalPaginatedFanFavoritePlaylists"}, {"$ref": "#/components/schemas/OptionalPaginatedFanFavoriteCategories"}]}, "OptionalPaginatedFanFavoriteVideos": {"oneOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteVideo"}}, {"$ref": "#/components/schemas/PaginatedFanFavoriteVideos"}, {"$ref": "#/components/schemas/PaginatedVideos"}]}, "OptionalPaginatedFanFavoritePlaylists": {"oneOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FanFavoritePlaylist"}}, {"$ref": "#/components/schemas/PaginatedFanFavoritePlaylists"}, {"$ref": "#/components/schemas/PaginatedPlaylists"}]}, "OptionalPaginatedFanFavoriteCategories": {"oneOf": [{"type": "array", "items": {"$ref": "#/components/schemas/FanFavoriteCategory"}}, {"$ref": "#/components/schemas/PaginatedFanFavoriteCategories"}, {"$ref": "#/components/schemas/PaginatedCategories"}]}, "FanFavoriteItem": {"oneOf": [{"$ref": "#/components/schemas/FanFavoriteVideo"}, {"$ref": "#/components/schemas/FanFavoritePlaylist"}, {"$ref": "#/components/schemas/FanFavoriteCategory"}]}, "FavoriteItemBody": {"oneOf": [{"$ref": "#/components/schemas/FanFavoriteVideoRequestBody"}, {"$ref": "#/components/schemas/FanFavoritePlaylistRequestBody"}, {"$ref": "#/components/schemas/FanFavoriteCategoryRequestBody"}]}, "Video": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "duration": {"type": "number", "nullable": true, "format": "float"}, "poster": {"type": "string", "nullable": true, "maxLength": 255}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "VideoCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategory"}}, "playlistRank": {"type": "integer", "nullable": true, "format": "int32"}, "PlaylistId": {"type": "string", "nullable": true, "format": "uuid"}, "lastUpdated": {"type": "string", "nullable": true, "format": "date-time"}}, "required": ["name", "liveSource", "createdAt", "updatedAt"]}, "VideoCategorySubCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "VideoId": {"type": "string", "nullable": true, "format": "uuid"}, "CategoryId": {"type": "string", "nullable": true, "format": "uuid"}, "SubCategoryId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "SubCategory": {"$ref": "#/components/schemas/SubCategory"}}, "required": ["createdAt", "updatedAt"]}, "Category": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "portraitThumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "VideoCategory": {"$ref": "#/components/schemas/VideoCategory"}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "VideoCategory": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "VideoId": {"type": "string", "nullable": true, "format": "uuid"}, "CategoryId": {"type": "string", "nullable": true, "format": "uuid"}}, "required": ["createdAt", "updatedAt"]}, "SubCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string", "maxLength": 255}, "accountId": {"type": "string", "maxLength": 255}, "CategoryId": {"type": "string", "nullable": true, "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "portraitThumbnail": {"type": "string", "nullable": true, "maxLength": 255}, "heroImageDesktop": {"type": "string", "nullable": true, "maxLength": 255}, "heroImageMobile": {"type": "string", "nullable": true, "maxLength": 255}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "numberOfVideos": {"type": "integer", "nullable": true, "format": "int32"}}, "required": ["name", "accountId", "createdAt", "updatedAt"]}, "Tag": {"type": "object", "properties": {"id": {"type": "string", "nullable": true, "format": "uuid"}, "tagType": {"type": "string", "nullable": true, "maxLength": 255}, "name": {"type": "string", "maxLength": 255}, "options": {}, "AccountId": {"type": "string", "format": "uuid"}, "placeholders": {"type": "array", "nullable": true, "items": {"type": "string", "maxLength": 255}}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["name", "AccountId", "createdAt", "updatedAt"]}, "PaginatedVideos": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Video"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}, "PaginatedPlaylists": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Playlist"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}, "PaginatedCategories": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}}}}}