import React from "react";
import { View, FlatList, StyleSheet } from "react-native";
import {
	BaseSectionRendererProps,
	SectionWithItems,
} from "../../../types/sectionTypes";
import { CarouselStaticSection } from "../../../utils/apis/generated/kentico";
import SectionHeader from "../../common/SectionHeader";
import ClubCard from "../../common/ClubCard";
import { scale } from "../../../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";

/**
 * Props for the Clubs Section Renderer
 */
export interface ClubsSectionRendererProps
	extends BaseSectionRendererProps<CarouselStaticSection> {
	showTitle?: boolean;
	clubSize?: number;
}

/**
 * ClubsSectionRenderer Component
 *
 * Specialized renderer for club sections
 * Displays club logos in a horizontal carousel
 */
const ClubsSectionRenderer: React.FC<ClubsSectionRendererProps> = ({
	data,
	onPress,
	accessibilityLabel,
	showTitle = false,
	clubSize = scale(240),
}) => {
	// Cast data to SectionWithItems to ensure TypeScript knows it has an items property
	const sectionData = data as SectionWithItems;
	// Extract items from the data
	const items = sectionData.items || [];

	// Debug logs removed for production

	// Don't render anything if there are no items
	if (!items || items.length === 0) {
		// Skip rendering if no items are available
		return null;
	}

	// Map the items to a consistent format - keeping it simple
	const mappedItems = items.map((item: any, index: number) => {
		// Extract basic information - just the minimum needed
		let id = item.itemId || item.id || `club-${index}`;

		// Extract club codename from the API response
		// This is the most important part - we need this for the API request
		let clubCodename = "";

		// Extract the club codename from mobileRedirectionTarget
		if (item.image && item.image.mobileRedirectionTarget) {
			// Extract club codename from mobileRedirectionTarget (e.g., "hbtv://club_pays_d_aix_universite_club")
			const match = item.image.mobileRedirectionTarget.match(
				/hbtv:\/\/(club_[a-z0-9_]+)/
			);
			if (match && match[1]) {
				clubCodename = match[1];
			}
		}
		// Try to extract from buttonRedirectionUrl if available
		else if (item.image && item.image.buttonRedirectionUrl) {
			// Extract club codename from buttonRedirectionUrl (e.g., "https://www.handballtv.fr/clubs/club___brest_bretagne_handball")
			const match = item.image.buttonRedirectionUrl.match(
				/\/clubs\/(club[_a-z0-9]+)/
			);

			if (match && match[1]) {
				clubCodename = match[1];
			}
		}
		// Fallback to _kenticoCodename if neither mobileRedirectionTarget nor buttonRedirectionUrl is available
		else if (item._kenticoCodename) {
			clubCodename = item._kenticoCodename;
		}

		const title = item.name || item.title || `Club ${index + 1}`;

		// Get image URL from the nested structure
		let imageUrl = "";

		// Check for the nested image structure
		if (item.image && item.image.image && item.image.image.url) {
			// Use the landscape image URL
			imageUrl = item.image.image.url;
		}
		// Also check for mobile image as a fallback
		else if (
			item.image &&
			item.image.mobileImage &&
			item.image.mobileImage.url
		) {
			// Use the portrait/mobile image URL
			imageUrl = item.image.mobileImage.url;
		}
		// If no image found, use the React Native logo as fallback
		if (!imageUrl) {
			imageUrl = "https://reactnative.dev/img/tiny_logo.png";
		}

		return {
			id,
			key: `club-${id}`,
			title,
			imageUrl,
			type: "club",
			clubCodename,
		};
	});

	// Render the club card
	const renderClubCard = ({ item }: { item: any }) => {
		// Create event data object to pass to the onPress handler
		// Include all necessary data for ClubDetailsPage navigation
		const eventData = {
			name: item.title,
			poster: item.imageUrl,
			description: "",
			isLive: false, // Clubs are not live events
			startDate: null,
			// Add club-specific data for navigation
			clubId: item.id,
			clubName: item.title,
			clubImage: item.imageUrl,
			// Add the club codename for direct API access
			clubCodename: item.clubCodename || "",
		};

		return (
			<ClubCard
				id={item.id}
				title={item.title}
				imageUrl={item.imageUrl}
				onPress={() => {
					onPress?.(item.id, item.type, eventData);
				}}
				size={clubSize}
				showTitle={showTitle}
			/>
		);
	};

	// Get the section title
	const sectionTitle = sectionData.title || "";

	return (
		<View
			style={styles.container}
			accessible={true}
			accessibilityLabel={
				accessibilityLabel ||
				`Clubs Section: ${sectionTitle || "Clubs"}`
			}
		>
			{/* Section header with the title */}
			<View style={styles.headerContainer}>
				<SectionHeader title={sectionTitle} />
			</View>

			{/* Clubs Carousel - Simple version */}
			<FlatList
				data={mappedItems}
				horizontal
				showsHorizontalScrollIndicator={false}
				keyExtractor={(item) => {
					return item.key || `${item.type || "club"}-${item.id}`;
				}}
				contentContainerStyle={styles.list}
				ItemSeparatorComponent={() => (
					<View style={{ width: GLOBAL_STYLES.ITEM_SPACING }} />
				)}
				renderItem={renderClubCard}
				// Performance optimizations
				removeClippedSubviews={true}
				maxToRenderPerBatch={10}
				windowSize={5}
				initialNumToRender={10}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
	},
	headerContainer: {
		marginBottom: scale(12), // Add spacing between section title and content
	},
	list: {
		paddingVertical: scale(25),
		paddingHorizontal: scale(4),
	},
});

export default React.memo(ClubsSectionRenderer);
