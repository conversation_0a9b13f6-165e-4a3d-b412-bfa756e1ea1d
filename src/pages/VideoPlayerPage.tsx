// pages/VideoPlayer.tsx
import {
	StyleSheet,
	View,
	Text,
	ActivityIndicator,
	Platform,
	Pressable,
} from "react-native";
import React, {
	useEffect,
	useRef,
	useState,
	useCallback,
	memo,
	useMemo,
} from "react";
import Video, { VideoRef, DRMType } from "react-native-video";
import { VideoData } from "../types/types";
import {
	RouteProp,
	useRoute,
	useNavigation,
} from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../app/index";
import { kenticoAPIClient } from "../utils/kenticoInstance";
import { OriginsVideo } from "../utils/apis/generated/kentico";
import { mainAPIClient } from "../utils/mainApiInstance";
import { AuthService } from "../services/authService";

// new route props type from main index(app) page
type VideoPlayerRouteProp = RouteProp<
	RootStackParamList,
	"VideoPlayer"
>;
// data structure to pass to VideoPlayer
interface VideoPlayerProps {
	videoId: string; // itemId from the response
	title: string; // name from the response
	thumbnail: string; // thumbnail URL
	description?: string; // optional description
	ratio?: string; // video aspect ratio (optional)
	isLive?: boolean; // whether this is a live stream
	// startTime?: string; // when the live stream starts (optional)
}

interface VideoAccessResponse {
	url?: string | null;
	urlMp4?: string | null;
	visibility?: string;
	status?: string;
	awsStream?: "STANDBY" | "ONAIR" | "ARCHIVED";
}

interface EventAccessResponse {
	Video?: {
		url?: string;
		urlMp4?: string;
		id?: string;
	};
	Streams?: Array<{
		url: string;
		type: string;
		protocol: string;
	}>;
	startTime?: string; // Make startTime optional
}

const MAX_RETRIES = 5;
const RETRY_DELAY = 1000;

// Constants for video configuration
const VIDEO_CONFIG = {
	RETRY_COUNT: 5,
	// Buffer configuration temporarily disabled - using API quality settings
	// BUFFER_CONFIG: {
	// 	minBufferMs: 15000,
	// 	maxBufferMs: 50000,
	// 	bufferForPlaybackMs: 2500,
	// 	bufferForPlaybackAfterRebufferMs: 5000,
	// },
};

// Separate Loading component for better organization
const LoadingState = memo(
	({
		isLive = false,
		retryCount,
	}: {
		isLive?: boolean;
		retryCount: number;
	}) => (
		<View style={styles.loadingContainer}>
			<ActivityIndicator
				size="large"
				color="#fff"
			/>
			{isLive && retryCount > 0 && (
				<Text style={styles.loadingText}>
					Connecting to live stream... ({retryCount}/{MAX_RETRIES})
				</Text>
			)}
		</View>
	)
);

// Separate Error component with navigation back functionality
const ErrorState = memo(({ message }: { message: string }) => {
	const navigation =
		useNavigation<NativeStackNavigationProp<RootStackParamList>>();
	const [isFocused, setIsFocused] = useState(false);

	// Handle back navigation - go back to the previous screen (VideoDetailsPage)
	const handleGoBack = useCallback(() => {
		navigation.goBack();
	}, [navigation]);

	return (
		<View style={styles.errorContainer}>
			<Text style={styles.error}>{message}</Text>
			<Pressable
				style={[
					styles.backButton,
					isFocused && styles.backButtonFocused,
				]}
				onPress={handleGoBack}
				onFocus={() => setIsFocused(true)}
				onBlur={() => setIsFocused(false)}
				hasTVPreferredFocus={true} // Auto-focus on tvOS
				focusable={true}
				accessible={true}
				accessibilityRole="button"
				accessibilityLabel="Go back to video details"
			>
				<Text
					style={[
						styles.backButtonText,
						isFocused && styles.backButtonTextFocused,
					]}
				>
					← Back
				</Text>
			</Pressable>
		</View>
	);
});

// Separate Live Indicator component
const LiveIndicator = memo(() => (
	<View style={styles.liveIndicatorContainer}>
		<View style={styles.liveIndicator}>
			<Text style={styles.liveText}>LIVE</Text>
		</View>
	</View>
));

// component for upcoming live streams
const UpcomingLiveStream = memo(
	({
		title,
		startTime,
	}: {
		title: string;
		startTime: string | undefined;
	}) => {
		const [timeLeft, setTimeLeft] = useState<string>("");

		useEffect(() => {
			if (!startTime) return;

			const calculateTimeLeft = () => {
				const targetDate = new Date(startTime).getTime();
				const now = Date.now();
				const difference = targetDate - now;

				if (difference <= 0) return "";

				const hours = Math.floor(difference / (1000 * 60 * 60));
				const minutes = Math.floor(
					(difference % (1000 * 60 * 60)) / (1000 * 60)
				);

				return `${hours}h ${minutes}m`;
			};

			// Calculate initial time left
			setTimeLeft(calculateTimeLeft());

			const timer = setInterval(() => {
				setTimeLeft(calculateTimeLeft());
			}, 60000); // Update every minute

			return () => clearInterval(timer);
		}, [startTime]);

		return (
			<View style={styles.upcomingLiveContainer}>
				<Text style={styles.startTime}>
					Starts at:{" "}
					{startTime ? new Date(startTime).toLocaleDateString() : ""}
				</Text>
				{timeLeft && (
					<Text style={styles.timeLeft}>
						Stream starts in: {timeLeft}
					</Text>
				)}
			</View>
		);
	}
);

const VideoPlayer: React.FC = () => {
	const route = useRoute<VideoPlayerRouteProp>();
	const { video } = route.params; // Access the passed video data
	const [videoDetails, setVideoDetails] =
		useState<OriginsVideo | null>(null);
	const [videoAccess, setVideoAccess] =
		useState<VideoAccessResponse | null>(null);
	const [error, setError] = useState<string | null>(null);
	const [retryCount, setRetryCount] = useState(0);
	const [isBuffering, setIsBuffering] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [isVideoReady, setIsVideoReady] = useState(false);
	const [isPlaybackStarted, setIsPlaybackStarted] = useState(false);
	const [countdown, setCountdown] = useState(0);
	const [customStartTime, setCustomStartTime] = useState<
		string | null
	>(null);
	const abortControllerRef = useRef<AbortController | null>(null);
	const isMountedRef = useRef(true);
	const videoRef = useRef<VideoRef>(null);

	// Reset all states when component unmounts or video changes
	useEffect(() => {
		const resetStates = () => {
			if (isMountedRef.current) {
				setVideoDetails(null);
				setVideoAccess(null);
				setError(null);
				setRetryCount(0);
				setIsBuffering(false);
				setIsLoading(true);
				setIsVideoReady(false);
				setIsPlaybackStarted(false);
				setCountdown(0);
				setCustomStartTime(null);
			}
		};

		resetStates();

		return () => {
			isMountedRef.current = false;
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [video.videoId]);

	const delay = useCallback(
		(ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),
		[]
	);

	const fetchVideoWithRetry = useCallback(async () => {
		if (!isMountedRef.current) return;

		abortControllerRef.current = new AbortController();

		// Store original auth header to restore later
		const originalAuthHeader =
			mainAPIClient.instance.defaults.headers.common["Authorization"];

		try {
			console.log("[VideoPlayer] Fetching video ID:", video.videoId);
			setIsLoading(true);

			// STEP 1: Try without authentication first (for free content)
			console.log(
				"[VideoPlayer] Trying without authentication first"
			);
			delete mainAPIClient.instance.defaults.headers.common[
				"Authorization"
			];

			if (video.isLive) {
				try {
					const accessResponse =
						await mainAPIClient.platforms.getEventAccess(
							video.videoId,
							{
								signal: abortControllerRef.current.signal,
							}
						);
					const eventData =
						accessResponse.data as EventAccessResponse;
					console.log(
						"[VideoPlayer] Free live event response:",
						eventData
					);

					if (eventData.Streams?.[0]?.url) {
						if (isMountedRef.current) {
							// Restore auth headers before returning
							if (originalAuthHeader) {
								mainAPIClient.instance.defaults.headers.common[
									"Authorization"
								] = originalAuthHeader;
							}
							setVideoAccess({
								url: eventData.Streams[0].url,
								urlMp4: undefined,
							});
							console.log(
								"[VideoPlayer] Free live stream access successful"
							);
							setIsLoading(false);
						}
						return;
					} else if (eventData.startTime) {
						if (isMountedRef.current) {
							// Restore auth headers before returning
							if (originalAuthHeader) {
								mainAPIClient.instance.defaults.headers.common[
									"Authorization"
								] = originalAuthHeader;
							}
							const targetDate = new Date(eventData.startTime);
							const now = new Date();
							const timeDifference =
								targetDate.getTime() - now.getTime();

							const hours = Math.floor(
								timeDifference / (1000 * 60 * 60)
							);
							const minutes = Math.floor(
								(timeDifference % (1000 * 60 * 60)) / (1000 * 60)
							);

							const formattedTime = `${String(hours).padStart(
								2,
								"0"
							)}:${String(minutes).padStart(2, "0")}`;
							setCustomStartTime(formattedTime);

							const countdownDuration = Math.max(
								0,
								Math.floor(timeDifference / 1000)
							);
							setCountdown(countdownDuration);
							console.log(
								"[VideoPlayer] Setting loading to false for upcoming stream"
							);
							setIsLoading(false);
						}
						return;
					}
				} catch (freeAccessError: any) {
					console.log(
						"[VideoPlayer] Free live access failed:",
						freeAccessError.response?.status
					);
					// Continue to try with authentication
				}
			} else {
				// Try free access for regular videos
				try {
					const accessResponse =
						await mainAPIClient.platforms.getVideoAccess(
							video.videoId,
							{
								signal: abortControllerRef.current.signal,
							}
						);
					const accessData =
						accessResponse.data as VideoAccessResponse;

					console.log("[VideoPlayer] Free video access response:", {
						status: accessResponse.status,
						data: accessData,
						hasUrl: !!accessData?.url,
						hasUrlMp4: !!accessData?.urlMp4,
					});

					if (
						isMountedRef.current &&
						accessData &&
						(accessData.url || accessData.urlMp4)
					) {
						// Restore auth headers before returning
						if (originalAuthHeader) {
							mainAPIClient.instance.defaults.headers.common[
								"Authorization"
							] = originalAuthHeader;
						}
						setVideoAccess({
							url: accessData.url,
							urlMp4: accessData.urlMp4,
						});
						console.log("[VideoPlayer] Free video access successful");
						setIsLoading(false);
						return;
					}
				} catch (freeAccessError: any) {
					console.log(
						"[VideoPlayer] Free access failed:",
						freeAccessError.response?.status
					);
					// Continue to try with authentication
				}
			}

			// STEP 2: If free access failed, try with authentication
			console.log(
				"[VideoPlayer] Free access failed, trying with authentication"
			);

			// Restore auth headers for authenticated request
			if (originalAuthHeader) {
				mainAPIClient.instance.defaults.headers.common[
					"Authorization"
				] = originalAuthHeader;
			}

			if (video.isLive) {
				const accessResponse =
					await mainAPIClient.platforms.getEventAccess(
						video.videoId,
						{
							signal: abortControllerRef.current.signal,
						}
					);
				const eventData = accessResponse.data as EventAccessResponse;
				console.log(
					"[VideoPlayer] Live event response (with auth):",
					eventData
				);

				if (eventData.Streams?.[0]?.url) {
					if (isMountedRef.current) {
						setVideoAccess({
							url: eventData.Streams[0].url,
							urlMp4: undefined,
						});
						console.log(
							"[VideoPlayer] Authenticated live stream access successful"
						);
						setIsLoading(false);
					}
					return;
				} else if (eventData.startTime) {
					if (isMountedRef.current) {
						const targetDate = new Date(eventData.startTime);
						const now = new Date();
						const timeDifference =
							targetDate.getTime() - now.getTime();

						const hours = Math.floor(
							timeDifference / (1000 * 60 * 60)
						);
						const minutes = Math.floor(
							(timeDifference % (1000 * 60 * 60)) / (1000 * 60)
						);

						const formattedTime = `${String(hours).padStart(
							2,
							"0"
						)}:${String(minutes).padStart(2, "0")}`;
						setCustomStartTime(formattedTime);

						const countdownDuration = Math.max(
							0,
							Math.floor(timeDifference / 1000)
						);
						setCountdown(countdownDuration);
						console.log(
							"[VideoPlayer] Setting loading to false for upcoming stream with auth"
						);
						setIsLoading(false);
					}
					return;
				}

				if (isMountedRef.current) {
					setError("This video is not available");
					console.log(
						"[VideoPlayer] Live stream not available even with auth"
					);
					setIsLoading(false);
				}
				return;
			}

			const accessResponse =
				await mainAPIClient.platforms.getVideoAccess(video.videoId, {
					signal: abortControllerRef.current.signal,
				});
			const accessData = accessResponse.data as VideoAccessResponse;

			console.log(
				"[VideoPlayer] Authenticated video access response:",
				{
					status: accessResponse.status,
					data: accessData,
					hasUrl: !!accessData?.url,
					hasUrlMp4: !!accessData?.urlMp4,
				}
			);

			if (
				isMountedRef.current &&
				accessData &&
				(accessData.url || accessData.urlMp4)
			) {
				setVideoAccess({
					url: accessData.url,
					urlMp4: accessData.urlMp4,
				});
				console.log(
					"[VideoPlayer] Authenticated video access successful"
				);
				setIsLoading(false);
				return;
			}

			if (isMountedRef.current) {
				setError("This video is not available");
				console.log(
					"[VideoPlayer] Video not available even with auth"
				);
				setIsLoading(false);
			}
		} catch (error: any) {
			// Always restore auth headers in case of error
			if (originalAuthHeader) {
				mainAPIClient.instance.defaults.headers.common[
					"Authorization"
				] = originalAuthHeader;
			}

			console.log(
				"[VideoPlayer] Error fetching video:",
				error.message
			);
			console.log("[VideoPlayer] Error details:", {
				message: error.message,
				status: error.response?.status,
				statusText: error.response?.statusText,
				data: error.response?.data,
			});

			if (error.name === "AbortError" || !isMountedRef.current) {
				console.log(
					"[VideoPlayer] Abort error or unmounted, setting loading to false"
				);
				setIsLoading(false);
				return;
			}

			if (video.isLive && retryCount < MAX_RETRIES) {
				if (isMountedRef.current) {
					setRetryCount((prev) => prev + 1);
				}
				await delay(RETRY_DELAY);
				if (isMountedRef.current) {
					fetchVideoWithRetry();
				}
				return;
			}

			if (isMountedRef.current) {
				setError("This video is not available");
				console.log(
					"[VideoPlayer] Setting loading to false after error in catch block"
				);
				setIsLoading(false);
			}
		}
	}, [video.videoId, video.isLive, retryCount, delay]);

	useEffect(() => {
		fetchVideoWithRetry();

		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [fetchVideoWithRetry]);

	const streamUrl = videoAccess?.url || videoAccess?.urlMp4 || null;

	const onBuffer = useCallback(
		({ isBuffering }: { isBuffering: boolean }) => {
			if (isMountedRef.current) {
				console.log("Buffer state changed:", {
					isBuffering,
					videoId: video.videoId,
					timestamp: new Date().toISOString(),
				});
				setIsBuffering(isBuffering);
			}
		},
		[video.videoId]
	);

	// Add useEffect to log buffering state changes
	useEffect(() => {
		console.log("Buffering state updated:", {
			isBuffering,
			videoId: video.videoId,
			timestamp: new Date().toISOString(),
		});
	}, [isBuffering, video.videoId]);

	// Add useEffect to log video ready state changes
	useEffect(() => {
		console.log("Video ready state updated:", {
			isVideoReady,
			videoId: video.videoId,
			timestamp: new Date().toISOString(),
		});
	}, [isVideoReady, video.videoId]);

	// Add useEffect to log playback started state changes and ensure loading spinner is hidden
	useEffect(() => {
		console.log("Playback started state updated:", {
			isPlaybackStarted,
			videoId: video.videoId,
			timestamp: new Date().toISOString(),
		});

		// When playback starts, ensure loading spinner is hidden
		if (isPlaybackStarted) {
			console.log(
				"[VideoPlayer] Playback started, hiding loading spinner"
			);
			setIsLoading(false);
			setIsBuffering(false); // Also ensure buffering overlay is hidden
		}
	}, [isPlaybackStarted, video.videoId]);

	// Add a failsafe timer to hide the loading spinner after a certain period
	// This ensures the spinner doesn't get stuck even if other state updates fail
	useEffect(() => {
		if (streamUrl && isLoading) {
			console.log(
				"[VideoPlayer] Setting up failsafe timer for loading spinner"
			);
			const failsafeTimer = setTimeout(() => {
				if (isMountedRef.current && isLoading) {
					console.log(
						"[VideoPlayer] Failsafe timer triggered - forcing loading spinner to hide"
					);
					setIsLoading(false);
				}
			}, 5000); // 5 seconds should be enough for most videos to start loading

			return () => clearTimeout(failsafeTimer);
		}
	}, [streamUrl, isLoading]);

	const onError = useCallback((error: any) => {
		console.log("Video Error:", error);
		setError("Error playing video");
		// Reset all states on error
		setIsBuffering(false);
		setIsLoading(false); // Ensure loading is set to false on error
		setIsVideoReady(false); // Reset video ready state on error
		setIsPlaybackStarted(false); // Reset playback started state on error
	}, []);

	// Handle video progress to detect when playback has actually started
	const onProgress = useCallback(
		(data: {
			currentTime: number;
			playableDuration: number;
			seekableDuration: number;
		}) => {
			// If we have actual playback progress and playback hasn't been marked as started yet
			// Using a very small threshold to detect even minimal playback progress
			if (data.currentTime > 0.1 && !isPlaybackStarted) {
				console.log("[VideoPlayer] Playback has started:", data);

				// Update all relevant states to ensure consistent UI
				setIsPlaybackStarted(true);
				setIsLoading(false);
				setIsBuffering(false);

				// Log the state change for debugging
				console.log(
					"[VideoPlayer] All loading states set to false due to playback progress"
				);
			}

			// Even if playback is already marked as started, ensure loading is false
			// This handles cases where the loading state might get out of sync
			if (data.currentTime > 0.1 && isLoading) {
				console.log(
					"[VideoPlayer] Playback is progressing but loading state is still true - fixing"
				);
				setIsLoading(false);
			}
		},
		[isPlaybackStarted, isLoading]
	);

	// Note: Video load events are handled directly in the Video component's onLoad prop

	const videoSource = useMemo(
		() => ({
			uri: streamUrl || "", // Provide empty string as fallback
			headers: {
				"x-account-key": "ByAWCu-i5",
				"User-Agent": "HandballTV/Android",
				Origin: "https://www.handballtv.fr",
				Referer: "https://www.handballtv.fr/",
			},
			type: video.isLive ? "m3u8" : undefined,
		}),
		[streamUrl, video.isLive]
	);

	const drmConfig = useMemo(
		() => ({
			type: Platform.OS === "android" ? DRMType.WIDEVINE : undefined,
			licenseServer: "https://widevine-license.onrewind.tv/proxy",
		}),
		[]
	);

	// Check for error state before rendering the video
	if (error) {
		if (video.isLive && countdown > 0) {
			return (
				<UpcomingLiveStream
					title={video.title}
					startTime={customStartTime || ""}
				/>
			);
		}
		return <ErrorState message={error} />;
	}

	// Show loading state while fetching
	// Only show loading state if we're still loading AND video hasn't started playing
	// Simplified condition to make it more reliable
	if (isLoading && !isPlaybackStarted) {
		console.log("[VideoPlayer] Showing loading state:", {
			isLoading,
			isVideoReady,
			isPlaybackStarted,
			videoId: video.videoId,
		});
		return (
			<LoadingState
				isLive={video.isLive}
				retryCount={retryCount}
			/>
		);
	}

	// Render the video player when the video is ready
	return (
		<View style={styles.container}>
			<View style={styles.videoContainer}>
				{Boolean(video.isLive) && <LiveIndicator />}
				<Video
					ref={videoRef}
					source={videoSource}
					drm={drmConfig}
					minLoadRetryCount={VIDEO_CONFIG.RETRY_COUNT}
					controls={true}
					onBuffer={onBuffer}
					onProgress={onProgress}
					muted={false}
					style={styles.backgroundVideo}
					resizeMode={video.isLive ? "cover" : "contain"}
					repeat={!video.isLive}
					playInBackground={!video.isLive}
					playWhenInactive={!video.isLive}
					onLoadStart={() => console.log("Loading URL:", streamUrl)}
					onLoad={(data) => {
						console.log(
							"[VideoPlayer] Video loaded successfully",
							data
						);

						// Update all relevant states in a single batch to avoid race conditions
						if (isMountedRef.current) {
							// Mark video as ready and hide loading spinner
							setIsVideoReady(true);
							setIsLoading(false);

							// If the video has a non-zero duration, it's likely ready to play
							if (data?.duration > 0) {
								console.log(
									"[VideoPlayer] Video has duration, should be ready to play"
								);
							}

							// If video loads but doesn't start playing immediately, we still want to hide the loading spinner
							// This helps with cases where the video is paused at the start but fully loaded
							setTimeout(() => {
								if (isMountedRef.current) {
									console.log(
										"[VideoPlayer] Forcing loading state to false after timeout"
									);
									setIsLoading(false);

									// If playback hasn't started after 2 seconds, force it to be considered started
									// This handles cases where onProgress might not fire correctly
									if (!isPlaybackStarted) {
										console.log(
											"[VideoPlayer] Forcing playback started state to true"
										);
										setIsPlaybackStarted(true);
									}
								}
							}, 2000); // Give a slightly longer delay to allow playback to start naturally
						}
					}}
					onError={(error) => {
						console.log(
							"Detailed error:",
							JSON.stringify(error, null, 2)
						);
						onError(error);
					}}
					// bufferConfig temporarily disabled - using API quality settings
					// bufferConfig={VIDEO_CONFIG.BUFFER_CONFIG}
				/>
				{isBuffering && (
					<View style={styles.bufferingOverlay}>
						<ActivityIndicator
							size="large"
							color="#fff"
						/>
					</View>
				)}
			</View>
		</View>
	);
};

export default memo(VideoPlayer);

const styles = StyleSheet.create({
	/**
	 * Core Layout Styles
	 * These styles define the main container and layout structure
	 * Uses flex layout for responsive video player positioning
	 */
	container: {
		flex: 1, // Makes the container fill the entire screen
		justifyContent: "center", // Centers content vertically
		alignItems: "center", // Centers content horizontally
		backgroundColor: "#000", // Black background for cinematic video viewing
	},
	loadingContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center", // Centers loading indicator and text
		backgroundColor: "#000",
	},
	videoContainer: {
		width: "100%", // Takes full width of the screen
		height: "100%", // Takes full height of the screen
		position: "relative", // Enables absolute positioning of overlays
	},

	/**
	 * Video Player Styles
	 * Core styling for the video component
	 * Ensures proper video sizing and positioning
	 */
	backgroundVideo: {
		width: "100%", // Takes full width of container
		height: "100%", // Takes full height of container
	},

	/**
	 * Live Indicator Styles
	 * Visual elements for live stream status
	 * Positioned overlay with distinct styling for visibility
	 */
	liveIndicatorContainer: {
		position: "absolute", // Positions over the video
		top: 16, // Spacing from top edge
		left: 16, // Spacing from left edge
		zIndex: 1, // Ensures overlay appears above video
	},
	liveIndicator: {
		backgroundColor: "#FF0000", // Bright red background for live status
		paddingHorizontal: 8, // Horizontal padding for text
		paddingVertical: 4, // Vertical padding for text
		borderRadius: 4, // Rounded corners for modern look
	},
	liveText: {
		color: "#FFFFFF", // White text for contrast
		fontWeight: "bold", // Bold text for emphasis
		fontSize: 12, // Appropriate size for overlay text
	},

	/**
	 * Status and Error Styles
	 * Text styles for various player states
	 * Includes loading and error message formatting
	 */
	errorContainer: {
		flex: 1,
		justifyContent: "center", // Center vertically
		alignItems: "center", // Center horizontally
		backgroundColor: "#000", // Dark background
		paddingHorizontal: 40, // Add horizontal padding for better layout
	},
	error: {
		color: "#fff", // White text
		fontSize: 18,
		fontWeight: "bold",
		textAlign: "center",
		marginBottom: 30, // Add space between error message and back button
	},
	loadingText: {
		color: "#fff", // White text for visibility
		marginTop: 20, // Spacing above loading text
		fontSize: 16, // Readable size for loading status
	},

	/**
	 * Back Button Styles for Error State
	 * Styles for the back button that appears in error states
	 * Includes focus states for tvOS navigation
	 */
	backButton: {
		backgroundColor: "rgba(255, 255, 255, 0.1)", // Semi-transparent background
		paddingHorizontal: 24,
		paddingVertical: 12,
		borderRadius: 8,
		borderWidth: 2,
		borderColor: "transparent", // Default transparent border
		minWidth: 120, // Minimum width for consistent button size
	},
	backButtonFocused: {
		backgroundColor: "rgba(255, 255, 255, 0.2)", // Brighter background when focused
		borderColor: "#fff", // White border when focused
		transform: [{ scale: 1.05 }], // Slightly larger when focused
	},
	backButtonText: {
		color: "#fff",
		fontSize: 16,
		fontWeight: "600",
		textAlign: "center",
	},
	backButtonTextFocused: {
		color: "#fff", // Keep white text when focused
		fontWeight: "bold", // Make text bolder when focused
	},

	/**
	 * Buffering Overlay Styles
	 * Overlay for showing loading state during video buffering
	 */
	bufferingOverlay: {
		...StyleSheet.absoluteFillObject, // Fills the entire container
		backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent background
		justifyContent: "center", // Centers loading indicator
		alignItems: "center", // Centers loading indicator
	},
	countdownText: {
		color: "#fff",
		fontSize: 18,
		fontWeight: "bold",
		marginTop: 10,
	},
	upcomingLiveContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		backgroundColor: "#000",
		paddingHorizontal: Platform.OS === "android" ? 10 : 20,
	},
	title: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 12 : 24,
		fontWeight: "bold",
		marginBottom: Platform.OS === "android" ? 5 : 10,
		textAlign: "center",
	},
	startTime: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 9 : 18,
		marginBottom: Platform.OS === "android" ? 2 : 5,
	},
	timeLeft: {
		color: "#fff",
		fontSize: Platform.OS === "android" ? 8 : 16,
		opacity: 0.8,
	},
});
