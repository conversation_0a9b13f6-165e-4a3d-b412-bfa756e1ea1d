import { KenticoAP<PERSON> } from "./apis/generated/kentico";

// token management
let authToken: string | null = null;

export const setAuthToken = (token: string | null) => {
	authToken = token;
	// Update headers when token changes
	kenticoAPIClient.instance.defaults.headers.common["Authorization"] =
		token ? `Bearer ${token}` : "";
};

export const getAuthToken = () => authToken;

// Creating a singleton instance of KenticoAPI
// #TODO rename 'service client' instead kentico
export const kenticoAPIClient = new KenticoAPI({
	baseURL: "https://cms-service.onrewind.tv",
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"x-account-key": "ByAWCu-i5",
	},
});
