{"openapi": "3.0.0", "info": {"title": "Video-stats-service", "description": "This is the Video-stats-service OpenAPI 3.0 specification.\n\nSome useful links:\n  - [The source API definition](/docs/spec)", "version": "1.0.0"}, "tags": [{"name": "auth", "description": "Endpoints related to authentication and health"}, {"name": "videos", "description": "Endpoints related to videos"}, {"name": "events", "description": "Endpoints related to events"}, {"name": "playlists", "description": "Endpoints related to playlists"}, {"name": "most-viewed", "description": "Endpoints related to the most-viewed videos and playlists"}], "paths": {"/fanVideos": {"get": {"tags": ["videos"], "summary": "return information about the video stats for a fan", "description": "If this fans doen't have statistics on the videos, it will return the video's total views.", "operationId": "indexFanVideo", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "videoId", "in": "query", "description": "video filter by id", "required": false, "schema": {"$ref": "#/components/schemas/UuidArray"}}, {"name": "limit", "in": "query", "description": "page limit, omitted if video ids is present", "required": false, "schema": {"$ref": "#/components/schemas/Limit"}}, {"name": "before", "in": "query", "description": "before cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "after", "in": "query", "description": "after cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the fan videos, including the total views", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanVideoEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/fanVideos/{videoId}": {"get": {"tags": ["videos"], "summary": "return information about the stats of one video for a fan", "description": "If this fans doen't have statistics on this video, it will return the video's views and the hitInterval", "operationId": "showFanVideo", "parameters": [{"name": "videoId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested video"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the fan videos, including the total views", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanVideoEntityWithPollingInfo"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["videos"], "summary": "add a hit to the video and save the status of the video playback", "description": "This endpoint will update the stats of the videos, including current viewers, total viewers, daily hits, among other.", "operationId": "fanVideoHit", "parameters": [{"name": "videoId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested video"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"201": {"description": "on success returns the hit interval", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Hit"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanVideoHit"}}}}}}, "/fanVideos/public/{videoId}": {"post": {"tags": ["videos"], "summary": "add a hit to the video and save the status of the video playback", "description": "This endpoint will update the stats of the videos, including current viewers, total viewers, daily hits, among other.", "operationId": "fanVideoHitPublic", "parameters": [{"name": "videoId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested video"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"201": {"description": "on success returns the hit interval", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Hit"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanVideoHit"}}}}}}, "/fanVideos/{videoId}/viewers": {"get": {"tags": ["videos"], "summary": "return the users playing this video in the last 5 secs", "description": "This endpoint count the number of viewers of the video in the last 5 seconds.", "operationId": "currentVideoViewers", "parameters": [{"name": "videoId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested video"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "viewers in the last 5 secs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentViewers"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/fanEvents": {"get": {"tags": ["events"], "summary": "return information about the event stats for a fan", "description": "This endpoint returns a array of fan events", "operationId": "indexFanEvent", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "eventId", "in": "query", "description": "event filter by id", "required": false, "schema": {"$ref": "#/components/schemas/UuidArray"}}, {"name": "limit", "in": "query", "description": "page limit, omitted if events ids is present", "required": false, "schema": {"$ref": "#/components/schemas/Limit"}}, {"name": "before", "in": "query", "description": "before cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "after", "in": "query", "description": "after cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the fan events", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanEventEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/fanEvents/{eventId}": {"get": {"tags": ["events"], "summary": "return information about the stats of one event for a fan", "description": "This endpoint includes the hitInterval in the response.", "operationId": "showFanEvent", "parameters": [{"name": "eventId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested event"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the fan event", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanEventEntityWithPollingInfo"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["events"], "summary": "add a hit to the event", "description": "This endpoint will update the stats of the event, including current viewers.", "operationId": "fanEventHit", "parameters": [{"name": "eventId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested event"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"201": {"description": "on success returns the hit interval", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Hit"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/fanEvents/login": {"post": {"tags": ["events"], "summary": "get a user token for unauthenticated users", "description": "This endpoint return a access token that you can use to count views on lives for not logged users.", "operationId": "fanEventLogin", "parameters": [{"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "on success returns the access token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FakeFanEventLogin"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/fanEvents/{eventId}/viewers": {"get": {"tags": ["events"], "summary": "return the users playing this event in the last 5 secs", "description": "This endpoint count the number of viewers of the event in the last 5 seconds.", "operationId": "currentEventViewers", "parameters": [{"name": "eventId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested event"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "viewers in the last 5 secs", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrentViewers"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/fanPlaylists": {"get": {"tags": ["playlists"], "summary": "return information about the playlists stats for a fan", "description": "This endpoint returns a array of fan playlists", "operationId": "indexFanPlaylists", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "playlistId", "in": "query", "description": "playlist filter by id", "required": false, "schema": {"$ref": "#/components/schemas/UuidArray"}}, {"name": "limit", "in": "query", "description": "page limit, omitted if playlist ids is present", "required": false, "schema": {"$ref": "#/components/schemas/Limit"}}, {"name": "before", "in": "query", "description": "before cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "after", "in": "query", "description": "after cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "array of playlists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedFanPlaylistEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/fanPlaylists/{playlistId}": {"get": {"tags": ["playlists"], "summary": "return information about the stats of one playlist for a fan", "description": "This endpoint includes the hitInterval in the response.", "operationId": "showFanPlaylist", "parameters": [{"name": "playlistId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested playlist"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "the fan playlists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanPlaylistEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}, "post": {"tags": ["playlists"], "summary": "add a hit to the playlist", "description": "This endpoint will update the stats of the playlist, including the current playlist rank.", "operationId": "fanPlaylistHit", "parameters": [{"name": "playlistId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested playlist"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"201": {"description": "on success returns not content"}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FanPlaylistHit"}}}}}}, "/videos/stats": {"get": {"tags": ["videos"], "summary": "return information about the video stats", "description": "returns a array of video stats.", "operationId": "indexVideoStats", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "videoId", "in": "query", "description": "video filter by id", "required": false, "schema": {"$ref": "#/components/schemas/UuidArray"}}, {"name": "limit", "in": "query", "description": "page limit, omitted if video ids is present", "required": false, "schema": {"$ref": "#/components/schemas/Limit"}}, {"name": "before", "in": "query", "description": "before cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "after", "in": "query", "description": "after cursor", "required": false, "schema": {"$ref": "#/components/schemas/Cursor"}}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return an array of video stats", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedVideoStatsEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/videos/stats/{videoId}": {"get": {"tags": ["videos"], "summary": "return information about requested video stats", "description": "returns a single entity.", "operationId": "showVideoStats", "parameters": [{"name": "videoId", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested video"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the video stats", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VideoStatsSchemaWithPollingInfo"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/events/{id}/player": {"get": {"tags": ["events"], "summary": "blind proxy to the main-api service", "description": "returns the requested player event and cache it by 15 seconds.", "operationId": "showEventPlayer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Uuid"}, "description": "id of the requested event"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "Return the main-api event", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MainApiEventEntity"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/mostViewed/{period}/videos": {"get": {"tags": ["most-viewed"], "summary": "returns the most viewed videos for the requested period", "description": "returns the most viewed videos for the requested period", "operationId": "showMostViewedVideos", "parameters": [{"name": "period", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Period"}, "description": "period"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "most viewed videos by period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShowMostViewedVideo"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/mostViewed/videos": {"get": {"tags": ["most-viewed"], "summary": "returns the most viewed videos for all valid periods", "description": "returns a list of most-vieved videos for each valid period", "operationId": "indexMostViewedVideos", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "most viewed videos", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShowMostViewedVideoArray"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/mostViewed/{period}/playlists": {"get": {"tags": ["most-viewed"], "summary": "returns the most viewed playlists for the requested period", "description": "returns the most viewed playlists for the requested period", "operationId": "showMostViewedPlaylists", "parameters": [{"name": "period", "in": "path", "required": true, "schema": {"$ref": "#/components/schemas/Period"}, "description": "period"}, {"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "most viewed playlists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShowMostViewedPlaylist"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/mostViewed/playlists": {"get": {"tags": ["most-viewed"], "summary": "returns the most viewed playlist for all valid periods", "description": "returns a list of most-vieved playlists for each valid period", "operationId": "indexMostViewedPlaylists", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"name": "x-service-name", "in": "header", "description": "the requester name", "required": false, "schema": {"$ref": "#/components/schemas/GenericString"}}], "responses": {"200": {"description": "most viewed playlists by period", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ShowMostViewedPlaylistArray"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/health": {"get": {"tags": ["auth"], "description": "useful to check the service health", "operationId": "getHealth", "responses": {"200": {"description": "The Service is up and healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StatusOK"}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "An token issued by users-service"}}, "parameters": {"AccountKey": {"name": "x-account-key", "in": "header", "description": "The client account key", "required": true, "schema": {"type": "string"}}}, "schemas": {"Error": {"type": "string"}, "Exception": {"type": "object", "properties": {"status": {"type": "integer"}, "type": {"type": "string"}, "message": {"type": "string"}}}, "StatusOK": {"type": "object", "properties": {"status": {"type": "string", "enum": ["OK"], "default": "OK"}}, "required": ["status"]}, "UuidArray": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "Limit": {"type": "integer", "minimum": 1, "maximum": 99}, "Cursor": {"type": "string", "nullable": true}, "Uuid": {"type": "string", "format": "uuid"}, "GenericString": {"type": "string"}, "FanVideoEntity": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "videoId": {"type": "string", "format": "uuid"}, "playlistId": {"type": "string", "nullable": true}, "marker": {"type": "number"}, "totalHits": {"type": "integer"}, "totalHitsSecs": {"type": "integer"}, "duration": {"type": "number"}, "updatedAt": {"type": "string"}, "views": {"type": "integer"}, "totalViews": {"type": "integer"}}, "required": ["accountId", "fanId", "videoId", "updatedAt"]}, "PagedFanVideoEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "videoId": {"type": "string", "format": "uuid"}, "playlistId": {"type": "string", "nullable": true}, "marker": {"type": "number"}, "totalHits": {"type": "integer"}, "totalHitsSecs": {"type": "integer"}, "duration": {"type": "number"}, "updatedAt": {"type": "string"}, "views": {"type": "integer"}}, "required": ["accountId", "fanId", "videoId", "updatedAt"]}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "FanVideoEntityWithPollingInfo": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "videoId": {"type": "string", "format": "uuid"}, "playlistId": {"type": "string", "nullable": true}, "marker": {"type": "number"}, "totalHits": {"type": "integer"}, "totalHitsSecs": {"type": "integer"}, "duration": {"type": "number"}, "updatedAt": {"type": "string"}, "views": {"type": "integer"}, "totalViews": {"type": "integer"}, "videoHitInterval": {"type": "number"}, "watchPercentToConsiderViewed": {"type": "number"}}, "required": ["videoHitInterval"]}, "FanVideoEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/FanVideoEntity"}}, "FanVideoHit": {"type": "object", "properties": {"marker": {"type": "number"}, "playlistId": {"type": "string", "nullable": true}}}, "Hit": {"type": "object", "properties": {"videoHitInterval": {"type": "number"}, "watchPercentToConsiderViewed": {"type": "number"}}, "required": ["videoHitInterval", "watchPercentToConsiderViewed"]}, "CurrentViewers": {"type": "object", "properties": {"viewers": {"type": "integer"}}, "required": ["viewers"]}, "FanEventEntity": {"type": "object", "properties": {"accountId": {"type": "string"}, "fanId": {"type": "string"}, "eventId": {"type": "string"}, "totalHits": {"type": "integer"}, "totalHitsSecs": {"type": "integer"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["accountId", "fanId", "eventId", "totalHits", "totalHitsSecs", "createdAt", "updatedAt"]}, "FakeFanEventLogin": {"type": "object", "properties": {"accessToken": {"type": "string"}}, "required": ["accessToken"]}, "PagedFanEventEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanEventEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "FanEventEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/FanEventEntity"}}, "FanEventEntityWithPollingInfo": {"type": "object", "properties": {"accountId": {"type": "string"}, "fanId": {"type": "string"}, "eventId": {"type": "string"}, "totalHits": {"type": "integer"}, "totalHitsSecs": {"type": "integer"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "videoHitInterval": {"type": "number"}}, "required": ["videoHitInterval"]}, "FanPlaylistHit": {"type": "object", "properties": {"currentVideoPlaylistRank": {"type": "number"}}}, "FanPlaylistEntity": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "fanId": {"type": "string", "format": "uuid"}, "playlistId": {"type": "string", "format": "uuid"}, "currentVideoPlaylistRank": {"type": "number"}, "updatedAt": {"type": "string"}}, "required": ["accountId", "fanId", "playlistId", "updatedAt"]}, "PagedFanPlaylistEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/FanPlaylistEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "FanPlaylistEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/FanPlaylistEntity"}}, "VideoStatsEntity": {"type": "object", "properties": {"accountId": {"type": "string"}, "videoId": {"type": "string"}, "playlistId": {"type": "string", "nullable": true, "format": "uuid"}, "views": {"type": "integer"}, "duration": {"type": "number"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}}, "required": ["accountId", "videoId", "views", "duration", "createdAt", "updatedAt"]}, "PagedVideoStatsEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoStatsEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "VideoStatsEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/VideoStatsEntity"}}, "VideoStatsSchemaWithPollingInfo": {"type": "object", "properties": {"accountId": {"type": "string"}, "videoId": {"type": "string"}, "playlistId": {"type": "string", "nullable": true, "format": "uuid"}, "views": {"type": "integer"}, "duration": {"type": "number"}, "createdAt": {"type": "string"}, "updatedAt": {"type": "string"}, "videoHitInterval": {"type": "number"}}, "required": ["videoHitInterval"]}, "MainApiVideoEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "description": {"type": "string", "nullable": true}, "duration": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "name": {"type": "string"}, "archiveData": {"nullable": true}, "captions": {"nullable": true}, "meta": {"nullable": true}, "poster": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "status": {"type": "string", "enum": ["none", "original", "in_progress", "encoded", "archived", "vendor"]}, "technicalDescription": {"type": "string", "nullable": true}, "PlaylistId": {"type": "string", "nullable": true, "format": "uuid"}, "playlistRank": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "url": {"type": "string", "nullable": true}, "vendorApiKey": {"type": "string", "nullable": true}, "vendorName": {"type": "string", "enum": ["jwplayer", "awsplayer", "dailymotion", "youtube", "twitch"]}, "vendorVideoId": {"type": "string", "nullable": true}, "visibility": {"type": "string", "enum": ["public", "private"]}, "isPaid": {"type": "boolean"}, "marker": {"type": "number"}, "isNew": {"type": "boolean"}, "hasBeenViewed": {"type": "boolean"}, "ratio": {"type": "string", "nullable": true, "enum": ["sixteen-nine", "nine-sixteen", null]}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOfferEntity"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiTagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiCategoryEntity"}}, "VideoCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoCategorySubCategoryEntity"}}}, "required": ["id", "AccountId", "description", "duration", "filename", "fullDescription", "name", "poster", "portrait<PERSON><PERSON><PERSON><PERSON>", "publicationDate", "status", "technicalDescription", "PlaylistId", "playlistRank", "updatedAt", "createdAt", "url", "vendorApiKey", "vendorName", "vendorVideoId", "visibility", "isPaid", "marker", "isNew", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ratio"]}, "PagedMainApiVideoEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "PagedMainApiVideoEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}]}, "MainApiStreamEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "isRecordReady": {"type": "boolean"}, "key": {"type": "string"}, "mapCoordinates": {"nullable": true}, "name": {"type": "string", "nullable": true}, "offset": {"type": "number", "nullable": true}, "options": {"nullable": true}, "recordName": {"type": "string", "nullable": true}, "startedAt": {"type": "string", "format": "date-time"}, "streamable": {"type": "string", "nullable": true}, "streamableId": {"type": "string", "nullable": true}, "streamType": {"type": "string", "nullable": true, "enum": ["main", "backup", "additionnal", null]}, "token": {"type": "string"}, "url": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}, "MainApiCompetitionEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "slug": {"type": "string", "nullable": true, "maxLength": 255}, "displayOrder": {"type": "integer", "nullable": true}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "SportId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiTagEntity"}}}, "required": ["id", "name"]}, "PagedMainApiCompetitionEntityArrayAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiCompetitionEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiCompetitionEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}]}, "MainApiEventEntity": {"type": "object", "properties": {"AccountId": {"type": "string", "nullable": true}, "activatedModules": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "advertisingBanner": {"nullable": true}, "calendarEventId": {"type": "string", "nullable": true, "format": "uuid"}, "ChatId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "dailymotionLiveStreamId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "facebookPlaylistId": {"type": "string", "nullable": true}, "geoBlockingMapping": {"type": "object", "nullable": true, "properties": {"whitelist": {"type": "array", "items": {"type": "string"}}, "blacklist": {"type": "array", "items": {"type": "string"}}}}, "fullDescription": {"type": "string", "nullable": true}, "hashtag": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "location": {"type": "string", "nullable": true}, "name": {"type": "string"}, "options": {"nullable": true}, "organiserName": {"type": "string", "nullable": true}, "placeholder": {"type": "object", "nullable": true, "properties": {"poster": {"type": "string"}, "url": {"type": "string"}}}, "ParentId": {"type": "string", "nullable": true}, "refereeName": {"type": "string", "nullable": true}, "score": {"type": "object", "nullable": true, "properties": {"teamIn": {"type": "string", "nullable": true}, "teamOut": {"type": "string", "nullable": true}, "scoreIn": {"type": "string", "nullable": true}, "scoreOut": {"type": "string", "nullable": true}}}, "RoundId": {"type": "string", "nullable": true}, "shareUrl": {"type": "string", "nullable": true}, "SportId": {"type": "string", "format": "uuid"}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "state": {"type": "string", "nullable": true, "enum": ["liveOn", "liveOff", "replay", "liveDailymotion", "liveYoutube", "awsLive", null]}, "stats": {"nullable": true}, "step": {"type": "string", "nullable": true, "enum": ["starting", "break", "end", null]}, "streamIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "TeamClientId": {"type": "string", "nullable": true}, "ThemeId": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "VideoId": {"type": "string", "nullable": true}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private", null]}, "youtubeLiveStreamId": {"type": "string", "nullable": true}, "isPaid": {"type": "boolean"}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, "Challengers": {"type": "array", "items": {"type": "object", "properties": {"birthday": {"type": "string", "nullable": true, "format": "date-time"}, "coachName": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "firstName": {"type": "string", "nullable": true}, "gamesPlayed": {"type": "number", "nullable": true}, "gender": {"type": "string", "nullable": true, "enum": ["standard", "team", "teammate", null]}, "goals": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "history": {"type": "string", "nullable": true}, "homeFieldName": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "isJunior": {"type": "boolean", "nullable": true}, "jerseyNumber": {"type": "number", "nullable": true}, "jerseyPicture": {"type": "string", "nullable": true}, "linkShop": {"type": "string", "nullable": true}, "linkStats": {"type": "string", "nullable": true}, "name": {"type": "string"}, "optaId": {"type": "number", "nullable": true}, "picture": {"type": "string", "nullable": true}, "pictureUrl": {"type": "string", "nullable": true}, "profileOptions": {"nullable": true}, "providerId": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "shortName": {"type": "string", "nullable": true}, "shots": {"type": "number", "nullable": true}, "smallPictureUrl": {"type": "string", "nullable": true}, "SportId": {"type": "string", "nullable": true}, "statsId": {"type": "number", "nullable": true}, "targetedShots": {"type": "number", "nullable": true}, "TeamId": {"type": "string", "nullable": true, "format": "uuid"}, "type": {"type": "string", "nullable": true, "enum": ["F", "M", "MIXTE", null]}, "updatedAt": {"type": "string", "format": "date-time"}, "weight": {"type": "number", "nullable": true}}, "required": ["birthday", "<PERSON><PERSON><PERSON>", "country", "createdAt", "firstName", "gamesPlayed", "gender", "goals", "height", "history", "homeFieldName", "id", "is<PERSON><PERSON><PERSON>", "jersey<PERSON><PERSON>ber", "jerseyPicture", "linkShop", "linkStats", "name", "optaId", "picture", "pictureUrl", "providerId", "role", "shortName", "shots", "smallPictureUrl", "SportId", "statsId", "targetedShots", "TeamId", "type", "updatedAt", "weight"]}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOfferEntity"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiTagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiCategoryEntity"}}, "EventCategorySubCategories": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "SubCategoryId": {"type": "string", "format": "uuid"}, "EventId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "SubCategory": {"$ref": "#/components/schemas/MainApiSubCategoryEntity"}}, "required": ["id", "CategoryId", "SubCategoryId", "EventId", "createdAt", "updatedAt"]}}, "Streams": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiStreamEntity"}}, {"type": "array", "items": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "streamType": {"type": "string"}, "awsStream": {"type": "string", "nullable": true, "enum": ["STANDBY", "ONAIR", "ARCHIVED", null]}}, "required": ["url", "streamType", "awsStream"]}}]}, "Round": {"type": "object", "properties": {"CompetitionId": {"type": "string", "format": "uuid"}, "competitionOrder": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "SeasonId": {"type": "string", "format": "uuid"}, "stage": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "Competition": {"$ref": "#/components/schemas/MainApiCompetitionEntity"}}, "required": ["CompetitionId", "competitionOrder", "createdAt", "id", "name", "SeasonId", "updatedAt"]}, "Sport": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "periods": {"type": "string", "nullable": true, "enum": ["multiple", "none", null]}, "sportsFieldFilename": {"type": "string", "nullable": true}, "svgSpriteFilename": {"type": "string", "nullable": true}, "timelineType": {"type": "string", "nullable": true, "enum": ["single", "double", null]}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "id", "name", "periods", "sportsFieldFilename", "svgSpriteFilename", "timelineType", "updatedAt"]}, "Markers": {"type": "array", "items": {"type": "object", "properties": {"ChallengerId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "dailymotionRepostId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endTime": {"type": "number"}, "endTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "EventId": {"type": "string", "nullable": true, "format": "uuid"}, "facebookVideoId": {"type": "string", "nullable": true}, "gameTime": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "MarkerTypeId": {"type": "string", "nullable": true, "format": "uuid"}, "notifications": {"nullable": true}, "options": {"nullable": true}, "ParentId": {"type": "string", "nullable": true, "format": "uuid"}, "shouldDisplayChallengerProfile": {"type": "boolean", "nullable": true}, "startTime": {"type": "number"}, "startTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["ChallengerId", "createdAt", "dailymotionRepostId", "description", "endTime", "endTimeTs", "EventId", "facebookVideoId", "gameTime", "id", "MarkerTypeId", "ParentId", "shouldDisplayChallengerProfile", "startTime", "startTimeTs", "updatedAt"]}}, "MarkerTypes": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "defaultTimeEnd": {"type": "integer", "nullable": true}, "defaultTimeStart": {"type": "integer", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "nullable": true}, "endGameMinute": {"type": "integer", "nullable": true}, "ico": {"type": "string", "nullable": true}, "icoDark": {"type": "string", "nullable": true}, "icoSettings": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "isMain": {"type": "boolean", "nullable": true}, "name": {"type": "string"}, "optaOffsetEnd": {"type": "integer", "nullable": true}, "optaOffsetStart": {"type": "integer", "nullable": true}, "pushNotification": {"type": "boolean", "nullable": true}, "shortcut": {"type": "string", "nullable": true}, "SportId": {"type": "string", "nullable": true, "format": "uuid"}, "startGameMinute": {"type": "integer", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "defaultTimeEnd", "defaultTimeStart", "description", "displayOrder", "endGameMinute", "ico", "icoDark", "icoSettings", "id", "is<PERSON><PERSON>", "name", "optaOffsetEnd", "optaOffsetStart", "pushNotification", "shortcut", "SportId", "startGameMinute", "updatedAt"]}}, "Video": {"$ref": "#/components/schemas/MainApiVideoEntity"}, "Theme": {"type": "object", "properties": {"AccountId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "customMarkerTypes": {"nullable": true}, "customMarkerTypesSvgSprite": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "style": {}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["AccountId", "createdAt", "customMarkerTypesSvgSprite", "id", "name", "updatedAt"]}, "EventAttachments": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "EventId": {"type": "string", "format": "uuid"}, "ext": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}, "required": ["createdAt", "EventId", "ext", "fileName", "id", "name", "type", "updatedAt", "url"]}}, "translations": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "languageId": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "modelId": {"type": "string", "nullable": true}, "name": {"type": "string"}, "refereeName": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "description", "fullDescription", "id", "languageId", "location", "modelId", "name", "<PERSON><PERSON><PERSON>", "updatedAt"]}}}, "required": ["AccountId", "activatedModules", "calendarEventId", "ChatId", "createdAt", "dailymotionLiveStreamId", "description", "endDate", "facebookPlaylistId", "geoBlockingMapping", "fullDescription", "hashtag", "id", "location", "name", "organiserName", "placeholder", "ParentId", "<PERSON><PERSON><PERSON>", "score", "RoundId", "shareUrl", "SportId", "startDate", "state", "step", "streamIds", "TeamClientId", "ThemeId", "updatedAt", "VideoId", "visibility", "youtubeLiveStreamId", "Streams", "Round", "Sport"]}, "PagedMainApiEventEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiEventEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "PagedMainApiEventEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiEventEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiEventEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}]}, "MainApiTagEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tagType": {"type": "string", "nullable": true}, "name": {"type": "string"}, "options": {"nullable": true}, "AccountId": {"type": "string", "format": "uuid"}, "placeholders": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ItemTag": {"$ref": "#/components/schemas/MainApiItemTagEntity"}, "PaymentOffers": {"$ref": "#/components/schemas/PaymentOfferEntity"}}, "required": ["id", "tagType", "name", "AccountId", "placeholders", "createdAt", "updatedAt"]}, "MainApiItemTagEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tagId": {"type": "string", "format": "uuid"}, "taggable": {"type": "string"}, "taggableId": {"type": "string", "format": "uuid"}}, "required": ["id", "tagId", "taggable", "taggableId"]}, "MainApiCategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "isFavorite": {"type": "boolean"}, "heroPortrait": {"type": "string"}, "heroLandscape": {"type": "string"}}, "required": ["id", "name", "accountId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "createdAt", "updatedAt"]}, "MainApiCategoryEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiCategoryEntity"}}, "MainApiVideoCategorySubCategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "SubCategoryId": {"type": "string", "format": "uuid"}, "VideoId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "SubCategory": {"$ref": "#/components/schemas/MainApiSubCategoryEntity"}}, "required": ["id", "CategoryId", "SubCategoryId", "VideoId", "createdAt", "updatedAt"]}, "MainApiSubCategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "createdAt", "updatedAt"]}, "MainApiSubCategoryEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiSubCategoryEntity"}}, "MainApiPlaylistEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Videos": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiVideoEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}]}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "PagedMainApiPlaylistEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiPlaylistEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}, "PagedMainApiPlaylistEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/MainApiPlaylistEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/MainApiPlaylistEntity"}}, "cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}}, "required": ["items", "cursor"]}]}, "PaymentProduct": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["Product"], "default": "Product"}, "accountId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"description": {"type": "string"}, "weight": {"type": "string"}}}, "isStandard": {"type": "boolean"}, "_type": {"type": "string", "enum": ["Product"], "default": "Product"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "paymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOffer"}}, "profiles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["payment", "marketing", "men-favorite-club", "women-favorite-club", "club-subscriber", null]}, "category": {"type": "string", "nullable": true, "enum": ["communication", "competition", "favorite-men-club", "favorite-women-club", "premium", null]}, "providerId": {"type": "string"}, "AccountId": {"type": "string", "format": "uuid"}, "retainOnLogin": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "type", "category", "providerId", "AccountId", "retainOnLogin", "createdAt", "updatedAt"]}}}, "required": ["accountId", "id", "productId", "status", "name", "created", "updated"]}, "PaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["PaymentOffer"], "default": "PaymentOffer"}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "providerPaymentOfferId": {"type": "string"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"isLimitedDuration": {"type": "boolean"}, "population": {"type": "string"}, "weigth": {"type": "string"}, "type": {"type": "string", "enum": ["recurring", "one_time"]}, "price": {"type": "number"}, "currency": {"type": "string"}, "interval": {"type": "string", "enum": ["month", "year", "week", "day"]}, "intervalCount": {"type": "number"}}}, "isPPV": {"type": "boolean"}, "_type": {"type": "string", "enum": ["PaymentOffer"], "default": "PaymentOffer"}, "duration": {"type": "number"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "associatedProfiles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["payment", "marketing", "men-favorite-club", "women-favorite-club", "club-subscriber", null]}, "category": {"type": "string", "nullable": true, "enum": ["communication", "competition", "favorite-men-club", "favorite-women-club", "premium", null]}, "providerId": {"type": "string"}, "AccountId": {"type": "string", "format": "uuid"}, "retainOnLogin": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "type", "category", "providerId", "AccountId", "retainOnLogin", "createdAt", "updatedAt"]}}}, "required": ["accountId", "productId", "paymentOfferId", "provider", "providerPaymentOfferId", "name", "created", "updated"]}, "PaymentOfferEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "paymentOfferType": {"type": "string", "enum": ["sku", "plan"]}, "name": {"type": "string"}, "stripeId": {"type": "string", "nullable": true}, "appleId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "AccountId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "weight": {"type": "number"}}, "required": ["id", "paymentOfferType", "name", "stripeId", "appleId", "AccountId", "createdAt", "updatedAt"]}, "Period": {"type": "string", "enum": ["daily", "weekly", "monthly", "quarterly", "semestral", "yearly"]}, "MostViewedVideo": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "points": {"type": "integer", "minimum": 0}}, "required": ["id", "points"]}, "ShowMostViewedVideo": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "period": {"$ref": "#/components/schemas/Period"}, "updatedAt": {"type": "string"}, "videos": {"type": "array", "items": {"$ref": "#/components/schemas/MostViewedVideo"}}}, "required": ["accountId", "period", "updatedAt", "videos"]}, "ShowMostViewedVideoArray": {"type": "array", "items": {"$ref": "#/components/schemas/ShowMostViewedVideo"}}, "MostViewedPlaylist": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "points": {"type": "integer", "minimum": 0}}, "required": ["id", "points"]}, "ShowMostViewedPlaylist": {"type": "object", "properties": {"accountId": {"type": "string", "format": "uuid"}, "period": {"$ref": "#/components/schemas/Period"}, "updatedAt": {"type": "string"}, "playlists": {"type": "array", "items": {"$ref": "#/components/schemas/MostViewedVideo"}}}, "required": ["accountId", "period", "updatedAt", "playlists"]}, "ShowMostViewedPlaylistArray": {"type": "array", "items": {"$ref": "#/components/schemas/ShowMostViewedPlaylist"}}}}}