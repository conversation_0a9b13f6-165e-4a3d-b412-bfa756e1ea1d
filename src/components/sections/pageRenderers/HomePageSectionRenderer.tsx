import React from "react";
import { View } from "react-native";
import {
	SectionRendererProps,
	SectionType,
} from "../../../types/sectionTypes";
import { GLOBAL_STYLES } from "../../../styles/globalStyles";
import {
	AdSec<PERSON><PERSON>enderer,
	StaticCarouselSectionRenderer,
	LiveSectionRenderer,
	DynamicCarouselSectionRenderer,
	DynamicGridWithCategorySectionRenderer,
	FeaturedVideoSectionRenderer,
} from "../renderers";

/**
 * HomePageSectionRenderer
 *
 * A universal section renderer for the Home Page that handles these section types:
 * - section_static_carousel: For displaying fixed content like featured videos, categories, and competitions
 * - section_dynamic_live: For displaying live matches
 * - section_dynamic_carousel: For displaying dynamic content
 * - section_static_ad: For displaying advertisements
 * - section_dynamic_grid_with_category: For displaying content with category filtering
 *
 * This renderer works for all home page sections, using the same renderers as the competition page
 * but with specific handling for home page section codenames.
 */
const HomePageSectionRenderer: React.FC<SectionRendererProps> = ({
	section,
	onItemPress,
	upcomingEvents = [],
}) => {
	// Log section information
	// console.log(
	// 	`Rendering home section: ${section._kenticoItemType} (${section._kenticoCodename})`
	// );

	// Handle null or undefined section
	if (!section) {
		console.warn("Received null or undefined section");
		return null;
	}

	try {
		// Special case: Featured video section at the top of home page
		// These sections typically have codenames like "home___new_slider"
		if (
			section._kenticoItemType === SectionType.STATIC_CAROUSEL &&
			section._kenticoCodename &&
			(section._kenticoCodename.includes("slider") ||
				section._kenticoCodename.includes("___new_") ||
				section._kenticoCodename.includes("___hero"))
		) {
			return (
				<FeaturedVideoSectionRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		}

		// Special case: Competitions section
		// After rendering competitions, we also want to render the live/upcoming section
		if (
			section._kenticoItemType === SectionType.STATIC_CAROUSEL &&
			section._kenticoCodename === "grid_home_page___competitions"
		) {
			// Check if we have any upcoming events to display
			const hasUpcomingEvents =
				upcomingEvents && upcomingEvents.length > 0;

			// Create a live section data object if we have upcoming events
			// Cast as any to avoid TypeScript errors with missing properties
			const liveSectionData: any = {
				_kenticoItemType: SectionType.DYNAMIC_LIVE,
				_kenticoCodename: "home_page___upcoming_matches",
				_kenticoId: "home_page_upcoming_matches",
				_kenticoLanguage: "en",
				title: "Coming lives",
				items: [], // We'll pass the upcoming events separately
				// Add required properties to satisfy the LiveCarouselDynamicSection type
				type: "section_dynamic_live",
				redirectionPath: null,
				mobileRedirection: null,
				params: null,
			};

			// If we have upcoming events, render both the competitions section and the live section
			if (hasUpcomingEvents) {
				return (
					<View
						style={{
							marginBottom: GLOBAL_STYLES.SECTION_MARGIN_BOTTOM,
						}}
					>
						{/* First render the competitions section without bottom margin */}
						<View style={{ marginBottom: 0 }}>
							<StaticCarouselSectionRenderer
								data={section}
								onPress={onItemPress}
							/>
						</View>

						{/* Then render the live/upcoming section without bottom margin */}
						<View style={{ marginBottom: 0 }}>
							<LiveSectionRenderer
								data={liveSectionData}
								onPress={onItemPress}
								upcomingEvents={upcomingEvents} // Pass upcoming events directly
							/>
						</View>
					</View>
				);
			}

			// If no upcoming events, just render the competitions section
			return (
				<StaticCarouselSectionRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		}

		// Special case: Categories section
		if (
			section._kenticoItemType === SectionType.STATIC_CAROUSEL &&
			section._kenticoCodename === "home_page___grid_categories"
		) {
			return (
				<StaticCarouselSectionRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		}

		// Main switch case to handle all section types
		switch (section._kenticoItemType) {
			case SectionType.STATIC_CAROUSEL:
				// For static carousels (featured videos, categories, etc.)
				return (
					<StaticCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);

			case SectionType.DYNAMIC_LIVE:
				// For live matches - check if this is a section from the API or our custom section
				// If it's a section from the API with a specific codename, don't render it
				// as we're already rendering a custom live section after competitions
				if (
					section._kenticoCodename === "home_page___coming_lives" ||
					section._kenticoCodename === "home_page___upcoming_matches"
				) {
					console.log("Skipping duplicate live section from API");
					return null;
				}

				// For other live sections, render them without upcoming events
				// to avoid duplicates with our custom section
				return (
					<LiveSectionRenderer
						data={section}
						onPress={onItemPress}
						// Don't pass upcomingEvents here to avoid duplicates
					/>
				);

			case SectionType.DYNAMIC_CAROUSEL:
				// For dynamic content carousels
				return (
					<DynamicCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);

			case SectionType.STATIC_AD:
				// For advertisements
				return (
					<AdSectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);

			case SectionType.DYNAMIC_GRID_WITH_CATEGORY:
				// For grid-based content with category filtering
				return (
					<DynamicGridWithCategorySectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);

			default:
				// For unknown section types, use StaticCarouselSectionRenderer as a fallback
				console.warn(
					`Using default renderer for unknown section type: ${section._kenticoItemType} (${section._kenticoCodename})`
				);
				return (
					<StaticCarouselSectionRenderer
						data={section}
						onPress={onItemPress}
					/>
				);
		}
	} catch (error) {
		// Error handling to prevent crashes
		console.error(
			`Error rendering section ${section._kenticoCodename}:`,
			error
		);
		// Return null in case of error to prevent app crash
		return null;
	}
};

export default React.memo(HomePageSectionRenderer);
