/**
 * VideoDescription Component
 *
 * This component displays detailed information about a video, including:
 * - Title
 * - Share/Favorite buttons
 * - Access status
 * - Categories (as tags)
 * - Subcategories (as hashtags)
 * - Description
 */

// Basic React Native UI components
import { StyleSheet, Text, View, Pressable } from "react-native";
import React, { useEffect, useState } from "react";
// Navigation utilities from React Navigation
import { useRoute } from "@react-navigation/native";
import type { RouteProp } from "@react-navigation/native";
// Type definitions for navigation
import { RootStackParamList } from "../../app/index";
// API client for making HTTP requests
import { mainAPIClient } from "../../utils/mainApiInstance";
import { scale } from "../../utils/helpers/dimensionScale.helper";
// Type definitions for data structures
import type {
	Video,
	Category,
	SubCategory,
} from "../../utils/apis/generated/main_api";

/**
 * Type definition for the route parameters.
 * This comes from the RootStackParamList in app/index.tsx where the VideoPlayer screen
 * is defined with a video parameter of type:
 * {
 *   videoId: string;
 *   title: string;
 *   thumbnail: string;
 *   description?: string;
 *   ratio?: string;
 *   isLive?: boolean;
 * }
 */
type VideoPlayerRouteProp = RouteProp<
	RootStackParamList,
	"VideoPlayer"
>;

interface VideoDescriptionProps {
	video: {
		videoId: string;
		title: string;
		thumbnail: string;
		description?: string;
		isLive?: boolean;
		startTime?: string;
	};
}

const VideoDescription = ({
	video: initialVideo,
}: VideoDescriptionProps) => {
	/**
	 * State Management
	 * Use three separate states to manage different aspects of data:
	 */
	// Stores the complete video data from the API
	const [videoData, setVideoData] = useState<Video | null>(null);
	// Stores the list of categories for tag display
	const [categories, setCategories] = useState<Category[]>([]);
	// Stores the list of subcategories for hashtag display
	const [subCategories, setSubCategories] = useState<SubCategory[]>(
		[]
	);

	/**
	 * Data Fetching Effect
	 * This effect runs when the component mounts or when videoId changes
	 * It fetches the complete video data from the API using the videoId
	 * from initial navigation parameters
	 */
	useEffect(() => {
		const fetchVideoData = async () => {
			try {
				// First check if this is an upcoming event
				try {
					const eventResponse =
						await mainAPIClient.platforms.getEventById(
							initialVideo.videoId
						);
					const eventData = eventResponse.data;

					if (eventData.startDate) {
						const targetDate = new Date(eventData.startDate);
						const now = new Date();
						const timeDifference =
							targetDate.getTime() - now.getTime();

						if (timeDifference > 0) {
							// This is an upcoming event, don't try to fetch video data
							return;
						}
					}
				} catch (eventError: any) {
					// Silently handle event check errors for live content
					if (initialVideo.isLive) {
						return;
					}
				}

				// Only proceed with video data fetch if not an upcoming event
				// Fetch complete video data using the videoId
				const response = await mainAPIClient.platforms.getVideoById(
					initialVideo.videoId
				);
				// console.log("Complete video data from API:", response.data);

				// Update video data state
				setVideoData(response.data);

				// If there is categories in the response, update categories state
				if (response.data.Categories) {
					setCategories(response.data.Categories);
				}

				// If subcategories, update subcategories state
				if (response.data.SubCategories) {
					setSubCategories(response.data.SubCategories);
				}
			} catch (error: any) {
				// Suppress error logging for live content 404s
				if (
					!(initialVideo.isLive && error?.response?.status === 404)
				) {
					console.error("Error fetching video data:", error);
				}
			}
		};

		// Execute fetch function
		fetchVideoData();
	}, [initialVideo.videoId, initialVideo.isLive]); // Only re-run if videoId changes

	/**
	 * Date Formatting
	 * Will be used for showing countdown
	 */
	const date = new Date();
	const formattedDate = date.toLocaleDateString("fr-FR", {
		day: "numeric",
		month: "long",
		year: "numeric",
	});

	/**
	 * Component Render
	 * The UI is structured in sections:
	 * 1. Header (title + action buttons)
	 * 2. Access information
	 * 3. Categories (tags)
	 * 4. Subcategories (hashtags)
	 * 5. Description
	 */
	return (
		<View style={styles.container}>
			{/* Header Section */}
			<View style={styles.header}>
				{/* Title with fallback chain:
					1. Use API data name
					2. Fallback to initial video title
					3. Final fallback to default text */}
				<Text style={styles.title}>
					{videoData?.name || initialVideo.title || "No title"}
				</Text>
				{/* Action Buttons */}
				{/* <View style={styles.headerButtons}>
					<Pressable style={styles.shareButton}>
						<Text style={styles.shareButtonText}>🔗 Share</Text>
					</Pressable>
					<Pressable style={styles.favoriteButton}>
						<Text style={styles.favoriteButtonText}>⭐️</Text>
					</Pressable>
				</View> */}
			</View>

			{/* Access Information */}
			<View style={styles.lockInfo}>
				<Text style={styles.lockText}>
					🔓 Accessible via pass Pass Abonné.
				</Text>
			</View>

			{/* Categories Section - Displayed as tags */}
			<View style={styles.tagContainer}>
				{categories.map((category, index) => (
					<View
						key={category.id || index}
						style={styles.tag}
					>
						<Text style={styles.tagText}>{category.name}</Text>
					</View>
				))}
			</View>

			{/* Subcategories Section - Displayed as hashtags */}
			<View style={styles.hashtagContainer}>
				{subCategories.map((subCategory, index) => (
					<View
						key={subCategory.id || index}
						style={styles.hashtag}
					>
						<Text style={styles.hashtagText}>{subCategory.name}</Text>
					</View>
				))}
			</View>

			{/* Description Section with fallback chain */}
			<Text
				style={styles.description}
				numberOfLines={4} // Limit to 4 lines to prevent overflow
				ellipsizeMode="tail" // Add ellipsis at the end if text is truncated
			>
				{videoData?.description ||
					initialVideo.description ||
					"No description"}
			</Text>
		</View>
	);
};

export default VideoDescription;

/**
 * Styles
 * The component uses a consistent styling approach:
 * - Dark theme with light text
 * - Proper spacing and padding
 * - Different visual treatments for tags vs hashtags
 * - Responsive layout using flex
 */
const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 0,
		justifyContent: "flex-start",
	},
	header: {
		flexDirection: "row",
		justifyContent: "space-between",
		alignItems: "flex-start",
		marginBottom: scale(16),
	},
	title: {
		fontSize: scale(36),
		fontWeight: "bold",
		color: "#FFFFFF",
		flex: 1,
	},
	headerButtons: {
		flexDirection: "row",
		gap: scale(12),
	},
	shareButton: {
		backgroundColor: "#FF0000",
		paddingHorizontal: scale(32),
		paddingVertical: scale(20),
		borderRadius: scale(12),
	},
	shareButtonText: {
		color: "#FFFFFF",
		fontWeight: "600",
		fontSize: scale(24),
	},
	favoriteButton: {
		borderWidth: scale(3),
		borderColor: "#FFFFFF",
		paddingHorizontal: scale(24),
		paddingVertical: scale(20),
		borderRadius: scale(12),
	},
	favoriteButtonText: {
		fontSize: scale(24),
	},
	lockInfo: {
		marginBottom: scale(16),
	},
	lockText: {
		color: "#4ADE80",
		fontSize: scale(20),
	},
	tagContainer: {
		flexDirection: "row",
		flexWrap: "wrap",
		gap: scale(8),
		marginBottom: scale(16),
	},
	tag: {
		backgroundColor: "rgba(255, 255, 255, 0.1)",
		paddingHorizontal: scale(16),
		paddingVertical: scale(8),
		borderRadius: scale(24),
	},
	tagText: {
		color: "#FFFFFF",
		fontSize: scale(18),
		fontWeight: "500",
	},
	hashtagContainer: {
		flexDirection: "row",
		flexWrap: "wrap",
		gap: scale(8),
		marginBottom: scale(16),
	},
	hashtag: {
		backgroundColor: "transparent",
		paddingHorizontal: scale(14),
		paddingVertical: scale(6),
		borderRadius: scale(20),
		borderWidth: scale(2),
		borderColor: "rgba(255, 255, 255, 0.3)",
	},
	hashtagText: {
		color: "#FFFFFF",
		fontSize: scale(16),
		opacity: 0.8,
	},
	description: {
		fontSize: scale(20),
		color: "#FFFFFF",
		opacity: 0.7,
		lineHeight: scale(28),
		maxHeight: scale(112), // Limit height to 4 lines (28 * 4 = 112)
		overflow: "hidden", // Ensure text doesn't overflow the container
	},
});

/**
 * Data Flow Summary:
 * 1. User navigates to VideoPlayer screen with initial video data
 * 2. Component receives this data through route.params
 * 3. Component uses videoId to fetch complete data from API
 * 4. States are updated with the fetched data
 * 5. UI renders using both initial and fetched data with fallbacks
 *
 * Error Handling:
 * - Console logs for debugging
 * - Try-catch for API calls
 * - Fallback texts for all dynamic content
 * - Optional chaining for safe property access
 */

{
	/* Action Buttons */
}
{
	/* <View style={styles.headerButtons}>
					<Pressable style={styles.shareButton}>
						<Text style={styles.shareButtonText}>🔗 Share</Text>
					</Pressable>
					<Pressable style={styles.favoriteButton}>
						<Text style={styles.favoriteButtonText}>⭐️</Text>
					</Pressable>
				</View> */
}
