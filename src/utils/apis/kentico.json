{"openapi": "3.0.0", "info": {"title": "CMS-Service-API", "description": "This is the CMS-Service-API OpenAPI 3.0 specification.\n\nSome useful links:\n  - [The source API definition](/docs/spec)", "version": "1.0.0"}, "tags": [{"name": "default", "description": "Default endpoints not grouped"}, {"name": "config", "description": "The web config"}, {"name": "pages", "description": "Everything related to pages"}, {"name": "sections", "description": "Everything related to sections"}, {"name": "playlists", "description": "Everything related to playlists"}, {"name": "categories", "description": "Everything related to categories"}, {"name": "subcategories", "description": "Everything related to sub-categories"}, {"name": "videos", "description": "Everything related to videos"}, {"name": "events", "description": "Everything related to events"}, {"name": "competitions", "description": "Everything related to competitions"}], "paths": {"/health": {"get": {"tags": ["default"], "summary": "Check the status of the service before the middlewares", "description": "If the service is up and running correctly the response will be OK", "operationId": "getHealth", "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Health"}}}, "description": "OK"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/pages": {"get": {"tags": ["pages"], "summary": "index pages", "description": "Return information concernant all the existing pages in the CMS", "operationId": "indexPages", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Categories"}, {"$ref": "#/components/parameters/Date"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "pages successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageIndex"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/pages/{codeName}": {"get": {"tags": ["pages"], "summary": "get one page", "description": "Returns a page given its codename", "operationId": "getPage", "parameters": [{"$ref": "#/components/parameters/Codename"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Filters"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "page successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PageLayout"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/kentico/sections/dynamic/{codeName}": {"get": {"tags": ["sections"], "summary": "return the data of a dynamic section aggregated with videos, events, playlists, etc", "description": "accepts different query params", "operationId": "getDynamicSectionByCodename", "parameters": [{"$ref": "#/components/parameters/Codename"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Filters"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns all the information related to a dynamic section aggregated with videos, events, playlists, categories, among others", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DynamicSection"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/kentico/sections/static/{codeName}": {"get": {"tags": ["sections"], "summary": "return the data of a static section aggregated with videos, events, playlists, etc", "description": "accepts different query params", "operationId": "getStaticSectionByCodename", "parameters": [{"$ref": "#/components/parameters/Codename"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns all the information related to a static section aggregated with videos, events, playlists, categories, among others", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StaticSection"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/kentico/config": {"get": {"tags": ["config"], "summary": "returns the config for the site", "description": "accepts language and previewFlag as query params", "operationId": "getConfig", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/PreviewFlag"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/InitConfig"}}}, "description": "OK"}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/config/mobile": {"get": {"tags": ["config"], "summary": "returns the config for the site", "description": "accepts language and previewFlag as query params", "operationId": "getMobileConfig", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/ServiceName"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/PreviewFlag"}], "responses": {"200": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileConfig"}}}, "description": "OK"}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/events": {"get": {"tags": ["events"], "summary": "returns all the events using filters", "description": "accepts previewFlag, challengerId, competitionId and language params", "operationId": "getEvents", "parameters": [{"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Categories"}, {"$ref": "#/components/parameters/Date"}, {"$ref": "#/components/parameters/VOD"}, {"$ref": "#/components/parameters/Status"}, {"$ref": "#/components/parameters/ChallengerId"}, {"$ref": "#/components/parameters/CompetitionId"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the events data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OriginsEventIndexItem"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/events/{id}": {"get": {"tags": ["events"], "summary": "return the data of a event in DB aggregated with the kentico data", "description": "accepts previewFlag, and language params", "operationId": "getEventById", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the event data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OriginsEvent"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/v2/kentico/events": {"get": {"tags": ["events"], "summary": "returns all the events using filters", "description": "accepts previewFlag, and language params", "operationId": "getEventsV2", "parameters": [{"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Categories"}, {"$ref": "#/components/parameters/Date"}, {"$ref": "#/components/parameters/VOD"}, {"$ref": "#/components/parameters/Status"}, {"$ref": "#/components/parameters/ChallengerId"}, {"$ref": "#/components/parameters/CompetitionId"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the events data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OriginsEventArray"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/kentico/videos": {"get": {"tags": ["videos"], "summary": "returns short information of videos in kontent and DB", "description": "accepts different filters", "operationId": "getVideos", "parameters": [{"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Date"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the video index data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OriginsVideoIndexItem"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/kentico/videos/{id}": {"get": {"tags": ["videos"], "summary": "return the data of a video in DB aggregated with the kentico data", "description": "accepts previewFlag, relatedVideosLimit, language and expand params", "operationId": "getVideoById", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/PreviewFlag"}, {"$ref": "#/components/parameters/RelatedVideoLimit"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/Expand"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the videos data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OriginsVideoWithRelatedVideos"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/playlists/count": {"get": {"tags": ["playlists"], "summary": "count the number of playlists in the platform for this client", "description": "return the number of playlist in the platform for this account", "operationId": "countPlaylists", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "playlist's videos count sucessfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlaylistCount"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/v2/playlists/{id}": {"get": {"tags": ["playlists"], "summary": "return the playlist and related paged videos", "description": "accepts limit and expand query params", "operationId": "getPlaylistV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Expand"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "playlist successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PlaylistWithVideoCard"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/playlists/{id}/videos": {"get": {"tags": ["playlists"], "summary": "return the related paginad videos of a playlist", "description": "accepts limit, before, and after query params", "operationId": "getPlaylistVideosV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/OverloadWithKontentData"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "videos successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedVideoCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories": {"get": {"tags": ["categories"], "summary": "return all categories", "description": "accepts limit, before, and after query params", "operationId": "indexCategoriesV2", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "categories successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryEntityArray"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}": {"get": {"tags": ["categories"], "summary": "return the category and/or related paged videos, events, playlists and sub-categories", "description": "accepts limit and expand query params", "operationId": "getCategoryByIdV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Expand"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "category successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryCardWithRelationships"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}/subcategories": {"get": {"tags": ["categories"], "summary": "return the related paged subcategories of category", "description": "SubCategories are not paged, because, normally, we will never have more than 100 in a category", "operationId": "getCategorySubCategoriesV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "subcategories successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubCategoryEntityArray"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}, "/ott/v2/categories/{id}/videos": {"get": {"tags": ["categories"], "summary": "return the related paged videos of category", "description": "accepts limit, before, and after query params", "operationId": "getCategoryVideosV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/OverloadWithKontentData"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "videos successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedVideoCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}/events": {"get": {"tags": ["categories"], "summary": "return the related paged events of category", "description": "accepts limit, before, and after query params", "operationId": "getCategoryEventsV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "events successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedEventCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}/playlists": {"get": {"tags": ["categories"], "summary": "return the related paged playlists of category", "description": "accepts limit, before, and after query params", "operationId": "getCategoryPlaylistsV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "events successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedPlaylistCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}/subcategories/{scatid}/videos": {"get": {"tags": ["categories"], "summary": "return the related paged videos matching the category and subcategory", "description": "accepts limit, before, and after query params", "operationId": "getCategorySubCategoriesVideosV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/SCIdentifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/OverloadWithKontentData"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "videos successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedVideoCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/categories/{id}/subcategories/{scatid}/events": {"get": {"tags": ["categories"], "summary": "return the related paged events matching the category and subcategory", "description": "accepts limit, before, and after query params", "operationId": "getCategorySubCategoriesEventsV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/SCIdentifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "events successfuly retrieved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedEventCardAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/v2/subcategories/{id}": {"get": {"tags": ["subcategories"], "summary": "return the subcategory data by id and its related entities", "description": "accepts expanding Categories, Videos and Events", "operationId": "getSubCategoryByIdV2", "parameters": [{"$ref": "#/components/parameters/Identifier"}, {"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/Expand"}, {"$ref": "#/components/parameters/Language"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "returns the correspoding subcategory information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SubCategoryCard"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "401": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Unauthorized"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}, "security": [{"bearerAuth": []}]}}, "/ott/kentico/competitions": {"get": {"tags": ["competitions"], "summary": "return competitions for the current account", "description": "accepts pagination parameters", "operationId": "getCompetitions", "parameters": [{"$ref": "#/components/parameters/AccountKey"}, {"$ref": "#/components/parameters/Limit"}, {"$ref": "#/components/parameters/After"}, {"$ref": "#/components/parameters/Before"}, {"$ref": "#/components/parameters/ServiceName"}], "responses": {"200": {"description": "Competitons successfuly retrived", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PagedCompetitionEntityArrayAlt"}}}}, "400": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Bad Request"}, "403": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Forbidden"}, "500": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}}}, "description": "Internal Server Error"}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "A token issued by users-service"}}, "parameters": {"Limit": {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1}}, "Before": {"name": "before", "in": "query", "required": false, "schema": {"type": "string"}}, "After": {"name": "after", "in": "query", "required": false, "schema": {"type": "string"}}, "AccountKey": {"name": "x-account-key", "in": "header", "required": true, "schema": {"type": "string"}}, "Categories": {"name": "categories", "in": "query", "required": false, "schema": {"type": "string"}}, "ServiceName": {"name": "x-service-name", "in": "header", "required": false, "schema": {"type": "string", "example": "swagger-ui"}}, "Codename": {"name": "codeName", "in": "path", "required": true, "schema": {"type": "string"}}, "RelatedVideoLimit": {"name": "relatedVideosLimit", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "maximum": 99}}, "Filters": {"name": "filters", "in": "query", "required": false, "schema": {"type": "string"}}, "Language": {"name": "language", "in": "query", "required": false, "schema": {"type": "string", "example": "fr"}}, "PreviewFlag": {"name": "previewFlag", "in": "query", "required": false, "schema": {"type": "boolean"}}, "Identifier": {"name": "id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, "SCIdentifier": {"name": "scatid", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}, "Expand": {"name": "expand", "in": "query", "required": false, "schema": {"type": "string", "example": "[Video]"}}, "Date": {"name": "date", "in": "query", "required": false, "schema": {"type": "string", "example": "[gte]27/01/2023"}}, "VOD": {"name": "VOD", "in": "query", "required": false, "schema": {"type": "string", "enum": ["true", "false"]}}, "Status": {"name": "status", "in": "query", "required": false, "schema": {"type": "string", "enum": ["past", "future", "live"]}}, "CompetitionId": {"name": "competitionId", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}}, "ChallengerId": {"name": "challengerId", "in": "query", "required": false, "schema": {"type": "string", "format": "uuid"}}, "OverloadWithKontentData": {"name": "overloadWithKontentData", "in": "query", "required": false, "schema": {"type": "string", "example": "false"}}}, "responses": {"BadRequest": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}, "example": {"status": 400, "type": "BadRequest", "message": "Bad Request"}}, "application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "Unathorized": {"description": "Unauthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}, "example": {"status": 401, "type": "Unauthorized", "message": "Unauthorized"}}, "application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "Forbidden": {"description": "Forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}, "example": {"status": 403, "type": "Forbidden", "message": "Forbidden"}}, "application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}, "InternalServerError": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Exception"}, "example": {"status": 500, "type": "InternalServerError", "message": "Internal Server Error"}}, "application/text": {"schema": {"$ref": "#/components/schemas/Error"}}}}}, "schemas": {"Error": {"type": "string"}, "Exception": {"type": "object", "properties": {"status": {"type": "integer"}, "type": {"type": "string"}, "message": {"type": "string"}}}, "MessageOK": {"type": "object", "properties": {"message": {"type": "string", "enum": ["OK", "KO"]}}}, "StatusOK": {"type": "object", "properties": {"status": {"type": "string", "enum": ["ok", "ko"], "default": "ok"}}}, "Health": {"type": "object", "properties": {"status": {"type": "string", "enum": ["OK", "KO"]}}}, "KenticoBaseType": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage"]}, "KenticoBaseTypePartial": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}}}, "KenticoAssetType": {"type": "object", "properties": {"name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string"}, "size": {"type": "number"}, "url": {"type": "string"}, "width": {"type": "string"}, "height": {"type": "string"}}, "required": ["name", "description", "url"]}, "KenticoImageType": {"type": "object", "properties": {"image": {"$ref": "#/components/schemas/KenticoAssetType"}, "mobileImage": {"allOf": [{"$ref": "#/components/schemas/KenticoAssetType"}], "nullable": true}, "buttonRedirectionUrl": {"type": "string", "nullable": true}, "mobileRedirectionTarget": {"type": "string", "nullable": true}, "buttonTitle": {"type": "string", "nullable": true}, "buttonRedirectionTargetType": {"type": "string", "enum": ["internal", "external"]}}, "required": ["buttonRedirectionUrl", "mobileRedirectionTarget", "buttonTitle", "buttonRedirectionTargetType"]}, "ContentImageType": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "image": {"$ref": "#/components/schemas/KenticoImageType"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "image", "title", "description"]}, "KenticoPageItem": {"type": "object", "properties": {"id": {"type": "string"}, "codename": {"type": "string"}, "itemType": {"type": "string"}, "_kenticoPublicationDate": {"type": "string"}}, "required": ["id", "codename", "itemType"]}, "PageIndex": {"type": "object", "properties": {"pages": {"type": "array", "items": {"$ref": "#/components/schemas/KenticoPageItem"}}, "cursor": {"allOf": [{"$ref": "#/components/schemas/Cursor"}], "nullable": true}}, "required": ["pages"]}, "Cursor": {"type": "object", "properties": {"before": {"type": "string", "nullable": true}, "after": {"type": "string", "nullable": true}}, "required": ["before", "after"]}, "MenuItem": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "redirectionTarget": {"type": "string"}, "redirectionTargetType": {"type": "string", "enum": ["external", "internal", "page"]}, "name": {"type": "string"}, "subItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}, "mobile": {"$ref": "#/components/schemas/MobileMenuItem"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "redirectionTarget", "name", "subItems", "mobile"], "example": {"_kenticoCodename": "string", "_kenticoId": "string", "_kenticoItemType": "string", "_kenticoLanguage": "string", "redirectionTarget": "string", "redirectionTargetType": "external", "name": "string", "subItems": [], "mobile": {"name": "string", "redirectionTarget": "string", "redirectionTargetType": "external", "subItems": []}}}, "MobileMenuItem": {"type": "object", "properties": {"redirectionTarget": {"type": "string"}, "redirectionTargetType": {"type": "string", "enum": ["external", "internal", "page"]}, "name": {"type": "string"}, "subItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}, "required": ["redirectionTarget", "name", "subItems"]}, "AboutLink": {"allOf": [{"$ref": "#/components/schemas/RedirectionTarget"}, {"type": "object", "properties": {"name": {"type": "string"}, "subItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}}, "required": ["name", "subItems"]}]}, "Header": {"type": "object", "properties": {"menuItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}, "logo": {"$ref": "#/components/schemas/KenticoAssetType"}, "favicon": {"$ref": "#/components/schemas/KenticoAssetType"}, "fixed": {"type": "boolean"}, "aboutLink": {"allOf": [{"$ref": "#/components/schemas/AboutLink"}], "nullable": true}, "liveEvents": {"type": "array", "items": {"$ref": "#/components/schemas/LiveEvents"}}, "headerColor": {"type": "string", "enum": ["standardPrimary", "alwaysTransparentBlurred", "standard-black"]}, "logoPosition": {"type": "string", "enum": ["left", "centered"]}}, "required": ["menuItems", "fixed", "aboutLink", "headerColor", "logoPosition"]}, "LiveEvents": {"type": "object", "properties": {"itemId": {"type": "string"}, "urlSlug": {"type": "string", "nullable": true}}, "required": ["itemId", "urlSlug"]}, "SEO": {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "canonicalTag": {"type": "string"}, "robots": {"type": "array", "items": {"type": "string"}}}, "required": ["title", "description"]}, "SocialItem": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"socialName": {"type": "string"}, "linkUrl": {"type": "string"}, "socialIcon": {"$ref": "#/components/schemas/KenticoAssetType"}}, "required": ["socialName", "linkUrl"]}]}, "Footer": {"type": "object", "properties": {"menuItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}, "logo": {"$ref": "#/components/schemas/KenticoAssetType"}, "socialItems": {"type": "array", "items": {"$ref": "#/components/schemas/SocialItem"}}, "copyright": {"type": "string"}, "legalLinks": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}, "footerLayout": {"type": "string", "enum": ["standard", "columns"]}, "footerBackgroundColor": {"type": "string", "enum": ["black", "white", "primary", "secondary"]}}, "required": ["menuItems", "socialItems", "copyright", "legalLinks", "footerLayout", "footerBackgroundColor"]}, "InitConfig": {"type": "object", "properties": {"header": {"$ref": "#/components/schemas/Header"}, "footer": {"$ref": "#/components/schemas/Footer"}, "partnersBottomSection": {"type": "object", "properties": {"partnersSections": {"type": "array", "items": {"$ref": "#/components/schemas/IterablePartnerStaticSection"}}}, "required": ["partnersSections"]}, "SEO": {"$ref": "#/components/schemas/SEO"}, "theatersLogos": {"type": "array", "items": {"$ref": "#/components/schemas/KenticoAssetType"}}, "landscapePlaceholder": {"$ref": "#/components/schemas/KenticoAssetType"}, "portraitPlaceholder": {"$ref": "#/components/schemas/KenticoAssetType"}, "sectionTitlesColor": {"type": "string", "enum": ["black/white", "primary"]}, "theme": {"type": "string", "enum": ["dark", "light", "both"]}, "adsSettings": {"anyOf": [{"$ref": "#/components/schemas/AdvertisementStaticSection"}, {"$ref": "#/components/schemas/GoogleAdsStaticSection"}]}}, "required": ["header", "footer", "partnersBottomSection", "SEO", "sectionTitlesColor", "theme", "adsSettings"]}, "UpdateConfig": {"type": "object", "properties": {"androidCurrentVersion": {"type": "string"}, "androidMinVersion": {"type": "string"}, "androidUpdateUrl": {"type": "string"}, "iosCurrentVersion": {"type": "string"}, "iosMinVersion": {"type": "string"}, "iosUpdateUrl": {"type": "string"}}}, "ManageSocialConfig": {"type": "object", "properties": {"androidMinVersion": {"type": "string"}, "iosMinVersion": {"type": "string"}}}, "MobileConfig": {"type": "object", "properties": {"onboarding_title": {"type": "string"}, "onboarding_text": {"type": "string"}, "registrationCompletedText": {"type": "string"}, "app_logo": {"$ref": "#/components/schemas/KenticoAssetType"}, "updateConfig": {"$ref": "#/components/schemas/UpdateConfig"}, "manage_social_login": {"$ref": "#/components/schemas/ManageSocialConfig"}, "menuItems": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItem"}}, "tab_bar": {"type": "array", "items": {"type": "object", "properties": {"type": {"type": "string"}, "is_visible": {"type": "string"}}, "required": ["type", "is_visible"]}}, "mySubscriptionsUrl": {"type": "string", "nullable": true}, "VASTAdsUrl": {"type": "string", "nullable": true}, "partners": {"type": "array", "items": {"$ref": "#/components/schemas/WebPartner"}}, "liveEvents": {"type": "array", "items": {"$ref": "#/components/schemas/LiveEvents"}}}, "required": ["onboarding_title", "onboarding_text", "registrationCompletedText", "menuItems", "tab_bar", "partners", "liveEvents"]}, "WebPartner": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"redirectUrl": {"type": "string"}, "image": {"$ref": "#/components/schemas/KenticoAssetType"}, "title": {"type": "string"}}, "required": ["redirectUrl"]}]}, "SectionContent": {"anyOf": [{"$ref": "#/components/schemas/CarouselStaticSection"}, {"$ref": "#/components/schemas/CustomTitleStaticSection"}, {"$ref": "#/components/schemas/GridDynamicSection"}, {"$ref": "#/components/schemas/GridStaticSection"}, {"$ref": "#/components/schemas/GoogleAdsStaticSection"}, {"$ref": "#/components/schemas/AdvertisementStaticSection"}, {"$ref": "#/components/schemas/SlideStaticSection"}, {"$ref": "#/components/schemas/AccordionStaticSection"}, {"$ref": "#/components/schemas/MostViewedStaticSection"}, {"$ref": "#/components/schemas/IterablePartnerStaticSection"}, {"$ref": "#/components/schemas/LiveCarouselDynamicSection"}, {"$ref": "#/components/schemas/HeroWebStaticSection"}, {"$ref": "#/components/schemas/CarouselDynamicSection"}, {"$ref": "#/components/schemas/CarouselWithTypeStaticSection"}, {"$ref": "#/components/schemas/GridWithCategoryDynamicSection"}, {"$ref": "#/components/schemas/CustomTextStaticSection"}, {"$ref": "#/components/schemas/RecommendedWebStaticSection"}, {"$ref": "#/components/schemas/CarouselLatestVideoDynamicSection"}]}, "DynamicSection": {"anyOf": [{"$ref": "#/components/schemas/GridWithCategoryDynamicSection"}, {"$ref": "#/components/schemas/CarouselLatestVideoDynamicSection"}, {"$ref": "#/components/schemas/CarouselDynamicSection"}, {"$ref": "#/components/schemas/LiveCarouselDynamicSection"}, {"$ref": "#/components/schemas/GridDynamicSection"}]}, "StaticSection": {"anyOf": [{"$ref": "#/components/schemas/SlideStaticSection"}, {"$ref": "#/components/schemas/CarouselStaticSection"}, {"$ref": "#/components/schemas/GoogleAdsStaticSection"}, {"$ref": "#/components/schemas/AdvertisementStaticSection"}, {"$ref": "#/components/schemas/MostViewedStaticSection"}, {"$ref": "#/components/schemas/CustomTitleStaticSection"}, {"$ref": "#/components/schemas/CustomTextStaticSection"}, {"$ref": "#/components/schemas/IterablePartnerStaticSection"}, {"$ref": "#/components/schemas/HeroWebStaticSection"}, {"$ref": "#/components/schemas/RecommendedWebStaticSection"}, {"$ref": "#/components/schemas/GridStaticSection"}, {"$ref": "#/components/schemas/AccordionStaticSection"}, {"$ref": "#/components/schemas/CarouselWithTypeStaticSection"}]}, "PageLayout": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"title": {"type": "string"}, "description": {"type": "string", "nullable": true}, "image": {"allOf": [{"$ref": "#/components/schemas/KenticoAssetType"}], "nullable": true}, "components": {"type": "array", "items": {"$ref": "#/components/schemas/SectionContent"}}, "_kenticoPublicationDate": {"type": "string", "format": "date-time"}}, "required": ["title", "description", "components"]}]}, "CarouselStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_carousel"], "default": "section_static_carousel"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "items": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/VideoCard"}, {"$ref": "#/components/schemas/PlaylistCard"}, {"$ref": "#/components/schemas/CategoryCard"}, {"$ref": "#/components/schemas/Image"}]}}, "itemsDisplayType": {"type": "string", "enum": ["landscape", "portrait"]}, "showMoreButtonRedirection": {"type": "string", "nullable": true}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}, "mobileShowMoreButtonRedirection": {"type": "string", "nullable": true}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "items", "itemsDisplayType", "showMoreButtonRedirection", "mobileShowMoreButtonRedirection"]}, "CustomTitleStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_custom_title"], "default": "section_custom_title"}, "_kenticoLanguage": {"type": "string"}, "text": {"type": "string"}, "size": {"type": "string", "enum": ["big", "medium", "small"]}, "horizontalPosition": {"type": "string", "enum": ["left", "right", "center"]}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "text", "size", "horizontalPosition"]}, "GridDynamicSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_dynamic_grid"], "default": "section_dynamic_grid"}, "_kenticoLanguage": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_grid"], "default": "section_dynamic_grid"}, "params": {"type": "object", "properties": {"title": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_grid"], "default": "section_dynamic_grid"}, "itemsType": {"type": "string"}, "sectionFilters": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/CustomTag"}}, "buttonType": {"type": "string"}, "buttonRedirect": {"type": "string"}, "additionalFilters": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/CustomTag"}}, "numberOfItems": {"type": "number"}}, "required": ["title", "type", "itemsType", "sectionFilters", "additionalFilters", "numberOfItems"]}, "items": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/VideoCard"}, {"$ref": "#/components/schemas/EventCard"}]}}, "codename": {"type": "string"}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "type", "params", "items", "codename"]}, "GridStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_grid"], "default": "section_static_grid"}, "_kenticoLanguage": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, "title": {"type": "string"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "items", "title"]}, "GoogleAdsStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_google_ads"], "default": "section_static_google_ads"}, "_kenticoLanguage": {"type": "string"}, "gptTag": {"type": "string"}, "adUnit": {"type": "string"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "gptTag", "adUnit"]}, "AdvertisementStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_ad"], "default": "section_static_ad"}, "_kenticoLanguage": {"type": "string"}, "image": {"type": "array", "items": {"$ref": "#/components/schemas/KenticoImageType"}}, "redirectionTarget": {"type": "string"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "image", "redirectionTarget"]}, "SlideStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_slider"], "default": "section_static_slider"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "displayRatio": {"type": "string", "enum": ["normal", "narrow", "wide"]}, "items": {"type": "array", "items": {"anyOf": [{"$ref": "#/components/schemas/VideoCard"}, {"$ref": "#/components/schemas/PlaylistCard"}, {"$ref": "#/components/schemas/EventCard"}, {"$ref": "#/components/schemas/CategoryCard"}, {"$ref": "#/components/schemas/Image"}]}}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "displayRatio", "items"]}, "AccordionStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_static_accordion"], "default": "section_static_accordion"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/OriginsVideo"}}, "ratio": {"type": "string", "enum": ["normal", "narrow", "wide"]}, "redirectionTarget": {"type": "string"}, "redirectionTargetType": {"type": "string", "enum": ["external", "page"]}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "items", "ratio", "redirectionTarget"]}, "MostViewedStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_most_viewed"], "default": "section_most_viewed"}, "_kenticoLanguage": {"type": "string"}, "type": {"type": "string", "enum": ["videos", "playlists"]}, "period": {"type": "string", "enum": ["weekly", "monthly", "semestral", "yearly"]}, "updatedAt": {"type": "string", "format": "date-time"}, "items": {"nullable": true, "anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCard"}}]}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "type", "period", "items"]}, "IterablePartnerStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["web_section___partners"], "default": "web_section___partners"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "itemsSize": {"type": "string", "enum": ["big", "small"]}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/IterablePartner"}}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "itemsSize", "items"]}, "LiveCarouselDynamicSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}, "_kenticoItemType": {"type": "string", "enum": ["section_dynamic_live"], "default": "section_dynamic_live"}, "type": {"type": "string", "enum": ["section_dynamic_live"], "default": "section_dynamic_live"}, "redirectionPath": {"type": "string"}, "mobileRedirection": {"type": "string"}, "params": {"type": "object", "properties": {"type": {"type": "string", "enum": ["section_dynamic_live"], "default": "section_dynamic_live"}}, "required": ["type"]}, "filter": {"type": "string", "enum": ["all", "club", "competition"]}, "clubOrCompetitionId": {"type": "string", "format": "uuid"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/EventEntity"}}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoLanguage", "title", "_kenticoItemType", "type", "redirectionPath", "mobileRedirection", "params", "filter", "clubOrCompetitionId"]}, "HeroWebStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["web_section___hero"], "default": "web_section___hero"}, "_kenticoLanguage": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "logo": {"allOf": [{"$ref": "#/components/schemas/KenticoAssetType"}], "nullable": true}, "image": {"type": "array", "items": {"$ref": "#/components/schemas/KenticoImageType"}}, "items": {"type": "array", "nullable": true, "items": {"$ref": "#/components/schemas/VideoHero"}}, "content": {"type": "array", "nullable": true, "items": {"anyOf": [{"$ref": "#/components/schemas/ContentImageType"}, {"$ref": "#/components/schemas/VideoHeroAutoplay"}]}}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "name", "description", "image", "items", "content"]}, "CarouselDynamicSection": {"allOf": [{"allOf": [{"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_dynamic_carousel"], "default": "section_dynamic_carousel"}, "_kenticoLanguage": {"type": "string"}, "codename": {"type": "string"}, "categoryId": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_carousel"], "default": "section_dynamic_carousel"}, "format": {"type": "string", "enum": ["landscape", "portrait"]}, "params": {"type": "object", "properties": {"type": {"type": "string", "enum": ["section_dynamic_carousel"], "default": "section_dynamic_carousel"}}, "required": ["type"]}, "isFavorite": {"type": "boolean"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/EventCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryEntity"}}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "codename", "params", "cursor", "SubCategories"]}, {"$ref": "#/components/schemas/CategoryEntity"}]}, {"anyOf": [{"type": "object", "properties": {"itemsType": {"type": "string", "enum": ["video"], "default": "video"}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}}, "required": ["itemsType", "Videos"]}, {"type": "object", "properties": {"itemsType": {"type": "string", "enum": ["playlist"], "default": "playlist"}, "Playlists": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCardBase"}}}, "required": ["itemsType", "Playlists"]}]}]}, "CarouselLatestVideoDynamicSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_dynamic_carousel_latest_videos"], "default": "section_dynamic_carousel_latest_videos"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_carousel_latest_videos"], "default": "section_dynamic_carousel_latest_videos"}, "codename": {"type": "string"}, "params": {"type": "object", "properties": {"type": {"type": "string", "enum": ["section_dynamic_carousel_latest_videos"], "default": "section_dynamic_carousel_latest_videos"}}, "required": ["type"]}, "period": {"type": "string", "enum": ["last_7_days", "last_15_days", "last_30_days", "last_365_days"]}, "format": {"type": "string", "enum": ["landscape", "portrait"]}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "type", "codename", "params", "period", "format", "breakpoints", "Videos", "cursor"]}, "CarouselWithTypeStaticSection": {"allOf": [{"$ref": "#/components/schemas/CarouselStaticSection"}, {"type": "object", "properties": {"displayCategoryOnThumbnails": {"type": "boolean"}, "sectionType": {"type": "string", "enum": ["carousel", "slider", "grid"]}}}]}, "GridWithCategoryDynamicSection": {"allOf": [{"allOf": [{"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_dynamic_grid_with_category"], "default": "section_dynamic_grid_with_category"}, "_kenticoLanguage": {"type": "string"}, "codename": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_grid_with_category"], "default": "section_dynamic_grid_with_category"}, "title": {"type": "string"}, "categoryId": {"type": "string", "format": "uuid"}, "withFilters": {"type": "boolean"}, "fetchMoreUrl": {"type": "string"}, "params": {"type": "object", "properties": {"title": {"type": "string"}, "type": {"type": "string", "enum": ["section_dynamic_grid_with_category"], "default": "section_dynamic_grid_with_category"}, "numberOfItems": {"type": "integer"}, "withFilters": {"type": "boolean"}, "itemsDisplayType": {"type": "string", "enum": ["landscape", "portrait"]}}, "required": ["title", "type", "numberOfItems", "withFilters", "itemsDisplayType"]}, "isFavorite": {"type": "boolean"}, "Events": {"type": "array", "items": {"$ref": "#/components/schemas/EventCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryEntity"}}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "codename", "title", "categoryId", "withFilters", "params", "cursor", "SubCategories"]}, {"$ref": "#/components/schemas/CategoryEntity"}]}, {"anyOf": [{"type": "object", "properties": {"itemsType": {"type": "string", "enum": ["video"], "default": "video"}, "Videos": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}}, "required": ["itemsType", "Videos"]}, {"type": "object", "properties": {"itemsType": {"type": "string", "enum": ["playlist"], "default": "playlist"}, "Playlists": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCardBase"}}}, "required": ["itemsType", "Playlists"]}]}]}, "CustomTextStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_custom_text"], "default": "section_custom_text"}, "_kenticoLanguage": {"type": "string"}, "text": {"type": "string"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "text"]}, "RecommendedWebStaticSection": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["section_recommended_videos"], "default": "section_recommended_videos"}, "_kenticoLanguage": {"type": "string"}, "title": {"type": "string"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, "breakpoints": {"$ref": "#/components/schemas/Breakpoints"}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "title", "items", "breakpoints"]}, "Breakpoints": {"type": "object", "properties": {"perPage": {"type": "number"}, "breakpoints": {"type": "object", "properties": {"430": {"$ref": "#/components/schemas/ResponsiveOptions"}, "920": {"$ref": "#/components/schemas/ResponsiveOptions"}, "1280": {"$ref": "#/components/schemas/ResponsiveOptions"}, "1920": {"$ref": "#/components/schemas/ResponsiveOptions"}}, "required": ["430", "920", "1280", "1920"]}}, "required": ["perPage", "breakpoints"]}, "ResponsiveOptions": {"type": "object", "properties": {"perPage": {"type": "number"}}, "required": ["perPage"]}, "RedirectionTarget": {"type": "object", "properties": {"redirectionTarget": {"type": "string"}, "redirectionTargetType": {"type": "string", "enum": ["external", "internal", "page"]}}, "required": ["redirectionTarget"]}, "VideoHeroAutoplay": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"videoId": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "subtitle": {"type": "string", "nullable": true}, "buttonTitle": {"type": "string", "nullable": true}, "urlButtonRedirect": {"type": "string", "nullable": true, "enum": ["internal", "external", null]}}, "required": ["videoId", "title", "subtitle", "buttonTitle", "urlButtonRedirect"]}]}, "IterablePartner": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"title": {"type": "string"}, "image": {"$ref": "#/components/schemas/KenticoAssetType"}, "redirectUrl": {"type": "string"}}, "required": ["title", "redirectUrl"]}]}, "Image": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string", "enum": ["image"], "default": "image"}, "_kenticoLanguage": {"type": "string"}, "image": {"$ref": "#/components/schemas/KenticoImageType"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "required": ["_kenticoCodename", "_kenticoId", "_kenticoItemType", "_kenticoLanguage", "image", "title", "description"]}, "Video": {"type": "object", "properties": {"itemType": {"type": "string", "enum": ["video"], "default": "video"}, "itemId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "technicalDescription": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}}, "required": ["itemId", "description", "fullDescription", "duration", "name", "poster", "urlSlug"]}, "VideoEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "AccountId": {"type": "string", "nullable": true, "format": "uuid"}, "description": {"type": "string", "nullable": true}, "duration": {"type": "number", "nullable": true}, "filename": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "name": {"type": "string"}, "archiveData": {"nullable": true}, "captions": {"nullable": true}, "meta": {"nullable": true}, "poster": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "status": {"type": "string", "enum": ["none", "original", "in_progress", "encoded", "archived", "vendor"]}, "technicalDescription": {"type": "string", "nullable": true}, "PlaylistId": {"type": "string", "nullable": true, "format": "uuid"}, "playlistRank": {"type": "number"}, "updatedAt": {"type": "string", "format": "date-time"}, "createdAt": {"type": "string", "format": "date-time"}, "url": {"type": "string", "nullable": true}, "vendorApiKey": {"type": "string", "nullable": true}, "vendorName": {"type": "string", "enum": ["jwplayer", "awsplayer", "dailymotion", "youtube", "twitch"]}, "vendorVideoId": {"type": "string", "nullable": true}, "visibility": {"type": "string", "enum": ["public", "private"]}, "isPaid": {"type": "boolean"}, "marker": {"type": "number"}, "isNew": {"type": "boolean"}, "hasBeenViewed": {"type": "boolean"}, "currentFanViews": {"type": "number"}, "views": {"type": "number"}, "ratio": {"type": "string", "nullable": true, "enum": ["sixteen-nine", "nine-sixteen", null]}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOfferEntity"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "VideoCategorySubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategoryEntity"}}}, "required": ["id", "AccountId", "description", "duration", "filename", "fullDescription", "name", "poster", "portrait<PERSON><PERSON><PERSON><PERSON>", "publicationDate", "status", "technicalDescription", "PlaylistId", "updatedAt", "createdAt", "url", "vendorApiKey", "vendorName", "vendorVideoId", "visibility", "ratio"]}, "PagedVideoEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}, "PagedVideoEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "PagedVideoCard": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items"]}, "PagedVideoCardAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items"]}]}, "OriginsVideo": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseTypePartial"}, {"type": "object", "properties": {"itemType": {"type": "string", "enum": ["video"], "default": "video"}, "video": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "itemId": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "shareUrl": {"type": "string", "nullable": true}, "technicalDescription": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "marker": {"type": "number"}, "isNew": {"type": "boolean"}, "hasBeenViewed": {"type": "boolean"}, "views": {"type": "number"}, "currentFanViews": {"type": "number"}, "searchTags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "ratio": {"type": "string", "nullable": true, "enum": ["sixteen-nine", "nine-sixteen", null]}, "isFavorite": {"type": "boolean"}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "ItemProducts": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["description", "duration", "fullDescription", "itemId", "name", "poster", "publicationDate", "shareUrl", "technicalDescription", "urlSlug", "searchTags", "tags", "ratio"]}}, "required": ["itemType", "video"]}]}, "VideoCard": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "itemType": {"type": "string", "enum": ["video"], "default": "video"}, "itemId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "technicalDescription": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "products": {"type": "array", "nullable": true, "items": {"type": "string", "format": "uuid"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategoryEntity"}}, "marker": {"type": "number"}, "isNew": {"type": "boolean"}, "hasBeenViewed": {"type": "boolean"}, "currentFanViews": {"type": "number"}, "views": {"type": "number"}, "ratio": {"type": "string", "enum": ["sixteen-nine", "nine-sixteen"], "default": "sixteen-nine"}, "isFavorite": {"type": "boolean"}}, "required": ["itemId", "description", "fullDescription", "duration", "name", "poster", "urlSlug"]}, "VideoHero": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "itemType": {"type": "string", "enum": ["video"], "default": "video"}, "itemId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategoryEntity"}}, "ratio": {"type": "string", "enum": ["sixteen-nine", "nine-sixteen"], "default": "sixteen-nine"}, "isFavorite": {"type": "boolean"}}, "required": ["itemId", "description", "duration", "name", "poster", "urlSlug"]}, "OriginsTag": {"allOf": [{"$ref": "#/components/schemas/KenticoBaseType"}, {"type": "object", "properties": {"id": {"type": "string"}, "tagType": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "tagType", "name"]}]}, "OriginsVideoIndexItem": {"type": "object", "properties": {"itemType": {"type": "string", "enum": ["video"], "default": "video"}, "itemId": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}}, "required": ["itemType", "itemId", "urlSlug", "publicationDate"]}, "OriginsVideoWithRelatedVideos": {"allOf": [{"type": "object", "properties": {"itemType": {"type": "string", "enum": ["video"], "default": "video"}, "video": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "duration": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "itemId": {"type": "string", "nullable": true, "format": "uuid"}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "shareUrl": {"type": "string", "nullable": true}, "technicalDescription": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "marker": {"type": "number"}, "isNew": {"type": "boolean"}, "hasBeenViewed": {"type": "boolean"}, "views": {"type": "number"}, "currentFanViews": {"type": "number"}, "searchTags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "ratio": {"type": "string", "nullable": true, "enum": ["sixteen-nine", "nine-sixteen", null]}, "isFavorite": {"type": "boolean"}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "Playlist": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "ItemProducts": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, {"type": "array", "items": {"type": "string"}}]}}, "required": ["description", "duration", "fullDescription", "itemId", "name", "poster", "publicationDate", "shareUrl", "technicalDescription", "urlSlug", "searchTags", "tags", "ratio"]}}, "required": ["itemType", "video"]}, {"type": "object", "properties": {"relatedVideos": {"$ref": "#/components/schemas/PagedVideoCard"}, "playlistRelatedVideos": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCard"}}}, "required": ["relatedVideos"]}]}, "AWSStream": {"type": "object", "properties": {"url": {"type": "string", "nullable": true}, "streamType": {"type": "string"}, "awsStream": {"type": "string", "nullable": true, "enum": ["STANDBY", "ONAIR", "ARCHIVED", null]}}, "required": ["url", "streamType", "awsStream"]}, "StreamEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "isRecordReady": {"type": "boolean"}, "key": {"type": "string"}, "mapCoordinates": {"nullable": true}, "name": {"type": "string", "nullable": true}, "offset": {"type": "number", "nullable": true}, "options": {"nullable": true}, "recordName": {"type": "string", "nullable": true}, "startedAt": {"type": "string", "format": "date-time"}, "streamable": {"type": "string", "nullable": true}, "streamableId": {"type": "string", "nullable": true}, "streamType": {"type": "string", "nullable": true, "enum": ["main", "backup", "additionnal", null]}, "token": {"type": "string"}, "url": {"type": "string"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "createdAt", "updatedAt"]}, "CompetitionEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "slug": {"type": "string", "nullable": true, "maxLength": 255}, "displayOrder": {"type": "integer", "nullable": true}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "providerId": {"type": "string", "nullable": true, "maxLength": 255}, "SportId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}}, "required": ["id", "name"]}, "PagedCompetitionEntityArrayAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/CompetitionEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/CompetitionEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "EventEntity": {"type": "object", "properties": {"AccountId": {"type": "string", "nullable": true}, "activatedModules": {"type": "array", "nullable": true, "items": {"type": "integer"}}, "advertisingBanner": {"nullable": true}, "calendarEventId": {"type": "string", "nullable": true, "format": "uuid"}, "ChatId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "dailymotionLiveStreamId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}, "facebookPlaylistId": {"type": "string", "nullable": true}, "geoBlockingMapping": {"type": "object", "nullable": true, "properties": {"whitelist": {"type": "array", "items": {"type": "string"}}, "blacklist": {"type": "array", "items": {"type": "string"}}}}, "fullDescription": {"type": "string", "nullable": true}, "hashtag": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "location": {"type": "string", "nullable": true}, "name": {"type": "string"}, "options": {"nullable": true}, "organiserName": {"type": "string", "nullable": true}, "placeholder": {"type": "object", "nullable": true, "properties": {"poster": {"type": "string"}, "url": {"type": "string"}}}, "ParentId": {"type": "string", "nullable": true}, "refereeName": {"type": "string", "nullable": true}, "score": {"type": "object", "nullable": true, "properties": {"teamIn": {"type": "string", "nullable": true}, "teamOut": {"type": "string", "nullable": true}, "scoreIn": {"type": "string", "nullable": true}, "scoreOut": {"type": "string", "nullable": true}}}, "RoundId": {"type": "string", "nullable": true}, "shareUrl": {"type": "string", "nullable": true}, "SportId": {"type": "string", "format": "uuid"}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "state": {"type": "string", "nullable": true, "enum": ["liveOn", "liveOff", "replay", "liveDailymotion", "liveYoutube", "awsLive", null]}, "stats": {"nullable": true}, "step": {"type": "string", "nullable": true, "enum": ["starting", "break", "end", null]}, "streamIds": {"type": "array", "nullable": true, "items": {"type": "string"}}, "TeamClientId": {"type": "string", "nullable": true}, "ThemeId": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "VideoId": {"type": "string", "nullable": true}, "visibility": {"type": "string", "nullable": true, "enum": ["public", "private", null]}, "youtubeLiveStreamId": {"type": "string", "nullable": true}, "isPaid": {"type": "boolean"}, "ItemProducts": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, "Challengers": {"type": "array", "items": {"type": "object", "properties": {"birthday": {"type": "string", "nullable": true, "format": "date-time"}, "coachName": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "firstName": {"type": "string", "nullable": true}, "gamesPlayed": {"type": "number", "nullable": true}, "gender": {"type": "string", "nullable": true, "enum": ["standard", "team", "teammate", null]}, "goals": {"type": "number", "nullable": true}, "height": {"type": "number", "nullable": true}, "history": {"type": "string", "nullable": true}, "homeFieldName": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "isJunior": {"type": "boolean", "nullable": true}, "jerseyNumber": {"type": "number", "nullable": true}, "jerseyPicture": {"type": "string", "nullable": true}, "linkShop": {"type": "string", "nullable": true}, "linkStats": {"type": "string", "nullable": true}, "name": {"type": "string"}, "optaId": {"type": "number", "nullable": true}, "picture": {"type": "string", "nullable": true}, "pictureUrl": {"type": "string", "nullable": true}, "profileOptions": {"nullable": true}, "providerId": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "shortName": {"type": "string", "nullable": true}, "shots": {"type": "number", "nullable": true}, "smallPictureUrl": {"type": "string", "nullable": true}, "SportId": {"type": "string", "nullable": true}, "statsId": {"type": "number", "nullable": true}, "targetedShots": {"type": "number", "nullable": true}, "TeamId": {"type": "string", "nullable": true, "format": "uuid"}, "type": {"type": "string", "nullable": true, "enum": ["F", "M", "MIXTE", null]}, "updatedAt": {"type": "string", "format": "date-time"}, "weight": {"type": "number", "nullable": true}}, "required": ["birthday", "<PERSON><PERSON><PERSON>", "country", "createdAt", "firstName", "gamesPlayed", "gender", "goals", "height", "history", "homeFieldName", "id", "is<PERSON><PERSON><PERSON>", "jersey<PERSON><PERSON>ber", "jerseyPicture", "linkShop", "linkStats", "name", "optaId", "picture", "pictureUrl", "providerId", "role", "shortName", "shots", "smallPictureUrl", "SportId", "statsId", "targetedShots", "TeamId", "type", "updatedAt", "weight"]}}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOfferEntity"}}, "Tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "EventCategorySubCategories": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "SubCategoryId": {"type": "string", "format": "uuid"}, "EventId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "SubCategory": {"$ref": "#/components/schemas/SubCategoryEntity"}}, "required": ["id", "CategoryId", "SubCategoryId", "EventId", "createdAt", "updatedAt"]}}, "Streams": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/StreamEntity"}}, {"type": "array", "items": {"$ref": "#/components/schemas/AWSStream"}}]}, "Round": {"type": "object", "properties": {"CompetitionId": {"type": "string", "format": "uuid"}, "competitionOrder": {"type": "number"}, "createdAt": {"type": "string", "format": "date-time"}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "SeasonId": {"type": "string", "format": "uuid"}, "stage": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "Competition": {"$ref": "#/components/schemas/CompetitionEntity"}}, "required": ["CompetitionId", "competitionOrder", "createdAt", "id", "name", "SeasonId", "updatedAt"]}, "Sport": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "periods": {"type": "string", "nullable": true, "enum": ["multiple", "none", null]}, "sportsFieldFilename": {"type": "string", "nullable": true}, "svgSpriteFilename": {"type": "string", "nullable": true}, "timelineType": {"type": "string", "nullable": true, "enum": ["single", "double", null]}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "id", "name", "periods", "sportsFieldFilename", "svgSpriteFilename", "timelineType", "updatedAt"]}, "Markers": {"type": "array", "items": {"type": "object", "properties": {"ChallengerId": {"type": "string", "nullable": true, "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "dailymotionRepostId": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "endTime": {"type": "number"}, "endTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "EventId": {"type": "string", "nullable": true, "format": "uuid"}, "facebookVideoId": {"type": "string", "nullable": true}, "gameTime": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "MarkerTypeId": {"type": "string", "nullable": true, "format": "uuid"}, "notifications": {"nullable": true}, "options": {"nullable": true}, "ParentId": {"type": "string", "nullable": true, "format": "uuid"}, "shouldDisplayChallengerProfile": {"type": "boolean", "nullable": true}, "startTime": {"type": "number"}, "startTimeTs": {"type": "string", "nullable": true, "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["ChallengerId", "createdAt", "dailymotionRepostId", "description", "endTime", "endTimeTs", "EventId", "facebookVideoId", "gameTime", "id", "MarkerTypeId", "ParentId", "shouldDisplayChallengerProfile", "startTime", "startTimeTs", "updatedAt"]}}, "MarkerTypes": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "defaultTimeEnd": {"type": "integer", "nullable": true}, "defaultTimeStart": {"type": "integer", "nullable": true}, "description": {"type": "string", "nullable": true}, "displayOrder": {"type": "integer", "nullable": true}, "endGameMinute": {"type": "integer", "nullable": true}, "ico": {"type": "string", "nullable": true}, "icoDark": {"type": "string", "nullable": true}, "icoSettings": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "isMain": {"type": "boolean", "nullable": true}, "name": {"type": "string"}, "optaOffsetEnd": {"type": "integer", "nullable": true}, "optaOffsetStart": {"type": "integer", "nullable": true}, "pushNotification": {"type": "boolean", "nullable": true}, "shortcut": {"type": "string", "nullable": true}, "SportId": {"type": "string", "nullable": true, "format": "uuid"}, "startGameMinute": {"type": "integer", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "defaultTimeEnd", "defaultTimeStart", "description", "displayOrder", "endGameMinute", "ico", "icoDark", "icoSettings", "id", "is<PERSON><PERSON>", "name", "optaOffsetEnd", "optaOffsetStart", "pushNotification", "shortcut", "SportId", "startGameMinute", "updatedAt"]}}, "Video": {"$ref": "#/components/schemas/VideoEntity"}, "Theme": {"type": "object", "properties": {"AccountId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "customMarkerTypes": {"nullable": true}, "customMarkerTypesSvgSprite": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "style": {}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["AccountId", "createdAt", "customMarkerTypesSvgSprite", "id", "name", "updatedAt"]}, "EventAttachments": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "EventId": {"type": "string", "format": "uuid"}, "ext": {"type": "string", "nullable": true}, "fileName": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "type": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}, "url": {"type": "string"}}, "required": ["createdAt", "EventId", "ext", "fileName", "id", "name", "type", "updatedAt", "url"]}}, "translations": {"type": "array", "items": {"type": "object", "properties": {"createdAt": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "id": {"type": "string", "format": "uuid"}, "languageId": {"type": "string", "nullable": true}, "location": {"type": "string", "nullable": true}, "modelId": {"type": "string", "nullable": true}, "name": {"type": "string"}, "refereeName": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["createdAt", "description", "fullDescription", "id", "languageId", "location", "modelId", "name", "<PERSON><PERSON><PERSON>", "updatedAt"]}}}, "required": ["AccountId", "activatedModules", "calendarEventId", "ChatId", "createdAt", "dailymotionLiveStreamId", "description", "endDate", "facebookPlaylistId", "geoBlockingMapping", "fullDescription", "hashtag", "id", "location", "name", "organiserName", "placeholder", "ParentId", "<PERSON><PERSON><PERSON>", "score", "RoundId", "shareUrl", "SportId", "startDate", "state", "step", "streamIds", "TeamClientId", "ThemeId", "updatedAt", "VideoId", "visibility", "youtubeLiveStreamId", "Streams", "Round", "Sport"]}, "EventCard": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "itemId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "name": {"type": "string"}, "poster": {"type": "string", "nullable": true}, "posterPortrait": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "products": {"type": "array", "nullable": true, "items": {"type": "string", "format": "uuid"}}, "categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "subCategories": {"type": "array", "items": {"$ref": "#/components/schemas/VideoCategorySubCategoryEntity"}}, "isFavorite": {"type": "boolean"}, "itemType": {"type": "string", "enum": ["event"], "default": "event"}, "startDate": {"type": "string", "nullable": true, "format": "date-time"}, "endDate": {"type": "string", "nullable": true, "format": "date-time"}}, "required": ["itemId", "description", "name", "poster", "urlSlug", "startDate", "endDate"]}, "PagedEventEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EventEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}, "PagedEventEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/EventEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EventEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "PagedEventCard": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EventCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}, "PagedEventCardAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/EventCard"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/EventCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "OriginsEventIndexItem": {"type": "object", "properties": {"itemType": {"type": "string", "enum": ["event"], "default": "event"}, "itemId": {"type": "string", "format": "uuid"}, "urlSlug": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}}, "required": ["itemId", "urlSlug", "publicationDate"]}, "OriginsEvent": {"type": "object", "properties": {"_kenticoCodename": {"type": "string"}, "_kenticoId": {"type": "string"}, "_kenticoItemType": {"type": "string"}, "_kenticoLanguage": {"type": "string"}, "itemType": {"type": "string", "enum": ["event"], "default": "event"}, "event": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "itemId": {"type": "string"}, "name": {"type": "string"}, "onrewindState": {"type": "string", "nullable": true}, "poster": {"type": "string", "nullable": true}, "shareUrl": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "searchTags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "Video": {"$ref": "#/components/schemas/VideoEntity"}, "ItemProducts": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, {"type": "array", "items": {"type": "string"}}]}, "competition": {"allOf": [{"$ref": "#/components/schemas/CompetitionEntity"}], "nullable": true}, "Streams": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/StreamEntity"}}, {"type": "array", "items": {"$ref": "#/components/schemas/AWSStream"}}]}}, "required": ["description", "itemId", "name", "onrewindState", "poster", "shareUrl", "urlSlug", "publicationDate", "startDate", "endDate", "fullDescription", "searchTags", "tags"]}}, "required": ["event"]}, "OriginsEventArray": {"type": "array", "items": {"$ref": "#/components/schemas/OriginsEvent"}}, "OriginsEventWithoutKenticoKeys": {"type": "object", "properties": {"itemType": {"type": "string", "enum": ["event"], "default": "event"}, "event": {"type": "object", "properties": {"description": {"type": "string", "nullable": true}, "itemId": {"type": "string"}, "name": {"type": "string"}, "onrewindState": {"type": "string", "nullable": true}, "poster": {"type": "string", "nullable": true}, "shareUrl": {"type": "string", "nullable": true}, "urlSlug": {"type": "string", "nullable": true}, "publicationDate": {"type": "string", "nullable": true, "format": "date-time"}, "startDate": {"type": "string", "nullable": true}, "endDate": {"type": "string", "nullable": true}, "fullDescription": {"type": "string", "nullable": true}, "searchTags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/TagEntity"}}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "Video": {"$ref": "#/components/schemas/VideoEntity"}, "ItemProducts": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PaymentProduct"}}, {"type": "array", "items": {"type": "string"}}]}, "competition": {"allOf": [{"$ref": "#/components/schemas/CompetitionEntity"}], "nullable": true}, "Streams": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/StreamEntity"}}, {"type": "array", "items": {"$ref": "#/components/schemas/AWSStream"}}]}}, "required": ["description", "itemId", "name", "onrewindState", "poster", "shareUrl", "urlSlug", "publicationDate", "startDate", "endDate", "fullDescription", "searchTags", "tags"]}}, "required": ["event"]}, "TagEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tagType": {"type": "string", "nullable": true}, "name": {"type": "string"}, "options": {"nullable": true}, "AccountId": {"type": "string", "format": "uuid"}, "placeholders": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "ItemTag": {"$ref": "#/components/schemas/ItemTagEntity"}, "PaymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOfferEntity"}}}, "required": ["id", "tagType", "name", "AccountId", "placeholders", "createdAt", "updatedAt"]}, "ItemTagEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "tagId": {"type": "string", "format": "uuid"}, "taggable": {"type": "string"}, "taggableId": {"type": "string", "format": "uuid"}}, "required": ["id", "tagId", "taggable", "taggableId"]}, "CustomTag": {"type": "object", "properties": {"type": {"type": "string"}, "name": {"type": "string"}, "displayValue": {"type": "string"}}, "required": ["type", "name", "displayValue"]}, "CategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "isFavorite": {"type": "boolean"}, "heroPortrait": {"type": "string"}, "heroLandscape": {"type": "string"}}, "required": ["id", "name", "accountId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "createdAt", "updatedAt"]}, "CategoryEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}, "VideoCategorySubCategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "SubCategoryId": {"type": "string", "format": "uuid"}, "VideoId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "SubCategory": {"$ref": "#/components/schemas/SubCategoryEntity"}}, "required": ["id", "CategoryId", "SubCategoryId", "VideoId", "createdAt", "updatedAt"]}, "CategoryCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "isFavorite": {"type": "boolean"}, "heroPortrait": {"type": "string"}, "heroLandscape": {"type": "string"}, "itemType": {"type": "string", "enum": ["category"], "default": "category"}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryEntity"}}}, "required": ["id", "name", "accountId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "createdAt", "updatedAt"]}, "SubCategoryEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "accountId", "createdAt", "updatedAt"]}, "SubCategoryEntityArray": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryEntity"}}, "SubCategoryCard": {"allOf": [{"$ref": "#/components/schemas/SubCategoryEntity"}, {"type": "object", "properties": {"Videos": {"$ref": "#/components/schemas/PagedVideoCard"}, "Events": {"$ref": "#/components/schemas/PagedEventCard"}, "Categories": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryEntity"}}}, "required": ["Categories"]}]}, "CategoryCardWithRelationships": {"allOf": [{"$ref": "#/components/schemas/CategoryCard"}, {"type": "object", "properties": {"Videos": {"$ref": "#/components/schemas/PagedVideoCard"}, "Playlist": {"$ref": "#/components/schemas/PagedPlaylistCard"}, "Events": {"$ref": "#/components/schemas/PagedEventCard"}, "SubCategories": {"type": "array", "items": {"$ref": "#/components/schemas/SubCategoryEntity"}}}}]}, "PlaylistEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Videos": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/VideoEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "PlaylistCardBase": {"type": "object", "properties": {"name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "itemType": {"type": "string", "enum": ["playlist"], "default": "playlist"}, "itemId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}}, "required": ["name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt", "itemId"]}, "PlaylistCard": {"allOf": [{"$ref": "#/components/schemas/PlaylistCardBase"}, {"type": "object", "properties": {"Videos": {"$ref": "#/components/schemas/PagedVideoCardAlt"}}}]}, "PlaylistWithVideoCard": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "accountId": {"type": "string", "format": "uuid"}, "CategoryId": {"type": "string", "format": "uuid"}, "description": {"type": "string", "nullable": true}, "thumbnail": {"type": "string", "nullable": true}, "portraitThumbnail": {"type": "string", "nullable": true}, "heroImageDesktop": {"type": "string", "nullable": true}, "heroImageMobile": {"type": "string", "nullable": true}, "currentVideoPlaylistRank": {"type": "integer"}, "newEpisodes": {"type": "integer"}, "hasBeenViewed": {"type": "boolean"}, "numberOfVideos": {"type": "integer"}, "isFavorite": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "Videos": {"$ref": "#/components/schemas/PagedVideoCardAlt"}}, "required": ["id", "name", "accountId", "CategoryId", "description", "thumbnail", "portrait<PERSON><PERSON><PERSON><PERSON>", "heroImageDesktop", "heroImageMobile", "createdAt", "updatedAt"]}, "PagedPlaylistEntity": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}, "PagedPlaylistEntityAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PlaylistEntity"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistEntity"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "PagedPlaylistCard": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}, "PagedPlaylistCardAlt": {"anyOf": [{"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCard"}}, {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/PlaylistCard"}}, "cursor": {"$ref": "#/components/schemas/Cursor"}}, "required": ["items", "cursor"]}]}, "PaymentProduct": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["Product"], "default": "Product"}, "accountId": {"type": "string", "format": "uuid"}, "id": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["draft", "published", "archived"], "default": "draft"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"description": {"type": "string"}, "weight": {"type": "string"}}}, "isStandard": {"type": "boolean"}, "_type": {"type": "string", "enum": ["Product"], "default": "Product"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "paymentOffers": {"type": "array", "items": {"$ref": "#/components/schemas/PaymentOffer"}}, "profiles": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfile"}}}, "required": ["accountId", "id", "productId", "status", "name", "created", "updated"]}, "PaymentOffer": {"type": "object", "properties": {"entityType": {"type": "string", "enum": ["PaymentOffer"], "default": "PaymentOffer"}, "accountId": {"type": "string", "format": "uuid"}, "productId": {"type": "string", "format": "uuid"}, "paymentOfferId": {"type": "string", "format": "uuid"}, "status": {"type": "string", "enum": ["active", "archived"], "default": "active"}, "provider": {"type": "string", "enum": ["stripe", "google", "apple"]}, "providerPaymentOfferId": {"type": "string"}, "name": {"type": "string"}, "data": {"type": "object", "properties": {"isLimitedDuration": {"type": "boolean"}, "population": {"type": "string"}, "weigth": {"type": "string"}, "type": {"type": "string", "enum": ["recurring", "one_time"]}, "price": {"type": "number"}, "currency": {"type": "string"}, "interval": {"type": "string", "enum": ["month", "year", "week", "day"]}, "intervalCount": {"type": "number"}}}, "isPPV": {"type": "boolean"}, "_type": {"type": "string", "enum": ["PaymentOffer"], "default": "PaymentOffer"}, "duration": {"type": "number"}, "created": {"type": "string", "format": "date-time"}, "updated": {"type": "string", "format": "date-time"}, "associatedProfiles": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfile"}}}, "required": ["accountId", "productId", "paymentOfferId", "provider", "providerPaymentOfferId", "name", "created", "updated"]}, "PaymentOfferEntity": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "paymentOfferType": {"type": "string", "enum": ["sku", "plan"]}, "name": {"type": "string"}, "stripeId": {"type": "string", "nullable": true}, "appleId": {"type": "string", "nullable": true}, "options": {"nullable": true}, "AccountId": {"type": "string", "format": "uuid"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}, "weight": {"type": "number"}}, "required": ["id", "paymentOfferType", "name", "stripeId", "appleId", "AccountId", "createdAt", "updatedAt"]}, "UserProfile": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true, "enum": ["payment", "marketing", "men-favorite-club", "women-favorite-club", "club-subscriber", null]}, "category": {"type": "string", "nullable": true, "enum": ["communication", "competition", "favorite-men-club", "favorite-women-club", "premium", null]}, "providerId": {"type": "string", "nullable": true}, "AccountId": {"type": "string", "format": "uuid"}, "retainOnLogin": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "required": ["id", "name", "type", "AccountId", "retainOnLogin", "createdAt", "updatedAt"]}, "PlaylistCount": {"type": "object", "properties": {"numberOfPlaylists": {"type": "integer"}}, "required": ["numberOfPlaylists"]}}}}