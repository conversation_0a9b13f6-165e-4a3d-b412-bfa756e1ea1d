# Section Renderer Documentation

## 1. Creating a new section renderer for a specific type:

1. Create a new file in **src/components/sections/renderers/** (e.g., NewSectionRenderer.tsx)
2. Define the component with appropriate props extending BaseSectionRendererProps
3. Add the component to the exports in **src/components/sections/renderers/index.ts**
4. Update the type definitions in **src/types/sectionTypes.ts**
5. Add the new renderer to the DEFAULT_SECTION_TYPES mapping in **SectionRenderer.tsx**

Example of creating a new renderer:

```typescript
// src/components/sections/renderers/NewSectionRenderer.tsx
import React from "react";
import { View, StyleSheet } from "react-native";
import { NewSectionRendererProps } from "../../../types/sectionTypes";

const NewSectionRenderer: React.FC<NewSectionRendererProps> = ({
	data,
	onPress,
	// Additional props specific to this renderer
	customProp,
}) => {
	// Implementation
	return <View>{/* Render content */}</View>;
};

export default React.memo(NewSectionRenderer);
```

## 2. Using the Section Renderer in a Page:

1. Import the SectionRenderer component
2. Map over your sections data and render each section using SectionRenderer
3. Pass the required props: section (the section data) and onItemPress (callback for item clicks)

Example from CompetitionsPage.tsx:

```typescript
const CompetitionsPage = () => {
	const navigation =
		useNavigation<CompetitionsScreenNavigationProp>();
	const { data, isLoading, error } = useCompetitionPageData();

	// Handle item press
	const handleItemPress = (id: string, type: string) => {
		if (type === "video") {
			navigation.navigate("VideoDetails", { videoId: id });
		} else if (type === "category") {
			navigation.navigate("Category", { categoryId: id });
		}
	};

	// Render each section
	const renderSection = ({ item }: { item: any }) => {
		return (
			<SectionRenderer
				section={item}
				onItemPress={handleItemPress}
			/>
		);
	};

	// Filter out sections with no items
	const filteredSections = data?.components?.filter(
		(section: any) => {
			// Skip sections with no items
			if (section.items && section.items.length === 0) {
				return false;
			}
			return true;
		}
	);

	return (
		<View style={styles.container}>
			{isLoading ? (
				<ActivityIndicator
					size="large"
					color="#0000ff"
				/>
			) : error ? (
				<Text>Error loading data</Text>
			) : (
				<FlatList
					data={filteredSections}
					renderItem={renderSection}
					keyExtractor={(item) => item._kenticoCodename}
				/>
			)}
		</View>
	);
};
```

## 3. Customizing Section Rendering

There are three ways to customize how a section is rendered:

A. Using SECTION_CONFIGS for specific sections

If you have a specific section that needs custom rendering:

1. Add an entry to `SECTION_CONFIGS` in `SectionRenderer.tsx` using the section's codename
2. Specify the renderer and any custom props

```typescript
const SECTION_CONFIGS: SectionConfigMap = {
	// Add your custom section
	your_section_codename: {
		renderer: "carousel", // or any other renderer
		cardProps: {
			// Props passed to the card components
			showTitle: true,
			imageRatio: "16:9",
		},
		layoutProps: {
			// Props for the layout
			spacing: 20,
		},
	},
};
```

B. Modifying DEFAULT_SECTION_TYPES for all sections of a type

To change how all sections of a specific type are rendered:

1. Update the mapping in `DEFAULT_SECTION_TYPES` in `SectionRenderer.tsx`

```typescript
const DEFAULT_SECTION_TYPES: SectionTypeMap = {
	// Change this to use a different renderer
	section_dynamic_grid: "carousel", // Now all dynamic grids use carousel renderer
};
```

C. Creating a specialized renderer for a specific section type

For more complex customization:

1. Create a new renderer component
2. Add it to the switch statement in `SectionRenderer.tsx`

```typescript
// In SectionRenderer.tsx
if (sectionConfig && sectionConfig.renderer) {
	switch (sectionConfig.renderer) {
		case "carousel":
			return (
				<CarouselSectionRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		case "yourCustomRenderer":
			return (
				<YourCustomRenderer
					data={section}
					onPress={onItemPress}
				/>
			);
		default:
			break;
	}
}
```

## 4. Prop Passing Flow

The prop passing flow follows this pattern:

1. **Page Component** → Passes `section` data and `onItemPress` callback to `SectionRenderer`
2. **SectionRenderer** → Determines which specific renderer to use and passes:
   - `data`: The section data
   - `onPress`: The callback for item clicks
   - Additional props from `SECTION_CONFIGS` if applicable
3. **Specific Renderer** (e.g., CarouselSectionRenderer) → Renders the section and passes:

   - Item data and callbacks to child components (e.g., VideoCard)
   - Style and layout props

## 5. Rendering a Particular Section Row

To render a specific section row:

1. Ensure the section data has the correct structure expected by the renderer
2. Make sure the section type is mapped correctly in `DEFAULT_SECTION_TYPES`
3. If needed, add a specific configuration in `SECTION_CONFIGS`
4. Pass the section data to `SectionRenderer`

For example, to render a carousel section:

```typescript
// Section data structure for a carousel
const carouselSection = {
	_kenticoCodename: "my_carousel_section",
	_kenticoItemType: "section_static_carousel",
	title: "My Carousel",
	items: [
		{
			itemId: "123",
			itemType: "video",
			name: "Video Title",
			thumbnail: "https://example.com/thumbnail.jpg",
			duration: "02:30",
		},
		// More items...
	],
};

// In your component
return (
	<SectionRenderer
		section={carouselSection}
		onItemPress={handleItemPress}
	/>
);
```

## 6. Handling Empty Sections

The section renderer system automatically handles empty sections in several ways:

1. **Empty items array**: If a section has no items or an empty items array, the renderer returns null
2. **Placeholder items**: If all items in a section are placeholders (no meaningful content), the renderer skips the section
3. **Filtering at page level**: You can filter out empty sections before passing them to SectionRenderer

Example of filtering at page level:

```typescript
const filteredSections = data?.components?.filter((section: any) => {
	// Skip sections with no items
	if (section.items && section.items.length === 0) {
		return false;
	}
	return true;
});
```

## 7. Performance Optimizations

The section renderers include several performance optimizations:

1. **React.memo**: All renderer components are wrapped with React.memo to prevent unnecessary re-renders
2. **FlatList optimizations**:

   - `removeClippedSubviews={true}`: Detaches views that are outside of the viewport
   - `maxToRenderPerBatch`: Limits the number of items rendered in each batch
   - `initialNumToRender`: Controls how many items to render initially
   - `windowSize`: Determines how far from the viewport to render items

3. **Conditional rendering**: Sections with no content are not rendered at all

## 8. Debugging Sections

To debug section rendering:

1. Add console logs to track which sections are being rendered or skipped:

```typescript
console.log(
	`Rendering section: ${data._kenticoCodename} (${data._kenticoItemType})`
);
```

2. Inspect the section data structure:

```typescript
console.log("Section data:", {
	itemCount: items.length,
	hasItems: items && items.length > 0,
	firstItem: items[0],
});
```

## 9. Extending the System

To extend the section renderer system:

1. **Add new section types**: Update the SectionType enum in sectionTypes.ts
2. **Create new renderers**: Add new renderer components for specialized layouts
3. **Enhance existing renderers**: Add new props to customize behavior
4. **Add utility functions**: Create helper functions for common operations
5. **Improve type definitions**: Refine interfaces to better match your data structures
