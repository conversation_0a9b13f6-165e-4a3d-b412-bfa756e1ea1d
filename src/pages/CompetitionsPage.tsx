import React, { useEffect } from "react";
import {
	StyleSheet,
	Text,
	View,
	FlatList,
	ActivityIndicator,
} from "react-native";
import {
	useNavigation,
	useRoute,
	RouteProp,
	NavigationProp,
} from "@react-navigation/native";
import { RootStackParamList } from "../app/index";

import CompetitionPageSectionRenderer from "../components/sections/pageRenderers/CompetitionPageSectionRenderer";
import { scale } from "../utils/helpers/dimensionScale.helper";
import { GLOBAL_STYLES } from "../styles/globalStyles";
import { replaceWithNotFound } from "../utils/helpers/errorNavigation.helper";

// Import our custom hook for fetching competition page data
import { useCompetitionPageData } from "../hooks/useCompetitionPageData";

/**
 * CompetitionsPage Component
 *
 * This page displays competition data including:
 * - Competition information
 * - Match highlights
 * - Teams
 * - News and media content
 */
const CompetitionsPage = () => {
	// Navigation hook for screen transitions
	const navigation =
		useNavigation<NavigationProp<RootStackParamList>>();

	// Get route params to check for pageCodename
	const route =
		useRoute<RouteProp<RootStackParamList, "CompetitionsPage">>();

	// Ensure pageCodename is provided from navigation params
	if (!route.params?.pageCodename) {
		console.error("No pageCodename provided in navigation params");
		// We'll use a fallback pageCodename for the default competitions page
	}

	// Use the provided pageCodename or fall back to a default competitions page
	const pageCodename =
		route.params?.pageCodename || "page_competitions";

	// Use our custom hook to fetch all required data only if we have a pageCodename
	const { pageData, upcomingEvents, isLoading, error } = pageCodename
		? useCompetitionPageData(pageCodename)
		: {
				pageData: null,
				upcomingEvents: [],
				isLoading: false,
				error:
					"No competition page specified. Please select a competition from the home page.",
		  };

	// Handle redirection to NotFoundPage when there's a 404 error
	useEffect(() => {
		if (error === "404") {
			// Use the helper function to replace the current screen with NotFoundPage
			// Use setTimeout to ensure this happens after the current render cycle
			// This helps prevent the error banner from appearing
			replaceWithNotFound(navigation);
		}
	}, [error, navigation]);

	// Render loading indicator, error message, or content
	return (
		<View style={styles.container}>
			{isLoading ? (
				<View style={styles.loadingContainer}>
					<ActivityIndicator
						size="large"
						color="#ffffff"
					/>
					<Text style={styles.loadingText}>
						Loading competitions ...
					</Text>
				</View>
			) : error && error !== "404" ? (
				// Only show the error container for non-404 errors
				// 404 errors are handled by the useEffect above which redirects to NotFoundPage
				<View style={styles.errorContainer}>
					<Text style={styles.errorText}>
						This page is not available.
					</Text>
				</View>
			) : (
				<>
					{/* Main content using FlatList for efficient rendering */}

					<FlatList
						data={(pageData?.components || []).filter((item) => {
							// Only filter out sections with no items
							// All sections are now handled by the CompetitionPageSectionRenderer
							return !(
								(item as any).items &&
								(item as any).items.length === 0
							);
						})}
						renderItem={({ item, index }) => {
							// Log the current section being rendered
							// console.log(
							// 	`Rendering section: ${item._kenticoCodename} (${item._kenticoItemType})`
							// );

							// Use CompetitionPageSectionRenderer to render the appropriate component based on section type
							return (
								<CompetitionPageSectionRenderer
									key={`section-${item._kenticoId}-${index}`}
									section={item}
									upcomingEvents={upcomingEvents}
									onItemPress={(id, type, eventData) => {
										// Log item press for tracking
										console.log(
											`${type} item pressed: ${id}`,
											eventData
										);

										// Handle category navigation
										if (type === "category" && id) {
											// Log detailed category information for API understanding
											console.log(
												`Category navigation: ${id} - ${eventData?.name}`
											);
											console.log(
												"Full category eventData:",
												JSON.stringify(eventData, null, 2)
											);

											// Navigate to Categories page with category data
											navigation.navigate("Categories", {
												categoryId: id,
												categoryName: eventData?.name || "Category",
												categoryImage: eventData?.poster || "",
											});
											return;
										}

										// Handle club navigation
										if (type === "club" && id) {
											// Log basic club navigation info
											console.log(
												`Club navigation: ${id} - ${
													eventData?.clubName || eventData?.name
												}`
											);

											// Check if we have a club codename from the API
											const clubCodename = eventData?.clubCodename;

											// Navigate to ClubDetailsPage with club ID, name, and codename
											navigation.navigate("ClubDetailsPage", {
												clubId: id,
												clubName:
													eventData?.clubName ||
													eventData?.name ||
													"Club",
												clubImage:
													eventData?.clubImage ||
													eventData?.poster ||
													"",
												clubCodename: clubCodename || "", // Pass the club codename if available
											});
											return;
										}

										// Navigate to video details page
										if (
											(type === "video" || type === "event") &&
											id
										) {
											// Determine if this is a live or upcoming event
											const isLive = eventData?.isLive || false;
											const startTime = eventData?.startDate || null;

											navigation.navigate("VideoDetailsPage", {
												video: {
													videoId: id,
													title:
														eventData?.name ||
														(item as any).title ||
														(item as any).name ||
														"Video",
													thumbnail: eventData?.poster
														? String(eventData.poster)
														: (item as any).poster
														? String((item as any).poster)
														: "",
													description:
														eventData?.description ||
														(item as any).description ||
														"",
													isLive: isLive,
													startTime: startTime,
												},
											});
										}
									}}
								/>
							);
						}}
						ListHeaderComponent={() => (
							<View
								style={styles.headerContainer}
								accessible={true}
								accessibilityRole="header"
								accessibilityLabel="Competitions page header"
							>
								{pageData?.description && (
									<Text style={styles.headerDescription}>
										{pageData.description}
									</Text>
								)}
							</View>
						)}
						ListEmptyComponent={() => (
							<View
								style={styles.emptyContainer}
								accessible={true}
								accessibilityRole="text"
								accessibilityLabel="No content available"
							>
								<Text style={styles.emptyText}>
									No content available
								</Text>
							</View>
						)}
						showsVerticalScrollIndicator={false}
						contentContainerStyle={styles.flatListContent}
						removeClippedSubviews={true}
						maxToRenderPerBatch={5}
						windowSize={5}
						initialNumToRender={5}
					/>

					{/* All sections are now handled by the CompetitionPageSectionRenderer */}
				</>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: GLOBAL_STYLES.COLORS.PAGE_BACKGROUND,
		paddingHorizontal: GLOBAL_STYLES.PAGE_HORIZONTAL_PADDING, // Apply global horizontal padding
	},

	flatListContent: {
		paddingBottom: scale(20), // Reduced padding at the bottom
		paddingTop: scale(5), // Reduced padding at the top
	},
	loadingContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
	},
	loadingText: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(18),
		marginTop: scale(32),
	},
	errorContainer: {
		flex: 1,
		justifyContent: "center",
		alignItems: "center",
		padding: scale(20),
	},
	errorText: {
		color: GLOBAL_STYLES.COLORS.ACCENT,
		fontSize: scale(16),
		textAlign: "center",
	},
	headerContainer: {
		paddingVertical: scale(15),
		marginBottom: scale(10),
		paddingBottom: scale(5),
		// No horizontal padding as it's handled by the container
	},
	headerDescription: {
		color: GLOBAL_STYLES.COLORS.TEXT_SECONDARY,
		fontSize: scale(16),
		marginBottom: scale(12),
	},
	emptyContainer: {
		padding: scale(40),
		justifyContent: "center",
		alignItems: "center",
	},
	emptyText: {
		color: GLOBAL_STYLES.COLORS.TEXT_TERTIARY,
		fontSize: scale(16),
		textAlign: "center",
	},
	sectionContainer: {
		padding: scale(16),
		marginBottom: scale(16),
		backgroundColor: "rgba(255, 255, 255, 0.05)",
		borderRadius: scale(8),
		marginHorizontal: scale(16),
	},
	sectionType: {
		color: GLOBAL_STYLES.COLORS.TEXT_TERTIARY,
		fontSize: scale(14),
		marginBottom: scale(4),
	},
	sectionTitle: {
		color: GLOBAL_STYLES.COLORS.TEXT_PRIMARY,
		fontSize: scale(18),
		fontWeight: "bold",
	},
});

export default CompetitionsPage;
