/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type Error = string;

export interface Exception {
  status?: number;
  type?: string;
  message?: string;
}

export interface StatusOK {
  /** @default "OK" */
  status: "OK";
}

export type UuidArray = string[];

/**
 * @min 1
 * @max 99
 */
export type Limit = number;

export type Cursor = string | null;

/** @format uuid */
export type Uuid = string;

export type GenericString = string;

export interface FanVideoEntity {
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  fanId: string;
  /** @format uuid */
  videoId: string;
  playlistId?: string | null;
  marker?: number;
  totalHits?: number;
  totalHitsSecs?: number;
  duration?: number;
  updatedAt: string;
  views?: number;
  totalViews?: number;
}

export interface PagedFanVideoEntity {
  items: {
    /** @format uuid */
    accountId: string;
    /** @format uuid */
    fanId: string;
    /** @format uuid */
    videoId: string;
    playlistId?: string | null;
    marker?: number;
    totalHits?: number;
    totalHitsSecs?: number;
    duration?: number;
    updatedAt: string;
    views?: number;
  }[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export interface FanVideoEntityWithPollingInfo {
  /** @format uuid */
  accountId?: string;
  /** @format uuid */
  fanId?: string;
  /** @format uuid */
  videoId?: string;
  playlistId?: string | null;
  marker?: number;
  totalHits?: number;
  totalHitsSecs?: number;
  duration?: number;
  updatedAt?: string;
  views?: number;
  totalViews?: number;
  videoHitInterval: number;
  watchPercentToConsiderViewed?: number;
}

export type FanVideoEntityArray = FanVideoEntity[];

export interface FanVideoHit {
  marker?: number;
  playlistId?: string | null;
}

export interface Hit {
  videoHitInterval: number;
  watchPercentToConsiderViewed: number;
}

export interface CurrentViewers {
  viewers: number;
}

export interface FanEventEntity {
  accountId: string;
  fanId: string;
  eventId: string;
  totalHits: number;
  totalHitsSecs: number;
  createdAt: string;
  updatedAt: string;
}

export interface FakeFanEventLogin {
  accessToken: string;
}

export interface PagedFanEventEntity {
  items: FanEventEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type FanEventEntityArray = FanEventEntity[];

export interface FanEventEntityWithPollingInfo {
  accountId?: string;
  fanId?: string;
  eventId?: string;
  totalHits?: number;
  totalHitsSecs?: number;
  createdAt?: string;
  updatedAt?: string;
  videoHitInterval: number;
}

export interface FanPlaylistHit {
  currentVideoPlaylistRank?: number;
}

export interface FanPlaylistEntity {
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  fanId: string;
  /** @format uuid */
  playlistId: string;
  currentVideoPlaylistRank?: number;
  updatedAt: string;
}

export interface PagedFanPlaylistEntity {
  items: FanPlaylistEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type FanPlaylistEntityArray = FanPlaylistEntity[];

export interface VideoStatsEntity {
  accountId: string;
  videoId: string;
  /** @format uuid */
  playlistId?: string | null;
  views: number;
  duration: number;
  createdAt: string;
  updatedAt: string;
}

export interface PagedVideoStatsEntity {
  items: VideoStatsEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type VideoStatsEntityArray = VideoStatsEntity[];

export interface VideoStatsSchemaWithPollingInfo {
  accountId?: string;
  videoId?: string;
  /** @format uuid */
  playlistId?: string | null;
  views?: number;
  duration?: number;
  createdAt?: string;
  updatedAt?: string;
  videoHitInterval: number;
}

export interface MainApiVideoEntity {
  /** @format uuid */
  id: string;
  /** @format uuid */
  AccountId: string | null;
  description: string | null;
  duration: number | null;
  filename: string | null;
  fullDescription: string | null;
  name: string;
  archiveData?: any;
  captions?: any;
  meta?: any;
  poster: string | null;
  portraitThumbnail: string | null;
  /** @format date-time */
  publicationDate: string | null;
  status: "none" | "original" | "in_progress" | "encoded" | "archived" | "vendor";
  technicalDescription: string | null;
  /** @format uuid */
  PlaylistId: string | null;
  playlistRank: number;
  /** @format date-time */
  updatedAt: string;
  /** @format date-time */
  createdAt: string;
  url: string | null;
  vendorApiKey: string | null;
  vendorName: "jwplayer" | "awsplayer" | "dailymotion" | "youtube" | "twitch";
  vendorVideoId: string | null;
  visibility: "public" | "private";
  isPaid: boolean;
  marker: number;
  isNew: boolean;
  hasBeenViewed: boolean;
  ratio: "sixteen-nine" | "nine-sixteen" | null;
  Playlist?: {
    /** @format uuid */
    id: string;
    name: string;
    /** @format uuid */
    accountId: string;
    /** @format uuid */
    CategoryId: string;
    description: string | null;
    thumbnail: string | null;
    portraitThumbnail: string | null;
    heroImageDesktop: string | null;
    heroImageMobile: string | null;
    currentVideoPlaylistRank?: number;
    newEpisodes?: number;
    hasBeenViewed?: boolean;
    numberOfVideos?: number;
    isFavorite?: boolean;
    /** @format date-time */
    createdAt: string;
    /** @format date-time */
    updatedAt: string;
  };
  ItemProducts?: PaymentProduct[];
  PaymentOffers?: PaymentOfferEntity[];
  Tags?: MainApiTagEntity[];
  Categories?: MainApiCategoryEntity[];
  VideoCategorySubCategories?: MainApiVideoCategorySubCategoryEntity[];
}

export interface PagedMainApiVideoEntity {
  items: MainApiVideoEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type PagedMainApiVideoEntityAlt =
  | MainApiVideoEntity[]
  | {
      items: MainApiVideoEntity[];
      cursor: {
        before: string | null;
        after: string | null;
      };
    };

export interface MainApiStreamEntity {
  /** @format uuid */
  id: string;
  isRecordReady?: boolean;
  key?: string;
  mapCoordinates?: any;
  name?: string | null;
  offset?: number | null;
  options?: any;
  recordName?: string | null;
  /** @format date-time */
  startedAt?: string;
  streamable?: string | null;
  streamableId?: string | null;
  streamType?: "main" | "backup" | "additionnal" | null;
  token?: string;
  url?: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface MainApiCompetitionEntity {
  /** @format uuid */
  id: string;
  name: string;
  /** @maxLength 255 */
  slug?: string | null;
  displayOrder?: number | null;
  /** @format date-time */
  startDate?: string | null;
  /** @format date-time */
  endDate?: string | null;
  /** @maxLength 255 */
  providerId?: string | null;
  /** @format uuid */
  SportId?: string;
  /** @format date-time */
  createdAt?: string;
  /** @format date-time */
  updatedAt?: string;
  Tags?: MainApiTagEntity[];
}

export type PagedMainApiCompetitionEntityArrayAlt =
  | MainApiCompetitionEntity[]
  | {
      items: MainApiCompetitionEntity[];
      cursor: {
        before: string | null;
        after: string | null;
      };
    };

export interface MainApiEventEntity {
  AccountId: string | null;
  activatedModules: number[] | null;
  advertisingBanner?: any;
  /** @format uuid */
  calendarEventId: string | null;
  /** @format uuid */
  ChatId: string | null;
  /** @format date-time */
  createdAt: string;
  dailymotionLiveStreamId: string | null;
  description: string | null;
  /** @format date-time */
  endDate: string | null;
  facebookPlaylistId: string | null;
  geoBlockingMapping: {
    whitelist?: string[];
    blacklist?: string[];
  } | null;
  fullDescription: string | null;
  hashtag: string | null;
  /** @format uuid */
  id: string;
  location: string | null;
  name: string;
  options?: any;
  organiserName: string | null;
  placeholder: {
    poster?: string;
    url?: string;
  } | null;
  ParentId: string | null;
  refereeName: string | null;
  score: {
    teamIn?: string | null;
    teamOut?: string | null;
    scoreIn?: string | null;
    scoreOut?: string | null;
  };
  RoundId: string | null;
  shareUrl: string | null;
  /** @format uuid */
  SportId: string;
  /** @format date-time */
  startDate: string | null;
  state: "liveOn" | "liveOff" | "replay" | "liveDailymotion" | "liveYoutube" | "awsLive" | null;
  stats?: any;
  step: "starting" | "break" | "end" | null;
  streamIds: string[] | null;
  TeamClientId: string | null;
  ThemeId: string | null;
  /** @format date-time */
  updatedAt: string;
  VideoId: string | null;
  visibility: "public" | "private" | null;
  youtubeLiveStreamId: string | null;
  isPaid?: boolean;
  ItemProducts?: PaymentProduct[];
  Challengers?: {
    /** @format date-time */
    birthday: string | null;
    coachName: string | null;
    country: string | null;
    /** @format date-time */
    createdAt: string;
    firstName: string | null;
    gamesPlayed: number | null;
    gender: "standard" | "team" | "teammate" | null;
    goals: number | null;
    height: number | null;
    history: string | null;
    homeFieldName: string | null;
    /** @format uuid */
    id: string;
    isJunior: boolean | null;
    jerseyNumber: number | null;
    jerseyPicture: string | null;
    linkShop: string | null;
    linkStats: string | null;
    name: string;
    optaId: number | null;
    picture: string | null;
    pictureUrl: string | null;
    profileOptions?: any;
    providerId: string | null;
    role: string | null;
    shortName: string | null;
    shots: number | null;
    smallPictureUrl: string | null;
    SportId: string | null;
    statsId: number | null;
    targetedShots: number | null;
    /** @format uuid */
    TeamId: string | null;
    type: "F" | "M" | "MIXTE" | null;
    /** @format date-time */
    updatedAt: string;
    weight: number | null;
  }[];
  PaymentOffers?: PaymentOfferEntity[];
  Tags?: MainApiTagEntity[];
  Categories?: MainApiCategoryEntity[];
  EventCategorySubCategories?: {
    /** @format uuid */
    id: string;
    /** @format uuid */
    CategoryId: string;
    /** @format uuid */
    SubCategoryId: string;
    /** @format uuid */
    EventId: string;
    /** @format date-time */
    createdAt: string;
    /** @format date-time */
    updatedAt: string;
    SubCategory?: MainApiSubCategoryEntity;
  }[];
  Streams:
    | MainApiStreamEntity[]
    | {
        url: string | null;
        streamType: string;
        awsStream: "STANDBY" | "ONAIR" | "ARCHIVED" | null;
      }[];
  Round: {
    /** @format uuid */
    CompetitionId: string;
    competitionOrder: number;
    /** @format date-time */
    createdAt: string;
    /** @format uuid */
    id: string;
    name: string;
    /** @format uuid */
    SeasonId: string;
    stage?: string | null;
    /** @format date-time */
    updatedAt: string;
    Competition?: MainApiCompetitionEntity;
  };
  Sport: {
    /** @format date-time */
    createdAt: string;
    description?: string | null;
    /** @format uuid */
    id: string;
    name: string;
    periods: "multiple" | "none" | null;
    sportsFieldFilename: string | null;
    svgSpriteFilename: string | null;
    timelineType: "single" | "double" | null;
    /** @format date-time */
    updatedAt: string;
  };
  Markers?: {
    /** @format uuid */
    ChallengerId: string | null;
    /** @format date-time */
    createdAt: string;
    dailymotionRepostId: string | null;
    description: string | null;
    endTime: number;
    /** @format date-time */
    endTimeTs: string | null;
    /** @format uuid */
    EventId: string | null;
    facebookVideoId: string | null;
    gameTime: string | null;
    /** @format uuid */
    id: string;
    /** @format uuid */
    MarkerTypeId: string | null;
    notifications?: any;
    options?: any;
    /** @format uuid */
    ParentId: string | null;
    shouldDisplayChallengerProfile: boolean | null;
    startTime: number;
    /** @format date-time */
    startTimeTs: string | null;
    /** @format date-time */
    updatedAt: string;
  }[];
  MarkerTypes?: {
    /** @format date-time */
    createdAt: string;
    defaultTimeEnd: number | null;
    defaultTimeStart: number | null;
    description: string | null;
    displayOrder: number | null;
    endGameMinute: number | null;
    ico: string | null;
    icoDark: string | null;
    icoSettings: string | null;
    /** @format uuid */
    id: string;
    isMain: boolean | null;
    name: string;
    optaOffsetEnd: number | null;
    optaOffsetStart: number | null;
    pushNotification: boolean | null;
    shortcut: string | null;
    /** @format uuid */
    SportId: string | null;
    startGameMinute: number | null;
    /** @format date-time */
    updatedAt: string;
  }[];
  Video?: MainApiVideoEntity;
  Theme?: {
    AccountId: string | null;
    /** @format date-time */
    createdAt: string;
    customMarkerTypes?: any;
    customMarkerTypesSvgSprite: string | null;
    /** @format uuid */
    id: string;
    name: string;
    style?: any;
    /** @format date-time */
    updatedAt: string;
  };
  EventAttachments?: {
    /** @format date-time */
    createdAt: string;
    /** @format uuid */
    EventId: string;
    ext: string | null;
    fileName: string | null;
    /** @format uuid */
    id: string;
    name: string;
    type: string | null;
    /** @format date-time */
    updatedAt: string;
    url: string;
  }[];
  translations?: {
    /** @format date-time */
    createdAt: string;
    description: string | null;
    fullDescription: string | null;
    /** @format uuid */
    id: string;
    languageId: string | null;
    location: string | null;
    modelId: string | null;
    name: string;
    refereeName: string | null;
    /** @format date-time */
    updatedAt: string;
  }[];
}

export interface PagedMainApiEventEntity {
  items: MainApiEventEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type PagedMainApiEventEntityAlt =
  | MainApiEventEntity[]
  | {
      items: MainApiEventEntity[];
      cursor: {
        before: string | null;
        after: string | null;
      };
    };

export interface MainApiTagEntity {
  /** @format uuid */
  id: string;
  tagType: string | null;
  name: string;
  options?: any;
  /** @format uuid */
  AccountId: string;
  placeholders: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  ItemTag?: MainApiItemTagEntity;
  PaymentOffers?: PaymentOfferEntity;
}

export interface MainApiItemTagEntity {
  /** @format uuid */
  id: string;
  /** @format uuid */
  tagId: string;
  taggable: string;
  /** @format uuid */
  taggableId: string;
}

export interface MainApiCategoryEntity {
  /** @format uuid */
  id: string;
  name: string;
  /** @format uuid */
  accountId: string;
  description: string | null;
  thumbnail: string | null;
  portraitThumbnail: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  isFavorite?: boolean;
  heroPortrait?: string;
  heroLandscape?: string;
}

export type MainApiCategoryEntityArray = MainApiCategoryEntity[];

export interface MainApiVideoCategorySubCategoryEntity {
  /** @format uuid */
  id: string;
  /** @format uuid */
  CategoryId: string;
  /** @format uuid */
  SubCategoryId: string;
  /** @format uuid */
  VideoId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  SubCategory?: MainApiSubCategoryEntity;
}

export interface MainApiSubCategoryEntity {
  /** @format uuid */
  id: string;
  name: string;
  /** @format uuid */
  accountId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export type MainApiSubCategoryEntityArray = MainApiSubCategoryEntity[];

export interface MainApiPlaylistEntity {
  /** @format uuid */
  id: string;
  name: string;
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  CategoryId: string;
  description: string | null;
  thumbnail: string | null;
  portraitThumbnail: string | null;
  heroImageDesktop: string | null;
  heroImageMobile: string | null;
  currentVideoPlaylistRank?: number;
  newEpisodes?: number;
  hasBeenViewed?: boolean;
  numberOfVideos?: number;
  isFavorite?: boolean;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Videos?:
    | MainApiVideoEntity[]
    | {
        items: MainApiVideoEntity[];
        cursor: {
          before: string | null;
          after: string | null;
        };
      };
}

export interface PagedMainApiPlaylistEntity {
  items: MainApiPlaylistEntity[];
  cursor: {
    before: string | null;
    after: string | null;
  };
}

export type PagedMainApiPlaylistEntityAlt =
  | MainApiPlaylistEntity[]
  | {
      items: MainApiPlaylistEntity[];
      cursor: {
        before: string | null;
        after: string | null;
      };
    };

export interface PaymentProduct {
  /** @default "Product" */
  entityType?: "Product";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  id: string;
  /** @format uuid */
  productId: string;
  /** @default "draft" */
  status: "draft" | "published" | "archived";
  name: string;
  data?: {
    description?: string;
    weight?: string;
  };
  isStandard?: boolean;
  /** @default "Product" */
  _type?: "Product";
  /** @format date-time */
  created: string;
  /** @format date-time */
  updated: string;
  paymentOffers?: PaymentOffer[];
  profiles?: {
    /** @format uuid */
    id: string;
    name: string;
    description?: string | null;
    type: "payment" | "marketing" | "men-favorite-club" | "women-favorite-club" | "club-subscriber" | null;
    category: "communication" | "competition" | "favorite-men-club" | "favorite-women-club" | "premium" | null;
    providerId: string;
    /** @format uuid */
    AccountId: string;
    retainOnLogin: boolean;
    /** @format date-time */
    createdAt: string;
    /** @format date-time */
    updatedAt: string;
  }[];
}

export interface PaymentOffer {
  /** @default "PaymentOffer" */
  entityType?: "PaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  productId: string;
  /** @format uuid */
  paymentOfferId: string;
  /** @default "active" */
  status?: "active" | "archived";
  provider: "stripe" | "google" | "apple";
  providerPaymentOfferId: string;
  name: string;
  data?: {
    isLimitedDuration?: boolean;
    population?: string;
    weigth?: string;
    type?: "recurring" | "one_time";
    price?: number;
    currency?: string;
    interval?: "month" | "year" | "week" | "day";
    intervalCount?: number;
  };
  isPPV?: boolean;
  /** @default "PaymentOffer" */
  _type?: "PaymentOffer";
  duration?: number;
  /** @format date-time */
  created: string;
  /** @format date-time */
  updated: string;
  associatedProfiles?: {
    /** @format uuid */
    id: string;
    name: string;
    description?: string | null;
    type: "payment" | "marketing" | "men-favorite-club" | "women-favorite-club" | "club-subscriber" | null;
    category: "communication" | "competition" | "favorite-men-club" | "favorite-women-club" | "premium" | null;
    providerId: string;
    /** @format uuid */
    AccountId: string;
    retainOnLogin: boolean;
    /** @format date-time */
    createdAt: string;
    /** @format date-time */
    updatedAt: string;
  }[];
}

export interface PaymentOfferEntity {
  /** @format uuid */
  id: string;
  paymentOfferType: "sku" | "plan";
  name: string;
  stripeId: string | null;
  appleId: string | null;
  options?: any;
  /** @format uuid */
  AccountId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  weight?: number;
}

export enum Period {
  Daily = "daily",
  Weekly = "weekly",
  Monthly = "monthly",
  Quarterly = "quarterly",
  Semestral = "semestral",
  Yearly = "yearly",
}

export interface MostViewedVideo {
  /** @format uuid */
  id: string;
  /** @min 0 */
  points: number;
}

export interface ShowMostViewedVideo {
  /** @format uuid */
  accountId: string;
  period: Period;
  updatedAt: string;
  videos: MostViewedVideo[];
}

export type ShowMostViewedVideoArray = ShowMostViewedVideo[];

export interface MostViewedPlaylist {
  /** @format uuid */
  id: string;
  /** @min 0 */
  points: number;
}

export interface ShowMostViewedPlaylist {
  /** @format uuid */
  accountId: string;
  period: Period;
  updatedAt: string;
  playlists: MostViewedVideo[];
}

export type ShowMostViewedPlaylistArray = ShowMostViewedPlaylist[];

import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, HeadersDefaults, ResponseType } from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, "body" | "method" | "query" | "path">;

export interface ApiConfig<SecurityDataType = unknown> extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({ securityWorker, secure, format, ...axiosConfig }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: axiosConfig.baseURL || "" });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(params1: AxiosRequestConfig, params2?: AxiosRequestConfig): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method && this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === "object") {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== "string") {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Video-stats-service
 * @version 1.0.0
 *
 * This is the Video-stats-service OpenAPI 3.0 specification.
 *
 * Some useful links:
 *   - [The source API definition](/docs/spec)
 */
export class VideoAPI<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  fanVideos = {
    /**
     * @description If this fans doen't have statistics on the videos, it will return the video's total views.
     *
     * @tags videos
     * @name IndexFanVideo
     * @summary return information about the video stats for a fan
     * @request GET:/fanVideos
     * @secure
     */
    indexFanVideo: (
      query?: {
        /** video filter by id */
        videoId?: UuidArray;
        /** page limit, omitted if video ids is present */
        limit?: Limit;
        /** before cursor */
        before?: Cursor;
        /** after cursor */
        after?: Cursor;
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanVideoEntity, Exception>({
        path: `/fanVideos`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description If this fans doen't have statistics on this video, it will return the video's views and the hitInterval
     *
     * @tags videos
     * @name ShowFanVideo
     * @summary return information about the stats of one video for a fan
     * @request GET:/fanVideos/{videoId}
     * @secure
     */
    showFanVideo: (videoId: Uuid, params: RequestParams = {}) =>
      this.request<FanVideoEntityWithPollingInfo, Exception>({
        path: `/fanVideos/${videoId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint will update the stats of the videos, including current viewers, total viewers, daily hits, among other.
     *
     * @tags videos
     * @name FanVideoHit
     * @summary add a hit to the video and save the status of the video playback
     * @request POST:/fanVideos/{videoId}
     * @secure
     */
    fanVideoHit: (videoId: Uuid, data: FanVideoHit, params: RequestParams = {}) =>
      this.request<Hit, Exception>({
        path: `/fanVideos/${videoId}`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint will update the stats of the videos, including current viewers, total viewers, daily hits, among other.
     *
     * @tags videos
     * @name FanVideoHitPublic
     * @summary add a hit to the video and save the status of the video playback
     * @request POST:/fanVideos/public/{videoId}
     */
    fanVideoHitPublic: (videoId: Uuid, data: FanVideoHit, params: RequestParams = {}) =>
      this.request<Hit, Exception>({
        path: `/fanVideos/public/${videoId}`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint count the number of viewers of the video in the last 5 seconds.
     *
     * @tags videos
     * @name CurrentVideoViewers
     * @summary return the users playing this video in the last 5 secs
     * @request GET:/fanVideos/{videoId}/viewers
     */
    currentVideoViewers: (videoId: Uuid, params: RequestParams = {}) =>
      this.request<CurrentViewers, Exception>({
        path: `/fanVideos/${videoId}/viewers`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  fanEvents = {
    /**
     * @description This endpoint returns a array of fan events
     *
     * @tags events
     * @name IndexFanEvent
     * @summary return information about the event stats for a fan
     * @request GET:/fanEvents
     * @secure
     */
    indexFanEvent: (
      query?: {
        /** event filter by id */
        eventId?: UuidArray;
        /** page limit, omitted if events ids is present */
        limit?: Limit;
        /** before cursor */
        before?: Cursor;
        /** after cursor */
        after?: Cursor;
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanEventEntity, Exception>({
        path: `/fanEvents`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint includes the hitInterval in the response.
     *
     * @tags events
     * @name ShowFanEvent
     * @summary return information about the stats of one event for a fan
     * @request GET:/fanEvents/{eventId}
     * @secure
     */
    showFanEvent: (eventId: Uuid, params: RequestParams = {}) =>
      this.request<FanEventEntityWithPollingInfo, Exception>({
        path: `/fanEvents/${eventId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint will update the stats of the event, including current viewers.
     *
     * @tags events
     * @name FanEventHit
     * @summary add a hit to the event
     * @request POST:/fanEvents/{eventId}
     * @secure
     */
    fanEventHit: (eventId: Uuid, params: RequestParams = {}) =>
      this.request<Hit, Exception>({
        path: `/fanEvents/${eventId}`,
        method: "POST",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint return a access token that you can use to count views on lives for not logged users.
     *
     * @tags events
     * @name FanEventLogin
     * @summary get a user token for unauthenticated users
     * @request POST:/fanEvents/login
     */
    fanEventLogin: (params: RequestParams = {}) =>
      this.request<FakeFanEventLogin, Exception>({
        path: `/fanEvents/login`,
        method: "POST",
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint count the number of viewers of the event in the last 5 seconds.
     *
     * @tags events
     * @name CurrentEventViewers
     * @summary return the users playing this event in the last 5 secs
     * @request GET:/fanEvents/{eventId}/viewers
     */
    currentEventViewers: (eventId: Uuid, params: RequestParams = {}) =>
      this.request<CurrentViewers, Exception>({
        path: `/fanEvents/${eventId}/viewers`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  fanPlaylists = {
    /**
     * @description This endpoint returns a array of fan playlists
     *
     * @tags playlists
     * @name IndexFanPlaylists
     * @summary return information about the playlists stats for a fan
     * @request GET:/fanPlaylists
     * @secure
     */
    indexFanPlaylists: (
      query?: {
        /** playlist filter by id */
        playlistId?: UuidArray;
        /** page limit, omitted if playlist ids is present */
        limit?: Limit;
        /** before cursor */
        before?: Cursor;
        /** after cursor */
        after?: Cursor;
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedFanPlaylistEntity, Exception>({
        path: `/fanPlaylists`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint includes the hitInterval in the response.
     *
     * @tags playlists
     * @name ShowFanPlaylist
     * @summary return information about the stats of one playlist for a fan
     * @request GET:/fanPlaylists/{playlistId}
     * @secure
     */
    showFanPlaylist: (playlistId: Uuid, params: RequestParams = {}) =>
      this.request<FanPlaylistEntity, Exception>({
        path: `/fanPlaylists/${playlistId}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint will update the stats of the playlist, including the current playlist rank.
     *
     * @tags playlists
     * @name FanPlaylistHit
     * @summary add a hit to the playlist
     * @request POST:/fanPlaylists/{playlistId}
     * @secure
     */
    fanPlaylistHit: (playlistId: Uuid, data: FanPlaylistHit, params: RequestParams = {}) =>
      this.request<void, Exception>({
        path: `/fanPlaylists/${playlistId}`,
        method: "POST",
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),
  };
  videos = {
    /**
     * @description returns a array of video stats.
     *
     * @tags videos
     * @name IndexVideoStats
     * @summary return information about the video stats
     * @request GET:/videos/stats
     */
    indexVideoStats: (
      query?: {
        /** video filter by id */
        videoId?: UuidArray;
        /** page limit, omitted if video ids is present */
        limit?: Limit;
        /** before cursor */
        before?: Cursor;
        /** after cursor */
        after?: Cursor;
      },
      params: RequestParams = {},
    ) =>
      this.request<PagedVideoStatsEntity, Exception>({
        path: `/videos/stats`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),

    /**
     * @description returns a single entity.
     *
     * @tags videos
     * @name ShowVideoStats
     * @summary return information about requested video stats
     * @request GET:/videos/stats/{videoId}
     */
    showVideoStats: (videoId: Uuid, params: RequestParams = {}) =>
      this.request<VideoStatsSchemaWithPollingInfo, Exception>({
        path: `/videos/stats/${videoId}`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  events = {
    /**
     * @description returns the requested player event and cache it by 15 seconds.
     *
     * @tags events
     * @name ShowEventPlayer
     * @summary blind proxy to the main-api service
     * @request GET:/events/{id}/player
     * @secure
     */
    showEventPlayer: (id: Uuid, params: RequestParams = {}) =>
      this.request<MainApiEventEntity, Exception>({
        path: `/events/${id}/player`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
  mostViewed = {
    /**
     * @description returns the most viewed videos for the requested period
     *
     * @tags most-viewed
     * @name ShowMostViewedVideos
     * @summary returns the most viewed videos for the requested period
     * @request GET:/mostViewed/{period}/videos
     */
    showMostViewedVideos: (period: Period, params: RequestParams = {}) =>
      this.request<ShowMostViewedVideo, Exception>({
        path: `/mostViewed/${period}/videos`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description returns a list of most-vieved videos for each valid period
     *
     * @tags most-viewed
     * @name IndexMostViewedVideos
     * @summary returns the most viewed videos for all valid periods
     * @request GET:/mostViewed/videos
     */
    indexMostViewedVideos: (params: RequestParams = {}) =>
      this.request<ShowMostViewedVideoArray, Exception>({
        path: `/mostViewed/videos`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description returns the most viewed playlists for the requested period
     *
     * @tags most-viewed
     * @name ShowMostViewedPlaylists
     * @summary returns the most viewed playlists for the requested period
     * @request GET:/mostViewed/{period}/playlists
     */
    showMostViewedPlaylists: (period: Period, params: RequestParams = {}) =>
      this.request<ShowMostViewedPlaylist, Exception>({
        path: `/mostViewed/${period}/playlists`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * @description returns a list of most-vieved playlists for each valid period
     *
     * @tags most-viewed
     * @name IndexMostViewedPlaylists
     * @summary returns the most viewed playlist for all valid periods
     * @request GET:/mostViewed/playlists
     */
    indexMostViewedPlaylists: (params: RequestParams = {}) =>
      this.request<ShowMostViewedPlaylistArray, Exception>({
        path: `/mostViewed/playlists`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  health = {
    /**
     * @description useful to check the service health
     *
     * @tags auth
     * @name GetHealth
     * @request GET:/health
     */
    getHealth: (params: RequestParams = {}) =>
      this.request<StatusOK, any>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
}
