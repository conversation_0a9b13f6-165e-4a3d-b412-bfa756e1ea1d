/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface BusinessPlayer {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @maxLength 255 */
  organiserName: string;
  state?: "liveOn" | "liveOff" | "replay" | "awsLive" | null;
  placeholder?: any;
  options?: any;
  /** @format date-time */
  startDate?: string | null;
  /** @format date-time */
  endDate?: string | null;
  activatedModules?: number[] | null;
  visibility?: "public" | "private" | null;
  tags?: any;
  /** @format uuid */
  AccountId?: string | null;
  streamIds?: string[] | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Video?: Video;
  Theme?: Theme;
  Chat?: Chat;
  Chapters?: Chapter[];
  Jobs?: Job[];
  Streams?: Stream[];
  Clips?: Clip[];
}

export interface Category {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  description?: string | null;
  /** @maxLength 255 */
  thumbnail?: string | null;
  /** @maxLength 255 */
  portraitThumbnail?: string | null;
  /** @maxLength 255 */
  heroPortrait?: string | null;
  /** @maxLength 255 */
  heroLandscape?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Videos?: Video[];
  Events?: Event[];
  SubCategories?: SubCategory[];
  Playlists?: Playlist[];
}

export interface ChallengerTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  shortName?: string | null;
  /** @maxLength 255 */
  homeFieldName?: string | null;
  /** @maxLength 255 */
  coachName?: string | null;
  /** @maxLength 255 */
  role?: string | null;
  history?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  challenger?: Challenger;
  language?: Language;
}

export interface Challenger {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  shortName?: string | null;
  /** @maxLength 255 */
  homeFieldName?: string | null;
  history?: string | null;
  /** @maxLength 255 */
  coachName?: string | null;
  type?: "standard" | "team" | "teammate" | null;
  /** @maxLength 255 */
  country?: string | null;
  /** @maxLength 255 */
  picture?: string | null;
  /** @maxLength 255 */
  pictureUrl?: string | null;
  /** @maxLength 255 */
  smallPictureUrl?: string | null;
  /** @format int32 */
  jerseyNumber?: number | null;
  gender?: "F" | "M" | "MIXED" | null;
  isJunior?: boolean | null;
  /** @maxLength 255 */
  providerId?: string | null;
  profileOptions?: any;
  /** @maxLength 255 */
  role?: string | null;
  /** @maxLength 255 */
  jerseyPicture?: string | null;
  /** @maxLength 255 */
  firstName?: string | null;
  /** @format date-time */
  birthday?: string | null;
  /** @format int32 */
  statsId?: number | null;
  /** @format int32 */
  optaId?: number | null;
  /** @maxLength 255 */
  linkShop?: string | null;
  /** @maxLength 255 */
  linkStats?: string | null;
  /** @format int32 */
  height?: number | null;
  /** @format int32 */
  weight?: number | null;
  /** @format int32 */
  shots?: number | null;
  /** @format int32 */
  targetedShots?: number | null;
  /** @format int32 */
  gamesPlayed?: number | null;
  /** @format int32 */
  goals?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: ChallengerTranslation[];
  Teammates?: Challenger[];
  Team?: Challenger;
  Events?: Event[];
  Squads?: TeammateSquad[];
  TeammateSquads?: TeammateSquad[];
  Sport?: Sport;
  Markers?: Marker[];
  ChallengerCompetitions?: ChallengerCompetitions[];
  Tags?: Tag[];
  ItemTags?: ItemTag[];
}

export interface ChallengerCompetitions {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Season?: Season;
  Competition?: Competition;
  Challenger?: Challenger;
}

export interface ChaptersSpeakers {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface Chapter {
  /** @format uuid */
  id?: string | null;
  name?: string | null;
  label?: string | null;
  /** @format float */
  time?: number | null;
  transcript?: string | null;
  /** @format int32 */
  level: number;
  attachments?: any;
  visibility?: "public" | "private" | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  BusinessPlayer?: BusinessPlayer;
  Speakers?: Speaker[];
}

export interface ChatLine {
  /** @format uuid */
  id?: string | null;
  content?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Chat?: Chat;
  ChatUser?: ChatUser;
}

export interface ChatUser {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  pseudonym: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Chat?: Chat;
}

export interface Chat {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  BusinessPlayer?: BusinessPlayer;
  ChatLines?: ChatLine[];
  ChatUsers?: ChatUser[];
}

export interface Clip {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slugName?: string | null;
  /** @maxLength 255 */
  poster?: string | null;
  meta: any;
  state?: "none" | "error" | "ready" | "in_progress" | null;
  scope?: "customer" | "viewer" | null;
  /** @maxLength 255 */
  url?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  /** @format uuid */
  UserId?: string | null;
  /** @maxLength 255 */
  shareUrl?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Event?: Event;
  BusinessPlayer?: BusinessPlayer;
  ClipSkin?: ClipSkin;
  Jobs?: Job[];
  Tags?: Tag[];
  ItemTags?: ItemTag[];
}

export interface ClipSkin {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  prerollUrl?: string | null;
  /** @maxLength 255 */
  postrollUrl?: string | null;
  watermark?: any;
  /** @format uuid */
  AccountId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface CompetitionTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  competition?: Competition;
  language?: Language;
}

export interface Competition {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  slug?: string | null;
  /** @format int32 */
  displayOrder?: number | null;
  /** @format date-time */
  startDate?: string | null;
  /** @format date-time */
  endDate?: string | null;
  /** @maxLength 255 */
  providerId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: CompetitionTranslation[];
  Rounds?: Round[];
  ChallengerCompetitions?: ChallengerCompetitions[];
  Sport?: Sport;
  Tags?: Tag[];
  ItemTags?: ItemTag[];
}

export interface DataProviderMapping {
  /** @format uuid */
  id?: string | null;
  dataProviderName:
    | "rugbyunion-api"
    | "opta"
    | "idalgo"
    | "pandaos"
    | "salzburg.opta.f9"
    | "hbs.opta.f9"
    | "asse.opta.f9"
    | "gsports.paok"
    | "beinconnect.opta.f9";
  /** @format int32 */
  dataProviderStatsId: number;
  /** @format int32 */
  weight?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  MarkerType?: MarkerType;
}

export interface EventCategorySubCategory {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Category?: Category;
  SubCategory?: SubCategory;
  Event?: Event;
}

export interface EventCategory {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Category?: Category;
  Event?: Event;
}

export interface EventsChallengers {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface EventTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  refereeName?: string | null;
  /** @maxLength 255 */
  location?: string | null;
  fullDescription?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  event?: Event;
  language?: Language;
}

export interface Event {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  organiserName?: string | null;
  /** @maxLength 255 */
  location?: string | null;
  /** @maxLength 255 */
  refereeName?: string | null;
  description?: string | null;
  /** @format date-time */
  startDate?: string | null;
  /** @format date-time */
  endDate?: string | null;
  state?: "liveOn" | "liveOff" | "replay" | "liveDailymotion" | "liveYoutube" | "awsLive" | "harmonic" | null;
  placeholder?: any;
  activatedModules?: number[] | null;
  geoBlockingMapping?: any;
  /** @maxLength 255 */
  dailymotionLiveStreamId?: string | null;
  /** @maxLength 255 */
  youtubeLiveStreamId?: string | null;
  /** @maxLength 255 */
  hashtag?: string | null;
  options?: any;
  stats?: any;
  score?: any;
  /** @maxLength 255 */
  facebookPlaylistId?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  visibility?: "public" | "private" | null;
  /** @format uuid */
  ChatId?: string | null;
  /** @maxLength 255 */
  shareUrl?: string | null;
  /** @maxLength 255 */
  calendarEventId?: string | null;
  streamIds?: string[] | null;
  advertisingBanner?: any;
  step?: "starting" | "break" | "end" | null;
  fullDescription?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: EventTranslation[];
  Sport?: Sport;
  Markers?: Marker[];
  Video?: Video;
  VideoEvents?: VideoEvent[];
  Round?: Round;
  Challengers?: Challenger[];
  TeamClient?: Challenger;
  Theme?: Theme;
  Clips?: Clip[];
  Jobs?: Job[];
  EventAttachments?: EventAttachment[];
  Streams?: Stream[];
  Tags?: Tag[];
  ItemTags?: ItemTag[];
  PaymentOffers?: PaymentOffer[];
  Parent?: Event;
  Children?: Event[];
  Categories?: Category[];
  SubCategories?: SubCategory[];
  EventCategorySubCategories?: EventCategorySubCategory[];
  EventCategories?: EventCategory[];
  ItemProducts?: Product[];
}

export interface EventAttachment {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  type?: string | null;
  /** @maxLength 255 */
  fileName?: string | null;
  /** @maxLength 255 */
  ext?: string | null;
  /** @maxLength 255 */
  url: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface Job {
  /** @format uuid */
  id?: string | null;
  state?: "waiting" | "in_progress" | "finished" | "error" | "cancelled" | null;
  task:
    | "encoding.tasks.convert"
    | "encoding.tasks.vod_transcode"
    | "encoding.tasks.transmux_to_mp4"
    | "encoding.tasks.live_transcode"
    | "encoding.tasks.extract_from_dvr"
    | "encoding.tasks.transfer_resource_to_s3"
    | "encoding.tasks.transfer_dailymotion_resource_to_s3"
    | "encoding.tasks.concat"
    | "encoding.tasks.simulcast_target"
    | "encoding.tasks.revoke_task"
    | "encoding.tasks.clean_dvr"
    | "encoding.tasks.upload_record"
    | "encoding.tasks.generate_thumbnails"
    | "encoding.tasks.periodic_request"
    | "encoding.tasks.periodic_rabbitmq_message"
    | "encoding.tasks.periodic_sns_event"
    | "encoding.tasks.sns_event"
    | "encoding.tasks.clip"
    | "encoding.tasks.stream_for_clip"
    | "parser.tasks.markers"
    | "parser.tasks.stats"
    | "parser.tasks.match_stats"
    | "trigger.tasks.parser";
  /** @maxLength 255 */
  resourceType?: string | null;
  /** @format uuid */
  resourceId?: string | null;
  /** @format uuid */
  workerTaskId: string;
  payload?: any;
  /** @format uuid */
  UserId?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Event?: Event;
  BusinessPlayer?: BusinessPlayer;
  Target?: Target;
  Video?: Video;
  Clip?: Clip;
}

export interface Language {
  /** @format uuid */
  id?: string | null;
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface MarkersChildren {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface Marker {
  /** @format uuid */
  id?: string | null;
  /** @format float */
  startTime: number;
  /** @format float */
  endTime: number;
  description?: string | null;
  /** @maxLength 255 */
  dailymotionRepostId?: string | null;
  /** @maxLength 255 */
  gameTime?: string | null;
  /** @format date-time */
  startTimeTs?: string | null;
  /** @format date-time */
  endTimeTs?: string | null;
  notifications?: number[] | null;
  shouldDisplayChallengerProfile?: boolean | null;
  /** @maxLength 255 */
  facebookVideoId?: string | null;
  options?: any;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Parent?: Marker;
  Children?: Marker[];
  MarkerType?: MarkerType;
  Event?: Event;
  Challenger?: Challenger;
}

export interface MarkerTypeTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  event?: Marker;
  language?: Language;
}

export interface MarkerType {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @maxLength 255 */
  ico?: string | null;
  /** @maxLength 255 */
  icoDark?: string | null;
  /** @maxLength 255 */
  icoSettings?: string | null;
  /** @maxLength 255 */
  shortcut?: string | null;
  /** @format int32 */
  defaultTimeStart?: number | null;
  /** @format int32 */
  defaultTimeEnd?: number | null;
  /** @format int32 */
  optaOffsetStart?: number | null;
  /** @format int32 */
  optaOffsetEnd?: number | null;
  /** @format int32 */
  startGameMinute?: number | null;
  /** @format int32 */
  endGameMinute?: number | null;
  isMain?: boolean | null;
  /** @format int32 */
  displayOrder?: number | null;
  pushNotification?: boolean | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: MarkerTypeTranslation[];
  ChildrenMarkerTypes?: MarkerType[];
  Sport?: Sport;
  Markers?: Marker[];
}

export interface RelatedMarkerTypes {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface PaymentOffersEvents {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface PaymentOffersTags {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface PaymentOffer {
  entityType: "PaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  productId: string;
  /** @format uuid */
  paymentOfferId: string;
  /** @default "active" */
  status: "active" | "archived";
  provider: "stripe" | "google" | "apple";
  providerPaymentOfferId: string;
  name: string;
  data?: {
    isLimitedDuration?: boolean;
    population?: string;
    weight?: number;
    currency: string;
    interval?: "day" | "week" | "month" | "year";
    type: "one_time" | "recurring";
    price: number;
    intervalCount?: number;
  };
  _type: "PaymentOffer";
  /** @format date-time */
  created?: string;
  /** @format date-time */
  updated?: string;
  duration?: number;
  /** @format date-time */
  expiryDate?: string;
  associatedProfiles?: ProfilePaymentOffer[];
  isPPV: boolean;
}

export interface PlayerRoleTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  playerRole?: PlayerRole;
  language?: Language;
}

export interface PlayerRole {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: PlayerRoleTranslation[];
  TeammateSquads?: TeammateSquad[];
  StatsTypesPlayerRoles?: StatsTypesPlayerRoles[];
  Sport?: Sport;
}

export interface Playlist {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  description?: string | null;
  /** @maxLength 255 */
  thumbnail?: string | null;
  /** @maxLength 255 */
  portraitThumbnail?: string | null;
  /** @maxLength 255 */
  heroImageDesktop?: string | null;
  /** @maxLength 255 */
  heroImageMobile?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  /** @format int32 */
  currentVideoPlaylistRank?: number | null;
  newEpisodes?: boolean | null;
  hasBeenViewed?: boolean | null;
  /** @format int32 */
  numberOfVideos?: number | null;
  Category?: Category;
  Videos?: Video[];
}

export interface RoundTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  stage?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  round?: Round;
  language?: Language;
}

export interface Round {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format int32 */
  competitionOrder: number;
  /** @maxLength 255 */
  stage?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: RoundTranslation[];
  Events?: Event[];
  Season?: Season;
  Competition?: Competition;
  Tags?: Tag[];
  ItemTags?: ItemTag[];
}

export interface SeasonTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  season?: Season;
  language?: Language;
}

export interface Season {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  startDate?: string | null;
  /** @format date-time */
  endDate?: string | null;
  isCurrent?: boolean | null;
  /** @maxLength 255 */
  providerId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: SeasonTranslation[];
  Rounds?: Round[];
  ChallengerCompetitions?: ChallengerCompetitions[];
  Sport?: Sport;
  Tags?: Tag[];
  ItemTags?: ItemTag[];
}

export interface ShortUrl {
  /** @maxLength 255 */
  shortId?: string | null;
  /** @maxLength 255 */
  parentDomain?: string | null;
  /** @format int32 */
  consumedCounter?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Marker?: Marker;
  Chapter?: Chapter;
}

export interface Simulcast {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  status: "1" | "0";
  /** @maxLength 255 */
  sourceIp?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  streamIds?: string[] | null;
  state: "legacy" | "aws";
  /** @format uuid */
  EventId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Targets?: Target[];
  Streams?: Stream[];
}

export interface Target {
  /** @format uuid */
  id?: string | null;
  platform:
    | "facebook-live"
    | "youtube-live"
    | "dailymotion-live"
    | "periscope-live"
    | "onrewind-live"
    | "rtmp"
    | "rtmps";
  status: "2" | "1" | "0";
  /** @maxLength 255 */
  streamUrl: string;
  /** @maxLength 255 */
  streamKey: string;
  /** @format date-time */
  lastAiringTime?: string | null;
  /** @format int64 */
  airingTimeCounter?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Simulcast?: Simulcast;
  Job?: Job;
}

export interface Speaker {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  lastName: string;
  /** @maxLength 255 */
  firstName: string;
  politicalParty?: string | null;
  job?: string | null;
  biography?: string | null;
  /** @maxLength 255 */
  picture?: string | null;
  /** @maxLength 255 */
  link?: string | null;
  /** @maxLength 255 */
  twitter?: string | null;
  /** @maxLength 255 */
  facebook?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Chapters?: Chapter[];
}

export interface Sport {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  timelineType?: "single" | "double" | null;
  periods?: "multiple" | "none" | null;
  /** @maxLength 255 */
  svgSpriteFilename?: string | null;
  /** @maxLength 255 */
  sportsFieldFilename?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Events?: Event[];
  Challengers?: Challenger[];
  MarkerTypes?: MarkerType[];
  Seasons?: Season[];
  Competitions?: Competition[];
}

export interface TeammateSquad {
  /** @format uuid */
  id?: string | null;
  meta?: any;
  /** @maxLength 255 */
  picture?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Team?: Challenger;
  Season?: Season;
  Teammate?: Challenger;
  Role?: PlayerRole;
}

export interface StatsTypeTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  statsType?: StatsType;
  language?: Language;
}

export interface StatsType {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  providerId: string;
  /** @format int32 */
  displayOrder?: number | null;
  /** @maxLength 255 */
  type: string;
  aggregation?: "sum" | "mean" | null;
  /** @format uuid */
  AccountId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  translations?: StatsTypeTranslation[];
  StatsTypesPlayerRoles?: StatsTypesPlayerRoles[];
}

export interface StatsTypesPlayerRoles {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  StatsType?: StatsType;
  PlayerRole?: PlayerRole;
}

export type Stream = MainApiStream | AWSStream;

export interface SubCategoryCategory {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface SubCategory {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Categories?: Category[];
  Videos?: Video[];
  Events?: Event[];
  VideoCategorySubCategories?: VideoCategorySubCategory[];
  EventCategorySubCategories?: EventCategorySubCategory[];
}

export interface ItemTag {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  taggable: string;
  /** @format uuid */
  taggableId: string;
  Event?: Event;
  Tag?: Tag;
  Video?: Video;
  Season?: Season;
  Competition?: Competition;
  Round?: Round;
  Challenger?: Challenger;
}

export interface Tag {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  tagType?: string | null;
  /** @maxLength 255 */
  name: string;
  options?: any;
  /** @format uuid */
  AccountId: string;
  placeholders?: string[] | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Events?: Event[];
  Clips?: Clip[];
  Videos?: Video[];
  Seasons?: Season[];
  Competitions?: Competition[];
  Rounds?: Round[];
  Challengers?: Challenger[];
  ItemTags?: ItemTag[];
  PaymentOffers?: PaymentOffer[];
}

export interface Theme {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  style: any;
  visibility?: "public" | "private" | null;
  customMarkerTypes?: any;
  /** @maxLength 255 */
  customMarkerTypesSvgSprite?: string | null;
  /** @format uuid */
  AccountId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Events?: Event[];
  BusinessPlayers?: BusinessPlayer[];
}

export interface VideoCategorySubCategory {
  /** @format uuid */
  id?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Category?: Category;
  SubCategory?: SubCategory;
  Video?: Video;
}

export interface VideoCategory {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Category?: Category;
  Video?: Video;
}

export interface VideoEvent {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Video?: Video;
  Event?: Event;
}

export interface VideoTranslation {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  language?: Language;
  video?: Video;
}

export interface Video {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  filename?: string | null;
  description?: string | null;
  /** @format float */
  duration?: number | null;
  /** @maxLength 255 */
  poster?: string | null;
  /** @maxLength 255 */
  portraitThumbnail?: string | null;
  /** @maxLength 255 */
  url?: string | null;
  visibility?: "public" | "private" | null;
  captions?: any[] | null;
  status?: "none" | "original" | "in_progress" | "encoded" | "archived" | "vendor" | null;
  archiveData?: any;
  vendorName?: "jwplayer" | "awsplayer" | "dailymotion" | "youtube" | "twitch" | null;
  /** @maxLength 255 */
  vendorVideoId?: string | null;
  /** @maxLength 255 */
  vendorApiKey?: string | null;
  meta?: any;
  /** @format uuid */
  AccountId?: string | null;
  /** @format date-time */
  publicationDate?: string | null;
  fullDescription?: string | null;
  technicalDescription?: string | null;
  /** @format int32 */
  playlistRank?: number | null;
  liveSource: boolean;
  urlMp4?: string | null;
  hasBeenViewed?: boolean | null;
  isNew?: boolean | null;
  marker?: number | null;
  /** @format int32 */
  currentFanViews?: number | null;
  /** @format int32 */
  views?: number | null;
  type?: "360" | "additionnal" | null;
  geoBlockingMapping?: any;
  ratio: "sixteen-nine" | "nine-sixteen";
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Event?: Event;
  BusinessPlayer?: BusinessPlayer;
  Playlist?: Playlist;
  Jobs?: Job[];
  translations?: VideoTranslation[];
  Tags?: Tag[];
  ItemTags?: ItemTag[];
  VideoEvents?: VideoEvent[];
  Categories?: Category[];
  SubCategories?: SubCategory[];
  VideoCategorySubCategories?: VideoCategorySubCategory[];
  VideoCategories?: VideoCategory[];
  ItemProducts?: Product[];
}

export interface Product {
  /** @default "Product" */
  _type: "Product";
  /** @format uuid */
  accountId: string;
  data?: {
    description?: string;
    weight?: number;
  };
  /** @default "Product" */
  entityType: "Product";
  /** @format uuid */
  id: string;
  name?: string;
  /** @format uuid */
  productId: string;
  isStandard?: boolean;
  /** @default "draft" */
  status: "draft" | "published" | "archived";
  /** @format date-time */
  created?: string;
  /** @format date-time */
  updated?: string;
  paymentOffers: PaymentOffer[];
}

export interface ProfileProduct {
  entityType: "ProfileProduct";
  _type?: "ProfileProduct";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  productId: string;
  data?: object;
}

export interface ProfilePaymentOffer {
  entityType: "ProfilePaymentOffer";
  _type?: "ProfilePaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  paymentOfferId: string;
  data?: object;
}

export interface GeoBlockItem {
  status: number;
  message?: string;
  code?: string;
}

export interface MainApiStream {
  /** @format uuid */
  id?: string | null;
  streamType?: "main" | "backup" | "additionnal" | null;
  /** @maxLength 255 */
  key: string;
  /** @maxLength 255 */
  token: string;
  /** @maxLength 255 */
  recordName?: string | null;
  isRecordReady: boolean;
  /** @format date-time */
  startedAt?: string | null;
  /** @format int32 */
  offset?: number | null;
  /** @maxLength 255 */
  name?: string | null;
  mapCoordinates?: any;
  /** @maxLength 255 */
  url?: string | null;
  options?: any;
  /** @maxLength 255 */
  streamable?: string | null;
  /** @format uuid */
  streamableId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  event?: Event;
  businessPlayer?: BusinessPlayer;
  simulcast?: Simulcast;
}

export interface AWSStream {
  /** @format uuid */
  id?: string;
  url?: string | null;
  streamType?: string | null;
  awsStream: "STANDBY" | "ONAIR" | "ARCHIVED";
}

import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, HeadersDefaults, ResponseType } from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, "body" | "method" | "query" | "path">;

export interface ApiConfig<SecurityDataType = unknown> extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({ securityWorker, secure, format, ...axiosConfig }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: axiosConfig.baseURL || "" });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(params1: AxiosRequestConfig, params2?: AxiosRequestConfig): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method && this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === "object") {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== "string") {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Main-API
 * @version 1.0.0
 *
 * This is the Main-API OpenAPI 3.0 specification.
 *
 * Some useful links:
 *   - [The source API definition](/docs/swagger.json)
 */
export class MainAPI<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  platforms = {
    /**
     * No description
     *
     * @tags Videos
     * @name GetVideos
     * @request GET:/platforms/videos
     * @secure
     */
    getVideos: (params: RequestParams = {}) =>
      this.request<Video[], any>({
        path: `/platforms/videos`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Videos
     * @name GetVideoById
     * @request GET:/platforms/videos/{id}
     * @secure
     */
    getVideoById: (id: string, params: RequestParams = {}) =>
      this.request<Video, any>({
        path: `/platforms/videos/${id}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Videos
     * @name GetVideoAccess
     * @request GET:/platforms/videos/{id}/access
     * @secure
     */
    getVideoAccess: (id: string, params: RequestParams = {}) =>
      this.request<Video, any>({
        path: `/platforms/videos/${id}/access`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Events
     * @name GetEvents
     * @request GET:/platforms/events
     * @secure
     */
    getEvents: (params: RequestParams = {}) =>
      this.request<Event[], any>({
        path: `/platforms/events`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Events
     * @name GetEventById
     * @request GET:/platforms/events/{id}
     * @secure
     */
    getEventById: (id: string, params: RequestParams = {}) =>
      this.request<Event, any>({
        path: `/platforms/events/${id}`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags Events
     * @name GetEventAccess
     * @request GET:/platforms/events/{id}/access
     * @secure
     */
    getEventAccess: (id: string, params: RequestParams = {}) =>
      this.request<Event | GeoBlockItem, any>({
        path: `/platforms/events/${id}/access`,
        method: "GET",
        secure: true,
        format: "json",
        ...params,
      }),
  };
}
