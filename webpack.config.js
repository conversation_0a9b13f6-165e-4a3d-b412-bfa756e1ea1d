const { withRNVWebpack } = require("@rnv/adapter");

module.exports = withRNVWebpack({
	output: {
		//This allows to build and output a single bundle.js file
		//https://github.com/flexn-io/renative/issues/1353
		// chunkFormat: false,
	},
	ignoreWarnings: [
		// Ignore source map warnings for packages that don't include source files
		{
			module: /node_modules\/build-url-ts/,
			message: /Failed to parse source map/,
		},
	],
});
