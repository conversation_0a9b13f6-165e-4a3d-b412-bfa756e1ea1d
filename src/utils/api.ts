// Swagger generate commands from json files at: src/utils/apis/
// npx swagger-typescript-api -p src/utils/apis/kentico.json -o src/utils/apis/generated -n kentico.ts --axios --api-class-name KenticoAPI
// npx swagger-typescript-api -p src/utils/apis/main_api.json -o src/utils/apis/generated -n main_api.ts --axios --api-class-name MainAPI
// npx swagger-typescript-api -p src/utils/apis/payments_api.json -o src/utils/apis/generated -n payments_api.ts --axios --api-class-name PaymentsAPI
// npx swagger-typescript-api -p src/utils/apis/user_api.json -o src/utils/apis/generated -n user_api.ts --axios --api-class-name UserAPI
// npx swagger-typescript-api -p src/utils/apis/video_api.json -o src/utils/apis/generated -n video_api.ts --axios --api-class-name VideoAPI

// Example of handmade calls -> redundant! use generated files from swagger
import axios from "axios";

const API_BASE_URL = "https://cms-service.onrewind.tv/ott";

const api = axios.create({
	baseURL: API_BASE_URL,
	headers: {
		Accept: "application/json",
		"Accept-Charset": "UTF-8",
		"x-account-key": "ByAWCu-i5",
	},
});

// Fetch home page data
export const fetchHomePageData = async () => {
	try {
		const response = await api.get("/kentico/pages/home?language=en");
		return response.data;
	} catch (error) {
		console.error("Error fetching home page data:", error);
		throw error;
	}
};

// Fetch live events
export const fetchLiveEvents = async () => {
	try {
		const response = await api.get(
			"/v2/kentico/events?status=live&language=en"
		);
		return response.data;
	} catch (error) {
		console.error("Error fetching live events:", error);
		throw error;
	}
};

// Fetch future events
export const fetchFutureEvents = async () => {
	try {
		const response = await api.get(
			"/v2/kentico/events?status=future&language=en"
		);
		return response.data;
	} catch (error) {
		console.error("Error fetching future events:", error);
		throw error;
	}
};
