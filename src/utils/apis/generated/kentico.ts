/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export type Error = string;

export interface Exception {
	status?: number;
	type?: string;
	message?: string;
}

export interface MessageOK {
	message?: "OK" | "KO";
}

export interface StatusOK {
	/** @default "ok" */
	status?: "ok" | "ko";
}

export interface Health {
	status?: "OK" | "KO";
}

export interface KenticoBaseType {
	_kenticoCodename: string;
	_kenticoId: string;
	_kenticoItemType: string;
	_kenticoLanguage: string;
}

export interface KenticoBaseTypePartial {
	_kenticoCodename?: string;
	_kenticoId?: string;
	_kenticoItemType?: string;
	_kenticoLanguage?: string;
}

export interface KenticoAssetType {
	name: string;
	description: string | null;
	type?: string;
	size?: number;
	url: string;
	width?: string;
	height?: string;
}

export interface KenticoImageType {
	image?: KenticoAssetType;
	mobileImage?: KenticoAssetType | null;
	buttonRedirectionUrl: string | null;
	mobileRedirectionTarget: string | null;
	buttonTitle: string | null;
	buttonRedirectionTargetType: "internal" | "external";
}

export interface ContentImageType {
	_kenticoCodename: string;
	_kenticoId: string;
	_kenticoItemType: string;
	_kenticoLanguage: string;
	image: KenticoImageType;
	title: string | null;
	description: string | null;
}

export interface KenticoPageItem {
	id: string;
	codename: string;
	itemType: string;
	_kenticoPublicationDate?: string;
}

export interface PageIndex {
	pages: KenticoPageItem[];
	cursor?: Cursor | null;
}

export interface Cursor {
	before: string | null;
	after: string | null;
}

/** @example {"_kenticoCodename":"string","_kenticoId":"string","_kenticoItemType":"string","_kenticoLanguage":"string","redirectionTarget":"string","redirectionTargetType":"external","name":"string","subItems":[],"mobile":{"name":"string","redirectionTarget":"string","redirectionTargetType":"external","subItems":[]}} */
export interface MenuItem {
	_kenticoCodename: string;
	_kenticoId: string;
	_kenticoItemType: string;
	_kenticoLanguage: string;
	redirectionTarget: string;
	redirectionTargetType?: "external" | "internal" | "page";
	name: string;
	subItems: MenuItem[];
	mobile: MobileMenuItem;
}

export interface MobileMenuItem {
	redirectionTarget: string;
	redirectionTargetType?: "external" | "internal" | "page";
	name: string;
	subItems: MenuItem[];
}

export type AboutLink = RedirectionTarget & {
	name: string;
	subItems: MenuItem[];
};

export interface Header {
	menuItems: MenuItem[];
	logo?: KenticoAssetType;
	favicon?: KenticoAssetType;
	fixed: boolean;
	aboutLink: AboutLink | null;
	liveEvents?: LiveEvents[];
	headerColor:
		| "standardPrimary"
		| "alwaysTransparentBlurred"
		| "standard-black";
	logoPosition: "left" | "centered";
}

export interface LiveEvents {
	itemId: string;
	urlSlug: string | null;
}

export interface SEO {
	title: string;
	description: string;
	canonicalTag?: string;
	robots?: string[];
}

export type SocialItem = KenticoBaseType & {
	socialName: string;
	linkUrl: string;
	socialIcon?: KenticoAssetType;
};

export interface Footer {
	menuItems: MenuItem[];
	logo?: KenticoAssetType;
	socialItems: SocialItem[];
	copyright: string;
	legalLinks: MenuItem[];
	footerLayout: "standard" | "columns";
	footerBackgroundColor: "black" | "white" | "primary" | "secondary";
}

export interface InitConfig {
	header: Header;
	footer: Footer;
	partnersBottomSection: {
		partnersSections: IterablePartnerStaticSection[];
	};
	SEO: SEO;
	theatersLogos?: KenticoAssetType[];
	landscapePlaceholder?: KenticoAssetType;
	portraitPlaceholder?: KenticoAssetType;
	sectionTitlesColor: "black/white" | "primary";
	theme: "dark" | "light" | "both";
	adsSettings: AdvertisementStaticSection | GoogleAdsStaticSection;
}

export interface UpdateConfig {
	androidCurrentVersion?: string;
	androidMinVersion?: string;
	androidUpdateUrl?: string;
	iosCurrentVersion?: string;
	iosMinVersion?: string;
	iosUpdateUrl?: string;
}

export interface ManageSocialConfig {
	androidMinVersion?: string;
	iosMinVersion?: string;
}

export interface MobileConfig {
	onboarding_title: string;
	onboarding_text: string;
	registrationCompletedText: string;
	app_logo?: KenticoAssetType;
	updateConfig?: UpdateConfig;
	manage_social_login?: ManageSocialConfig;
	menuItems: MenuItem[];
	tab_bar: {
		type: string;
		is_visible: string;
	}[];
	mySubscriptionsUrl?: string | null;
	VASTAdsUrl?: string | null;
	partners: WebPartner[];
	liveEvents: LiveEvents[];
}

export type WebPartner = KenticoBaseType & {
	redirectUrl: string;
	image?: KenticoAssetType;
	title?: string;
};

export type SectionContent =
	| CarouselStaticSection
	| CustomTitleStaticSection
	| GridDynamicSection
	| GridStaticSection
	| GoogleAdsStaticSection
	| AdvertisementStaticSection
	| SlideStaticSection
	| AccordionStaticSection
	| MostViewedStaticSection
	| IterablePartnerStaticSection
	| LiveCarouselDynamicSection
	| HeroWebStaticSection
	| CarouselDynamicSection
	| CarouselWithTypeStaticSection
	| GridWithCategoryDynamicSection
	| CustomTextStaticSection
	| RecommendedWebStaticSection
	| CarouselLatestVideoDynamicSection;

export type DynamicSection =
	| GridWithCategoryDynamicSection
	| CarouselLatestVideoDynamicSection
	| CarouselDynamicSection
	| LiveCarouselDynamicSection
	| GridDynamicSection;

export type StaticSection =
	| SlideStaticSection
	| CarouselStaticSection
	| GoogleAdsStaticSection
	| AdvertisementStaticSection
	| MostViewedStaticSection
	| CustomTitleStaticSection
	| CustomTextStaticSection
	| IterablePartnerStaticSection
	| HeroWebStaticSection
	| RecommendedWebStaticSection
	| GridStaticSection
	| AccordionStaticSection
	| CarouselWithTypeStaticSection;

export type PageLayout = KenticoBaseType & {
	title: string;
	description: string | null;
	image?: KenticoAssetType | null;
	components: SectionContent[];
	/** @format date-time */
	_kenticoPublicationDate?: string;
};

export interface CarouselStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_carousel" */
	_kenticoItemType: "section_static_carousel";
	_kenticoLanguage: string;
	title: string;
	items: (VideoCard | PlaylistCard | CategoryCard | Image)[];
	itemsDisplayType: "landscape" | "portrait";
	showMoreButtonRedirection: string | null;
	breakpoints?: Breakpoints;
	mobileShowMoreButtonRedirection: string | null;
}

export interface CustomTitleStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_custom_title" */
	_kenticoItemType: "section_custom_title";
	_kenticoLanguage: string;
	text: string;
	size: "big" | "medium" | "small";
	horizontalPosition: "left" | "right" | "center";
}

export interface GridDynamicSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_dynamic_grid" */
	_kenticoItemType: "section_dynamic_grid";
	_kenticoLanguage: string;
	/** @default "section_dynamic_grid" */
	type: "section_dynamic_grid";
	params: {
		title: string;
		/** @default "section_dynamic_grid" */
		type: "section_dynamic_grid";
		itemsType: string;
		sectionFilters: CustomTag[] | null;
		buttonType?: string;
		buttonRedirect?: string;
		additionalFilters: CustomTag[] | null;
		numberOfItems: number;
	};
	items: (VideoCard | EventCard)[];
	codename: string;
	cursor?: Cursor;
}

export interface GridStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_grid" */
	_kenticoItemType: "section_static_grid";
	_kenticoLanguage: string;
	items: VideoCard[];
	title: string;
}

export interface GoogleAdsStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_google_ads" */
	_kenticoItemType: "section_static_google_ads";
	_kenticoLanguage: string;
	gptTag: string;
	adUnit: string;
}

export interface AdvertisementStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_ad" */
	_kenticoItemType: "section_static_ad";
	_kenticoLanguage: string;
	image: KenticoImageType[];
	redirectionTarget: string;
}

export interface SlideStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_slider" */
	_kenticoItemType: "section_static_slider";
	_kenticoLanguage: string;
	title: string;
	displayRatio: "normal" | "narrow" | "wide";
	items: (
		| VideoCard
		| PlaylistCard
		| EventCard
		| CategoryCard
		| Image
	)[];
}

export interface AccordionStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_static_accordion" */
	_kenticoItemType: "section_static_accordion";
	_kenticoLanguage: string;
	title: string;
	items: OriginsVideo[];
	ratio: "normal" | "narrow" | "wide";
	redirectionTarget: string;
	redirectionTargetType?: "external" | "page";
}

export interface MostViewedStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_most_viewed" */
	_kenticoItemType: "section_most_viewed";
	_kenticoLanguage: string;
	type: "videos" | "playlists";
	period: "weekly" | "monthly" | "semestral" | "yearly";
	/** @format date-time */
	updatedAt?: string;
	items: VideoCard[] | PlaylistCard[] | null;
	breakpoints?: Breakpoints;
}

export interface IterablePartnerStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "web_section___partners" */
	_kenticoItemType: "web_section___partners";
	_kenticoLanguage: string;
	title: string;
	itemsSize: "big" | "small";
	items: IterablePartner[];
}

export interface LiveCarouselDynamicSection {
	_kenticoCodename: string;
	_kenticoId: string;
	_kenticoLanguage: string;
	title: string;
	breakpoints?: Breakpoints;
	/** @default "section_dynamic_live" */
	_kenticoItemType: "section_dynamic_live";
	/** @default "section_dynamic_live" */
	type: "section_dynamic_live";
	redirectionPath: string;
	mobileRedirection: string;
	params: {
		/** @default "section_dynamic_live" */
		type: "section_dynamic_live";
	};
	filter: "all" | "club" | "competition";
	/** @format uuid */
	clubOrCompetitionId: string;
	items?: EventEntity[];
}

export interface HeroWebStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "web_section___hero" */
	_kenticoItemType: "web_section___hero";
	_kenticoLanguage: string;
	name: string;
	description: string | null;
	logo?: KenticoAssetType | null;
	image: KenticoImageType[];
	items: VideoHero[] | null;
	content: (ContentImageType | VideoHeroAutoplay)[] | null;
}

export type CarouselDynamicSection = ({
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_dynamic_carousel" */
	_kenticoItemType: "section_dynamic_carousel";
	_kenticoLanguage: string;
	codename: string;
	categoryId?: string;
	/** @default "section_dynamic_carousel" */
	type?: "section_dynamic_carousel";
	format?: "landscape" | "portrait";
	params: {
		/** @default "section_dynamic_carousel" */
		type: "section_dynamic_carousel";
	};
	isFavorite?: boolean;
	Events?: EventCard[];
	cursor: Cursor;
	SubCategories: SubCategoryEntity[];
	breakpoints?: Breakpoints;
} & CategoryEntity) &
	(
		| {
				/** @default "video" */
				itemsType: "video";
				Videos: VideoCard[];
		  }
		| {
				/** @default "playlist" */
				itemsType: "playlist";
				Playlists: PlaylistCardBase[];
		  }
	);

export interface CarouselLatestVideoDynamicSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_dynamic_carousel_latest_videos" */
	_kenticoItemType: "section_dynamic_carousel_latest_videos";
	_kenticoLanguage: string;
	title: string;
	/** @default "section_dynamic_carousel_latest_videos" */
	type: "section_dynamic_carousel_latest_videos";
	codename: string;
	params: {
		/** @default "section_dynamic_carousel_latest_videos" */
		type: "section_dynamic_carousel_latest_videos";
	};
	period:
		| "last_7_days"
		| "last_15_days"
		| "last_30_days"
		| "last_365_days";
	format: "landscape" | "portrait";
	breakpoints: Breakpoints;
	Videos: VideoCard[];
	cursor: Cursor;
}

export type CarouselWithTypeStaticSection = CarouselStaticSection & {
	displayCategoryOnThumbnails?: boolean;
	sectionType?: "carousel" | "slider" | "grid";
};

export type GridWithCategoryDynamicSection = ({
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_dynamic_grid_with_category" */
	_kenticoItemType: "section_dynamic_grid_with_category";
	_kenticoLanguage: string;
	codename: string;
	/** @default "section_dynamic_grid_with_category" */
	type?: "section_dynamic_grid_with_category";
	title: string;
	/** @format uuid */
	categoryId: string;
	withFilters: boolean;
	fetchMoreUrl?: string;
	params: {
		title: string;
		/** @default "section_dynamic_grid_with_category" */
		type: "section_dynamic_grid_with_category";
		numberOfItems: number;
		withFilters: boolean;
		itemsDisplayType: "landscape" | "portrait";
	};
	isFavorite?: boolean;
	Events?: EventCard[];
	cursor: Cursor;
	SubCategories: SubCategoryEntity[];
} & CategoryEntity) &
	(
		| {
				/** @default "video" */
				itemsType: "video";
				Videos: VideoCard[];
		  }
		| {
				/** @default "playlist" */
				itemsType: "playlist";
				Playlists: PlaylistCardBase[];
		  }
	);

export interface CustomTextStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_custom_text" */
	_kenticoItemType: "section_custom_text";
	_kenticoLanguage: string;
	text: string;
}

export interface RecommendedWebStaticSection {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "section_recommended_videos" */
	_kenticoItemType: "section_recommended_videos";
	_kenticoLanguage: string;
	title: string;
	items: VideoEntity[];
	breakpoints: Breakpoints;
}

export interface Breakpoints {
	perPage: number;
	breakpoints: {
		"430": ResponsiveOptions;
		"920": ResponsiveOptions;
		"1280": ResponsiveOptions;
		"1920": ResponsiveOptions;
	};
}

export interface ResponsiveOptions {
	perPage: number;
}

export interface RedirectionTarget {
	redirectionTarget: string;
	redirectionTargetType?: "external" | "internal" | "page";
}

export type VideoHeroAutoplay = KenticoBaseType & {
	/** @format uuid */
	videoId: string;
	title: string | null;
	subtitle: string | null;
	buttonTitle: string | null;
	urlButtonRedirect: "internal" | "external" | null;
};

export type IterablePartner = KenticoBaseType & {
	title: string;
	image?: KenticoAssetType;
	redirectUrl: string;
};

export interface Image {
	_kenticoCodename: string;
	_kenticoId: string;
	/** @default "image" */
	_kenticoItemType: "image";
	_kenticoLanguage: string;
	image: KenticoImageType;
	title: string | null;
	description: string | null;
}

export interface Video {
	/** @default "video" */
	itemType?: "video";
	/** @format uuid */
	itemId: string;
	description: string | null;
	fullDescription: string | null;
	technicalDescription?: string | null;
	duration: string | null;
	name: string;
	poster: string | null;
	posterPortrait?: string | null;
	urlSlug: string | null;
}

export interface VideoEntity {
	/** @format uuid */
	id: string;
	/** @format uuid */
	AccountId: string | null;
	description: string | null;
	duration: number | null;
	filename: string | null;
	fullDescription: string | null;
	name: string;
	archiveData?: any;
	captions?: any;
	meta?: any;
	poster: string | null;
	portraitThumbnail: string | null;
	/** @format date-time */
	publicationDate: string | null;
	status:
		| "none"
		| "original"
		| "in_progress"
		| "encoded"
		| "archived"
		| "vendor";
	technicalDescription: string | null;
	/** @format uuid */
	PlaylistId: string | null;
	playlistRank?: number;
	/** @format date-time */
	updatedAt: string;
	/** @format date-time */
	createdAt: string;
	url: string | null;
	vendorApiKey: string | null;
	vendorName:
		| "jwplayer"
		| "awsplayer"
		| "dailymotion"
		| "youtube"
		| "twitch";
	vendorVideoId: string | null;
	visibility: "public" | "private";
	isPaid?: boolean;
	marker?: number;
	isNew?: boolean;
	hasBeenViewed?: boolean;
	currentFanViews?: number;
	views?: number;
	ratio: "sixteen-nine" | "nine-sixteen" | null;
	Playlist?: {
		/** @format uuid */
		id: string;
		name: string;
		/** @format uuid */
		accountId: string;
		/** @format uuid */
		CategoryId: string;
		description: string | null;
		thumbnail: string | null;
		portraitThumbnail: string | null;
		heroImageDesktop: string | null;
		heroImageMobile: string | null;
		currentVideoPlaylistRank?: number;
		newEpisodes?: number;
		hasBeenViewed?: boolean;
		numberOfVideos?: number;
		isFavorite?: boolean;
		/** @format date-time */
		createdAt: string;
		/** @format date-time */
		updatedAt: string;
	};
	ItemProducts?: PaymentProduct[];
	PaymentOffers?: PaymentOfferEntity[];
	Tags?: TagEntity[];
	Categories?: CategoryEntity[];
	VideoCategorySubCategories?: VideoCategorySubCategoryEntity[];
}

export interface PagedVideoEntity {
	items: VideoEntity[];
	cursor: Cursor;
}

export type PagedVideoEntityAlt =
	| VideoEntity[]
	| {
			items: VideoEntity[];
			cursor: Cursor;
	  };

export interface PagedVideoCard {
	items: VideoCard[];
	cursor?: Cursor;
}

export type PagedVideoCardAlt =
	| VideoCard[]
	| {
			items: VideoCard[];
			cursor?: Cursor;
	  };

export type OriginsVideo = KenticoBaseTypePartial & {
	/** @default "video" */
	itemType: "video";
	video: {
		description: string | null;
		duration: string | null;
		fullDescription: string | null;
		/** @format uuid */
		itemId: string | null;
		name: string;
		poster: string | null;
		posterPortrait?: string | null;
		/** @format date-time */
		publicationDate: string | null;
		shareUrl: string | null;
		technicalDescription: string | null;
		urlSlug: string | null;
		marker?: number;
		isNew?: boolean;
		hasBeenViewed?: boolean;
		views?: number;
		currentFanViews?: number;
		searchTags: TagEntity[];
		tags: TagEntity[];
		ratio: "sixteen-nine" | "nine-sixteen" | null;
		isFavorite?: boolean;
		Categories?: CategoryEntity[];
		Playlist?: {
			/** @format uuid */
			id: string;
			name: string;
			/** @format uuid */
			accountId: string;
			/** @format uuid */
			CategoryId: string;
			description: string | null;
			thumbnail: string | null;
			portraitThumbnail: string | null;
			heroImageDesktop: string | null;
			heroImageMobile: string | null;
			currentVideoPlaylistRank?: number;
			newEpisodes?: number;
			hasBeenViewed?: boolean;
			numberOfVideos?: number;
			isFavorite?: boolean;
			/** @format date-time */
			createdAt: string;
			/** @format date-time */
			updatedAt: string;
		};
		ItemProducts?: PaymentProduct[] | string[];
	};
};

export interface VideoCard {
	_kenticoCodename?: string;
	_kenticoId?: string;
	_kenticoItemType?: string;
	_kenticoLanguage?: string;
	/** @default "video" */
	itemType?: "video";
	/** @format uuid */
	itemId: string;
	description: string | null;
	fullDescription: string | null;
	technicalDescription?: string | null;
	duration: string | null;
	name: string;
	poster: string | null;
	posterPortrait?: string | null;
	urlSlug: string | null;
	tags?: TagEntity[];
	products?: string[] | null;
	categories?: CategoryEntity[];
	subCategories?: VideoCategorySubCategoryEntity[];
	marker?: number;
	isNew?: boolean;
	hasBeenViewed?: boolean;
	currentFanViews?: number;
	views?: number;
	/** @default "sixteen-nine" */
	ratio?: "sixteen-nine" | "nine-sixteen";
	isFavorite?: boolean;
}

export interface VideoHero {
	_kenticoCodename?: string;
	_kenticoId?: string;
	_kenticoItemType?: string;
	_kenticoLanguage?: string;
	/** @default "video" */
	itemType?: "video";
	/** @format uuid */
	itemId: string;
	description: string | null;
	duration: string | null;
	name: string;
	poster: string | null;
	posterPortrait?: string | null;
	urlSlug: string | null;
	tags?: TagEntity[];
	categories?: CategoryEntity[];
	subCategories?: VideoCategorySubCategoryEntity[];
	/** @default "sixteen-nine" */
	ratio?: "sixteen-nine" | "nine-sixteen";
	isFavorite?: boolean;
}

export type OriginsTag = KenticoBaseType & {
	id: string;
	tagType: string;
	name: string;
};

export interface OriginsVideoIndexItem {
	/** @default "video" */
	itemType: "video";
	itemId: string | null;
	urlSlug: string | null;
	/** @format date-time */
	publicationDate: string | null;
}

export type OriginsVideoWithRelatedVideos = {
	/** @default "video" */
	itemType: "video";
	video: {
		description: string | null;
		duration: string | null;
		fullDescription: string | null;
		/** @format uuid */
		itemId: string | null;
		name: string;
		poster: string | null;
		posterPortrait?: string | null;
		/** @format date-time */
		publicationDate: string | null;
		shareUrl: string | null;
		technicalDescription: string | null;
		urlSlug: string | null;
		marker?: number;
		isNew?: boolean;
		hasBeenViewed?: boolean;
		views?: number;
		currentFanViews?: number;
		searchTags: TagEntity[];
		tags: TagEntity[];
		ratio: "sixteen-nine" | "nine-sixteen" | null;
		isFavorite?: boolean;
		Categories?: CategoryEntity[];
		Playlist?: {
			/** @format uuid */
			id: string;
			name: string;
			/** @format uuid */
			accountId: string;
			/** @format uuid */
			CategoryId: string;
			description: string | null;
			thumbnail: string | null;
			portraitThumbnail: string | null;
			heroImageDesktop: string | null;
			heroImageMobile: string | null;
			currentVideoPlaylistRank?: number;
			newEpisodes?: number;
			hasBeenViewed?: boolean;
			numberOfVideos?: number;
			isFavorite?: boolean;
			/** @format date-time */
			createdAt: string;
			/** @format date-time */
			updatedAt: string;
		};
		ItemProducts?: PaymentProduct[] | string[];
	};
} & {
	relatedVideos: PagedVideoCard;
	playlistRelatedVideos?: VideoCard[];
};

export interface AWSStream {
	url: string | null;
	streamType: string;
	awsStream: "STANDBY" | "ONAIR" | "ARCHIVED" | null;
}

export interface StreamEntity {
	/** @format uuid */
	id: string;
	isRecordReady?: boolean;
	key?: string;
	mapCoordinates?: any;
	name?: string | null;
	offset?: number | null;
	options?: any;
	recordName?: string | null;
	/** @format date-time */
	startedAt?: string;
	streamable?: string | null;
	streamableId?: string | null;
	streamType?: "main" | "backup" | "additionnal" | null;
	token?: string;
	url?: string;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
}

export interface CompetitionEntity {
	/** @format uuid */
	id: string;
	name: string;
	/** @maxLength 255 */
	slug?: string | null;
	displayOrder?: number | null;
	/** @format date-time */
	startDate?: string | null;
	/** @format date-time */
	endDate?: string | null;
	/** @maxLength 255 */
	providerId?: string | null;
	/** @format uuid */
	SportId?: string;
	/** @format date-time */
	createdAt?: string;
	/** @format date-time */
	updatedAt?: string;
	Tags?: TagEntity[];
}

export type PagedCompetitionEntityArrayAlt =
	| CompetitionEntity[]
	| {
			items: CompetitionEntity[];
			cursor: Cursor;
	  };

export interface EventEntity {
	AccountId: string | null;
	activatedModules: number[] | null;
	advertisingBanner?: any;
	/** @format uuid */
	calendarEventId: string | null;
	/** @format uuid */
	ChatId: string | null;
	/** @format date-time */
	createdAt: string;
	dailymotionLiveStreamId: string | null;
	description: string | null;
	/** @format date-time */
	endDate: string | null;
	facebookPlaylistId: string | null;
	geoBlockingMapping: {
		whitelist?: string[];
		blacklist?: string[];
	} | null;
	fullDescription: string | null;
	hashtag: string | null;
	/** @format uuid */
	id: string;
	location: string | null;
	name: string;
	options?: any;
	organiserName: string | null;
	placeholder: {
		poster?: string;
		url?: string;
	} | null;
	ParentId: string | null;
	refereeName: string | null;
	score: {
		teamIn?: string | null;
		teamOut?: string | null;
		scoreIn?: string | null;
		scoreOut?: string | null;
	};
	RoundId: string | null;
	shareUrl: string | null;
	/** @format uuid */
	SportId: string;
	/** @format date-time */
	startDate: string | null;
	state:
		| "liveOn"
		| "liveOff"
		| "replay"
		| "liveDailymotion"
		| "liveYoutube"
		| "awsLive"
		| null;
	stats?: any;
	step: "starting" | "break" | "end" | null;
	streamIds: string[] | null;
	TeamClientId: string | null;
	ThemeId: string | null;
	/** @format date-time */
	updatedAt: string;
	VideoId: string | null;
	visibility: "public" | "private" | null;
	youtubeLiveStreamId: string | null;
	isPaid?: boolean;
	ItemProducts?: PaymentProduct[];
	Challengers?: {
		/** @format date-time */
		birthday: string | null;
		coachName: string | null;
		country: string | null;
		/** @format date-time */
		createdAt: string;
		firstName: string | null;
		gamesPlayed: number | null;
		gender: "standard" | "team" | "teammate" | null;
		goals: number | null;
		height: number | null;
		history: string | null;
		homeFieldName: string | null;
		/** @format uuid */
		id: string;
		isJunior: boolean | null;
		jerseyNumber: number | null;
		jerseyPicture: string | null;
		linkShop: string | null;
		linkStats: string | null;
		name: string;
		optaId: number | null;
		picture: string | null;
		pictureUrl: string | null;
		profileOptions?: any;
		providerId: string | null;
		role: string | null;
		shortName: string | null;
		shots: number | null;
		smallPictureUrl: string | null;
		SportId: string | null;
		statsId: number | null;
		targetedShots: number | null;
		/** @format uuid */
		TeamId: string | null;
		type: "F" | "M" | "MIXTE" | null;
		/** @format date-time */
		updatedAt: string;
		weight: number | null;
	}[];
	PaymentOffers?: PaymentOfferEntity[];
	Tags?: TagEntity[];
	Categories?: CategoryEntity[];
	EventCategorySubCategories?: {
		/** @format uuid */
		id: string;
		/** @format uuid */
		CategoryId: string;
		/** @format uuid */
		SubCategoryId: string;
		/** @format uuid */
		EventId: string;
		/** @format date-time */
		createdAt: string;
		/** @format date-time */
		updatedAt: string;
		SubCategory?: SubCategoryEntity;
	}[];
	Streams: StreamEntity[] | AWSStream[];
	Round: {
		/** @format uuid */
		CompetitionId: string;
		competitionOrder: number;
		/** @format date-time */
		createdAt: string;
		/** @format uuid */
		id: string;
		name: string;
		/** @format uuid */
		SeasonId: string;
		stage?: string | null;
		/** @format date-time */
		updatedAt: string;
		Competition?: CompetitionEntity;
	};
	Sport: {
		/** @format date-time */
		createdAt: string;
		description?: string | null;
		/** @format uuid */
		id: string;
		name: string;
		periods: "multiple" | "none" | null;
		sportsFieldFilename: string | null;
		svgSpriteFilename: string | null;
		timelineType: "single" | "double" | null;
		/** @format date-time */
		updatedAt: string;
	};
	Markers?: {
		/** @format uuid */
		ChallengerId: string | null;
		/** @format date-time */
		createdAt: string;
		dailymotionRepostId: string | null;
		description: string | null;
		endTime: number;
		/** @format date-time */
		endTimeTs: string | null;
		/** @format uuid */
		EventId: string | null;
		facebookVideoId: string | null;
		gameTime: string | null;
		/** @format uuid */
		id: string;
		/** @format uuid */
		MarkerTypeId: string | null;
		notifications?: any;
		options?: any;
		/** @format uuid */
		ParentId: string | null;
		shouldDisplayChallengerProfile: boolean | null;
		startTime: number;
		/** @format date-time */
		startTimeTs: string | null;
		/** @format date-time */
		updatedAt: string;
	}[];
	MarkerTypes?: {
		/** @format date-time */
		createdAt: string;
		defaultTimeEnd: number | null;
		defaultTimeStart: number | null;
		description: string | null;
		displayOrder: number | null;
		endGameMinute: number | null;
		ico: string | null;
		icoDark: string | null;
		icoSettings: string | null;
		/** @format uuid */
		id: string;
		isMain: boolean | null;
		name: string;
		optaOffsetEnd: number | null;
		optaOffsetStart: number | null;
		pushNotification: boolean | null;
		shortcut: string | null;
		/** @format uuid */
		SportId: string | null;
		startGameMinute: number | null;
		/** @format date-time */
		updatedAt: string;
	}[];
	Video?: VideoEntity;
	Theme?: {
		AccountId: string | null;
		/** @format date-time */
		createdAt: string;
		customMarkerTypes?: any;
		customMarkerTypesSvgSprite: string | null;
		/** @format uuid */
		id: string;
		name: string;
		style?: any;
		/** @format date-time */
		updatedAt: string;
	};
	EventAttachments?: {
		/** @format date-time */
		createdAt: string;
		/** @format uuid */
		EventId: string;
		ext: string | null;
		fileName: string | null;
		/** @format uuid */
		id: string;
		name: string;
		type: string | null;
		/** @format date-time */
		updatedAt: string;
		url: string;
	}[];
	translations?: {
		/** @format date-time */
		createdAt: string;
		description: string | null;
		fullDescription: string | null;
		/** @format uuid */
		id: string;
		languageId: string | null;
		location: string | null;
		modelId: string | null;
		name: string;
		refereeName: string | null;
		/** @format date-time */
		updatedAt: string;
	}[];
}

export interface EventCard {
	_kenticoCodename?: string;
	_kenticoId?: string;
	_kenticoItemType?: string;
	_kenticoLanguage?: string;
	/** @format uuid */
	itemId: string;
	description: string | null;
	name: string;
	poster: string | null;
	posterPortrait?: string | null;
	urlSlug: string | null;
	tags?: TagEntity[];
	products?: string[] | null;
	categories?: CategoryEntity[];
	subCategories?: VideoCategorySubCategoryEntity[];
	isFavorite?: boolean;
	/** @default "event" */
	itemType?: "event";
	/** @format date-time */
	startDate: string | null;
	/** @format date-time */
	endDate: string | null;
}

export interface PagedEventEntity {
	items: EventEntity[];
	cursor: Cursor;
}

export type PagedEventEntityAlt =
	| EventEntity[]
	| {
			items: EventEntity[];
			cursor: Cursor;
	  };

export interface PagedEventCard {
	items: EventCard[];
	cursor: Cursor;
}

export type PagedEventCardAlt =
	| EventCard[]
	| {
			items: EventCard[];
			cursor: Cursor;
	  };

export interface OriginsEventIndexItem {
	/** @default "event" */
	itemType?: "event";
	/** @format uuid */
	itemId: string;
	urlSlug: string | null;
	/** @format date-time */
	publicationDate: string | null;
}

export interface OriginsEvent {
	_kenticoCodename?: string;
	_kenticoId?: string;
	_kenticoItemType?: string;
	_kenticoLanguage?: string;
	/** @default "event" */
	itemType?: "event";
	event: {
		description: string | null;
		itemId: string;
		name: string;
		onrewindState: string | null;
		poster: string | null;
		shareUrl: string | null;
		urlSlug: string | null;
		/** @format date-time */
		publicationDate: string | null;
		startDate: string | null;
		endDate: string | null;
		fullDescription: string | null;
		searchTags: TagEntity[];
		tags: TagEntity[];
		Categories?: CategoryEntity[];
		Video?: VideoEntity;
		ItemProducts?: PaymentProduct[] | string[];
		competition?: CompetitionEntity | null;
		Streams?: StreamEntity[] | AWSStream[];
	};
}

export type OriginsEventArray = OriginsEvent[];

export interface OriginsEventWithoutKenticoKeys {
	/** @default "event" */
	itemType?: "event";
	event: {
		description: string | null;
		itemId: string;
		name: string;
		onrewindState: string | null;
		poster: string | null;
		shareUrl: string | null;
		urlSlug: string | null;
		/** @format date-time */
		publicationDate: string | null;
		startDate: string | null;
		endDate: string | null;
		fullDescription: string | null;
		searchTags: TagEntity[];
		tags: TagEntity[];
		Categories?: CategoryEntity[];
		Video?: VideoEntity;
		ItemProducts?: PaymentProduct[] | string[];
		competition?: CompetitionEntity | null;
		Streams?: StreamEntity[] | AWSStream[];
	};
}

export interface TagEntity {
	/** @format uuid */
	id: string;
	tagType: string | null;
	name: string;
	options?: any;
	/** @format uuid */
	AccountId: string;
	placeholders: string | null;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	ItemTag?: ItemTagEntity;
	PaymentOffers?: PaymentOfferEntity[];
}

export interface ItemTagEntity {
	/** @format uuid */
	id: string;
	/** @format uuid */
	tagId: string;
	taggable: string;
	/** @format uuid */
	taggableId: string;
}

export interface CustomTag {
	type: string;
	name: string;
	displayValue: string;
}

export interface CategoryEntity {
	/** @format uuid */
	id: string;
	name: string;
	/** @format uuid */
	accountId: string;
	description: string | null;
	thumbnail: string | null;
	portraitThumbnail: string | null;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	isFavorite?: boolean;
	heroPortrait?: string;
	heroLandscape?: string;
}

export type CategoryEntityArray = CategoryEntity[];

export interface VideoCategorySubCategoryEntity {
	/** @format uuid */
	id: string;
	/** @format uuid */
	CategoryId: string;
	/** @format uuid */
	SubCategoryId: string;
	/** @format uuid */
	VideoId: string;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	SubCategory?: SubCategoryEntity;
}

export interface CategoryCard {
	/** @format uuid */
	id: string;
	name: string;
	/** @format uuid */
	accountId: string;
	description: string | null;
	thumbnail: string | null;
	portraitThumbnail: string | null;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	isFavorite?: boolean;
	heroPortrait?: string;
	heroLandscape?: string;
	/** @default "category" */
	itemType?: "category";
	SubCategories?: SubCategoryEntity[];
}

export interface SubCategoryEntity {
	/** @format uuid */
	id: string;
	name: string;
	/** @format uuid */
	accountId: string;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
}

export type SubCategoryEntityArray = SubCategoryEntity[];

export type SubCategoryCard = SubCategoryEntity & {
	Videos?: PagedVideoCard;
	Events?: PagedEventCard;
	Categories: CategoryEntity[];
};

export type CategoryCardWithRelationships = CategoryCard & {
	Videos?: PagedVideoCard;
	Playlist?: PagedPlaylistCard;
	Events?: PagedEventCard;
	SubCategories?: SubCategoryEntity[];
};

export interface PlaylistEntity {
	/** @format uuid */
	id: string;
	name: string;
	/** @format uuid */
	accountId: string;
	/** @format uuid */
	CategoryId: string;
	description: string | null;
	thumbnail: string | null;
	portraitThumbnail: string | null;
	heroImageDesktop: string | null;
	heroImageMobile: string | null;
	currentVideoPlaylistRank?: number;
	newEpisodes?: number;
	hasBeenViewed?: boolean;
	numberOfVideos?: number;
	isFavorite?: boolean;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	Videos?:
		| VideoEntity[]
		| {
				items: VideoEntity[];
				cursor: Cursor;
		  };
}

export interface PlaylistCardBase {
	name: string;
	/** @format uuid */
	accountId: string;
	/** @format uuid */
	CategoryId: string;
	description: string | null;
	thumbnail: string | null;
	portraitThumbnail: string | null;
	heroImageDesktop: string | null;
	heroImageMobile: string | null;
	currentVideoPlaylistRank?: number;
	newEpisodes?: number;
	hasBeenViewed?: boolean;
	numberOfVideos?: number;
	isFavorite?: boolean;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	/** @default "playlist" */
	itemType?: "playlist";
	/** @format uuid */
	itemId: string;
	/** @format uuid */
	id?: string;
}

export type PlaylistCard = PlaylistCardBase & {
	Videos?: PagedVideoCardAlt;
};

export interface PlaylistWithVideoCard {
	/** @format uuid */
	id: string;
	name: string;
	/** @format uuid */
	accountId: string;
	/** @format uuid */
	CategoryId: string;
	description: string | null;
	thumbnail: string | null;
	portraitThumbnail: string | null;
	heroImageDesktop: string | null;
	heroImageMobile: string | null;
	currentVideoPlaylistRank?: number;
	newEpisodes?: number;
	hasBeenViewed?: boolean;
	numberOfVideos?: number;
	isFavorite?: boolean;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	Videos?: PagedVideoCardAlt;
}

export interface PagedPlaylistEntity {
	items: PlaylistEntity[];
	cursor: Cursor;
}

export type PagedPlaylistEntityAlt =
	| PlaylistEntity[]
	| {
			items: PlaylistEntity[];
			cursor: Cursor;
	  };

export interface PagedPlaylistCard {
	items: PlaylistCard[];
	cursor: Cursor;
}

export type PagedPlaylistCardAlt =
	| PlaylistCard[]
	| {
			items: PlaylistCard[];
			cursor: Cursor;
	  };

export interface PaymentProduct {
	/** @default "Product" */
	entityType?: "Product";
	/** @format uuid */
	accountId: string;
	/** @format uuid */
	id: string;
	/** @format uuid */
	productId: string;
	/** @default "draft" */
	status: "draft" | "published" | "archived";
	name: string;
	data?: {
		description?: string;
		weight?: string;
	};
	isStandard?: boolean;
	/** @default "Product" */
	_type?: "Product";
	/** @format date-time */
	created: string;
	/** @format date-time */
	updated: string;
	paymentOffers?: PaymentOffer[];
	profiles?: UserProfile[];
}

export interface PaymentOffer {
	/** @default "PaymentOffer" */
	entityType?: "PaymentOffer";
	/** @format uuid */
	accountId: string;
	/** @format uuid */
	productId: string;
	/** @format uuid */
	paymentOfferId: string;
	/** @default "active" */
	status?: "active" | "archived";
	provider: "stripe" | "google" | "apple";
	providerPaymentOfferId: string;
	name: string;
	data?: {
		isLimitedDuration?: boolean;
		population?: string;
		weigth?: string;
		type?: "recurring" | "one_time";
		price?: number;
		currency?: string;
		interval?: "month" | "year" | "week" | "day";
		intervalCount?: number;
	};
	isPPV?: boolean;
	/** @default "PaymentOffer" */
	_type?: "PaymentOffer";
	duration?: number;
	/** @format date-time */
	created: string;
	/** @format date-time */
	updated: string;
	associatedProfiles?: UserProfile[];
}

export interface PaymentOfferEntity {
	/** @format uuid */
	id: string;
	paymentOfferType: "sku" | "plan";
	name: string;
	stripeId: string | null;
	appleId: string | null;
	options?: any;
	/** @format uuid */
	AccountId: string;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
	weight?: number;
}

export interface UserProfile {
	/** @format uuid */
	id: string;
	name: string;
	description?: string | null;
	type:
		| "payment"
		| "marketing"
		| "men-favorite-club"
		| "women-favorite-club"
		| "club-subscriber"
		| null;
	category?:
		| "communication"
		| "competition"
		| "favorite-men-club"
		| "favorite-women-club"
		| "premium"
		| null;
	providerId?: string | null;
	/** @format uuid */
	AccountId: string;
	retainOnLogin: boolean;
	/** @format date-time */
	createdAt: string;
	/** @format date-time */
	updatedAt: string;
}

export interface PlaylistCount {
	numberOfPlaylists: number;
}

import type {
	AxiosInstance,
	AxiosRequestConfig,
	AxiosResponse,
	HeadersDefaults,
	ResponseType,
} from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams
	extends Omit<
		AxiosRequestConfig,
		"data" | "params" | "url" | "responseType"
	> {
	/** set parameter to `true` for call `securityWorker` for this request */
	secure?: boolean;
	/** request path */
	path: string;
	/** content type of request body */
	type?: ContentType;
	/** query params */
	query?: QueryParamsType;
	/** format of response (i.e. response.json() -> format: "json") */
	format?: ResponseType;
	/** request body */
	body?: unknown;
}

export type RequestParams = Omit<
	FullRequestParams,
	"body" | "method" | "query" | "path"
>;

export interface ApiConfig<SecurityDataType = unknown>
	extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
	securityWorker?: (
		securityData: SecurityDataType | null
	) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
	secure?: boolean;
	format?: ResponseType;
}

export enum ContentType {
	Json = "application/json",
	FormData = "multipart/form-data",
	UrlEncoded = "application/x-www-form-urlencoded",
	Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
	public instance: AxiosInstance;
	private securityData: SecurityDataType | null = null;
	private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
	private secure?: boolean;
	private format?: ResponseType;

	constructor({
		securityWorker,
		secure,
		format,
		...axiosConfig
	}: ApiConfig<SecurityDataType> = {}) {
		this.instance = axios.create({
			...axiosConfig,
			baseURL: axiosConfig.baseURL || "",
		});
		this.secure = secure;
		this.format = format;
		this.securityWorker = securityWorker;
	}

	public setSecurityData = (data: SecurityDataType | null) => {
		this.securityData = data;
	};

	protected mergeRequestParams(
		params1: AxiosRequestConfig,
		params2?: AxiosRequestConfig
	): AxiosRequestConfig {
		const method = params1.method || (params2 && params2.method);

		return {
			...this.instance.defaults,
			...params1,
			...(params2 || {}),
			headers: {
				...((method &&
					this.instance.defaults.headers[
						method.toLowerCase() as keyof HeadersDefaults
					]) ||
					{}),
				...(params1.headers || {}),
				...((params2 && params2.headers) || {}),
			},
		};
	}

	protected stringifyFormItem(formItem: unknown) {
		if (typeof formItem === "object" && formItem !== null) {
			return JSON.stringify(formItem);
		} else {
			return `${formItem}`;
		}
	}

	protected createFormData(input: Record<string, unknown>): FormData {
		if (input instanceof FormData) {
			return input;
		}
		return Object.keys(input || {}).reduce((formData, key) => {
			const property = input[key];
			const propertyContent: any[] =
				property instanceof Array ? property : [property];

			for (const formItem of propertyContent) {
				const isFileType =
					formItem instanceof Blob || formItem instanceof File;
				formData.append(
					key,
					isFileType ? formItem : this.stringifyFormItem(formItem)
				);
			}

			return formData;
		}, new FormData());
	}

	public request = async <T = any, _E = any>({
		secure,
		path,
		type,
		query,
		format,
		body,
		...params
	}: FullRequestParams): Promise<AxiosResponse<T>> => {
		const secureParams =
			((typeof secure === "boolean" ? secure : this.secure) &&
				this.securityWorker &&
				(await this.securityWorker(this.securityData))) ||
			{};
		const requestParams = this.mergeRequestParams(
			params,
			secureParams
		);
		const responseFormat = format || this.format || undefined;

		if (
			type === ContentType.FormData &&
			body &&
			body !== null &&
			typeof body === "object"
		) {
			body = this.createFormData(body as Record<string, unknown>);
		}

		if (
			type === ContentType.Text &&
			body &&
			body !== null &&
			typeof body !== "string"
		) {
			body = JSON.stringify(body);
		}

		return this.instance.request({
			...requestParams,
			headers: {
				...(requestParams.headers || {}),
				...(type ? { "Content-Type": type } : {}),
			},
			params: query,
			responseType: responseFormat,
			data: body,
			url: path,
		});
	};
}

/**
 * @title CMS-Service-API
 * @version 1.0.0
 *
 * This is the CMS-Service-API OpenAPI 3.0 specification.
 *
 * Some useful links:
 *   - [The source API definition](/docs/spec)
 */
export class KenticoAPI<
	SecurityDataType extends unknown,
> extends HttpClient<SecurityDataType> {
	health = {
		/**
		 * @description If the service is up and running correctly the response will be OK
		 *
		 * @tags default
		 * @name GetHealth
		 * @summary Check the status of the service before the middlewares
		 * @request GET:/health
		 */
		getHealth: (params: RequestParams = {}) =>
			this.request<Health, Exception>({
				path: `/health`,
				method: "GET",
				format: "json",
				...params,
			}),
	};
	ott = {
		/**
		 * @description Return information concernant all the existing pages in the CMS
		 *
		 * @tags pages
		 * @name IndexPages
		 * @summary index pages
		 * @request GET:/ott/kentico/pages
		 */
		indexPages: (
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				categories?: string;
				/** @example "[gte]27/01/2023" */
				date?: string;
				/** @example "fr" */
				language?: string;
				previewFlag?: boolean;
			},
			params: RequestParams = {}
		) =>
			this.request<PageIndex, Exception>({
				path: `/ott/kentico/pages`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description Returns a page given its codename
		 *
		 * @tags pages
		 * @name GetPage
		 * @summary get one page
		 * @request GET:/ott/kentico/pages/{codeName}
		 * @secure
		 */
		getPage: (
			codeName: string,
			query?: {
				/** @min 1 */
				limit?: number;
				filters?: string;
				after?: string;
				before?: string;
				previewFlag?: boolean;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PageLayout, Exception>({
				path: `/ott/kentico/pages/${codeName}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts different query params
		 *
		 * @tags sections
		 * @name GetDynamicSectionByCodename
		 * @summary return the data of a dynamic section aggregated with videos, events, playlists, etc
		 * @request GET:/ott/kentico/sections/dynamic/{codeName}
		 * @secure
		 */
		getDynamicSectionByCodename: (
			codeName: string,
			query?: {
				/** @min 1 */
				limit?: number;
				filters?: string;
				after?: string;
				before?: string;
				previewFlag?: boolean;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<DynamicSection, Exception>({
				path: `/ott/kentico/sections/dynamic/${codeName}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts different query params
		 *
		 * @tags sections
		 * @name GetStaticSectionByCodename
		 * @summary return the data of a static section aggregated with videos, events, playlists, etc
		 * @request GET:/ott/kentico/sections/static/{codeName}
		 * @secure
		 */
		getStaticSectionByCodename: (
			codeName: string,
			query?: {
				previewFlag?: boolean;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<StaticSection, Exception>({
				path: `/ott/kentico/sections/static/${codeName}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts language and previewFlag as query params
		 *
		 * @tags config
		 * @name GetConfig
		 * @summary returns the config for the site
		 * @request GET:/ott/kentico/config
		 */
		getConfig: (
			query?: {
				/** @example "fr" */
				language?: string;
				previewFlag?: boolean;
			},
			params: RequestParams = {}
		) =>
			this.request<InitConfig, Exception>({
				path: `/ott/kentico/config`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts language and previewFlag as query params
		 *
		 * @tags config
		 * @name GetMobileConfig
		 * @summary returns the config for the site
		 * @request GET:/ott/kentico/config/mobile
		 */
		getMobileConfig: (
			query?: {
				/** @example "fr" */
				language?: string;
				previewFlag?: boolean;
			},
			params: RequestParams = {}
		) =>
			this.request<MobileConfig, Exception>({
				path: `/ott/kentico/config/mobile`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts previewFlag, challengerId, competitionId and language params
		 *
		 * @tags events
		 * @name GetEvents
		 * @summary returns all the events using filters
		 * @request GET:/ott/kentico/events
		 */
		getEvents: (
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				categories?: string;
				/** @example "[gte]27/01/2023" */
				date?: string;
				VOD?: "true" | "false";
				status?: "past" | "future" | "live";
				/** @format uuid */
				challengerId?: string;
				/** @format uuid */
				competitionId?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<OriginsEventIndexItem, Exception>({
				path: `/ott/kentico/events`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts previewFlag, and language params
		 *
		 * @tags events
		 * @name GetEventById
		 * @summary return the data of a event in DB aggregated with the kentico data
		 * @request GET:/ott/kentico/events/{id}
		 */
		getEventById: (
			id: string,
			query?: {
				previewFlag?: boolean;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<OriginsEvent, Exception>({
				path: `/ott/kentico/events/${id}`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts previewFlag, and language params
		 *
		 * @tags events
		 * @name GetEventsV2
		 * @summary returns all the events using filters
		 * @request GET:/ott/v2/kentico/events
		 */
		getEventsV2: (
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				categories?: string;
				/** @example "[gte]27/01/2023" */
				date?: string;
				VOD?: "true" | "false";
				status?: "past" | "future" | "live";
				/** @format uuid */
				challengerId?: string;
				/** @format uuid */
				competitionId?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<OriginsEventArray, Exception>({
				path: `/ott/v2/kentico/events`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts different filters
		 *
		 * @tags videos
		 * @name GetVideos
		 * @summary returns short information of videos in kontent and DB
		 * @request GET:/ott/kentico/videos
		 * @secure
		 */
		getVideos: (
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				/** @example "[gte]27/01/2023" */
				date?: string;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<OriginsVideoIndexItem, Exception>({
				path: `/ott/kentico/videos`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts previewFlag, relatedVideosLimit, language and expand params
		 *
		 * @tags videos
		 * @name GetVideoById
		 * @summary return the data of a video in DB aggregated with the kentico data
		 * @request GET:/ott/kentico/videos/{id}
		 * @secure
		 */
		getVideoById: (
			id: string,
			query?: {
				previewFlag?: boolean;
				/**
				 * @min 1
				 * @max 99
				 */
				relatedVideosLimit?: number;
				/** @example "fr" */
				language?: string;
				/** @example "[Video]" */
				expand?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<OriginsVideoWithRelatedVideos, Exception>({
				path: `/ott/kentico/videos/${id}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description return the number of playlist in the platform for this account
		 *
		 * @tags playlists
		 * @name CountPlaylists
		 * @summary count the number of playlists in the platform for this client
		 * @request GET:/ott/v2/playlists/count
		 */
		countPlaylists: (params: RequestParams = {}) =>
			this.request<PlaylistCount, Exception>({
				path: `/ott/v2/playlists/count`,
				method: "GET",
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit and expand query params
		 *
		 * @tags playlists
		 * @name GetPlaylistV2
		 * @summary return the playlist and related paged videos
		 * @request GET:/ott/v2/playlists/{id}
		 * @secure
		 */
		getPlaylistV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				/** @example "[Video]" */
				expand?: string;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PlaylistWithVideoCard, Exception>({
				path: `/ott/v2/playlists/${id}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags playlists
		 * @name GetPlaylistVideosV2
		 * @summary return the related paginad videos of a playlist
		 * @request GET:/ott/v2/playlists/{id}/videos
		 * @secure
		 */
		getPlaylistVideosV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				/** @example "fr" */
				language?: string;
				/** @example "false" */
				overloadWithKontentData?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedVideoCardAlt, Exception>({
				path: `/ott/v2/playlists/${id}/videos`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name IndexCategoriesV2
		 * @summary return all categories
		 * @request GET:/ott/v2/categories
		 * @secure
		 */
		indexCategoriesV2: (params: RequestParams = {}) =>
			this.request<CategoryEntityArray, Exception>({
				path: `/ott/v2/categories`,
				method: "GET",
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit and expand query params
		 *
		 * @tags categories
		 * @name GetCategoryByIdV2
		 * @summary return the category and/or related paged videos, events, playlists and sub-categories
		 * @request GET:/ott/v2/categories/{id}
		 * @secure
		 */
		getCategoryByIdV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				/** @example "[Video]" */
				expand?: string;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<CategoryCardWithRelationships, Exception>({
				path: `/ott/v2/categories/${id}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description SubCategories are not paged, because, normally, we will never have more than 100 in a category
		 *
		 * @tags categories
		 * @name GetCategorySubCategoriesV2
		 * @summary return the related paged subcategories of category
		 * @request GET:/ott/v2/categories/{id}/subcategories
		 */
		getCategorySubCategoriesV2: (
			id: string,
			params: RequestParams = {}
		) =>
			this.request<SubCategoryEntityArray, Exception>({
				path: `/ott/v2/categories/${id}/subcategories`,
				method: "GET",
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name GetCategoryVideosV2
		 * @summary return the related paged videos of category
		 * @request GET:/ott/v2/categories/{id}/videos
		 * @secure
		 */
		getCategoryVideosV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				/** @example "fr" */
				language?: string;
				/** @example "false" */
				overloadWithKontentData?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedVideoCardAlt, Exception>({
				path: `/ott/v2/categories/${id}/videos`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name GetCategoryEventsV2
		 * @summary return the related paged events of category
		 * @request GET:/ott/v2/categories/{id}/events
		 * @secure
		 */
		getCategoryEventsV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedEventCardAlt, Exception>({
				path: `/ott/v2/categories/${id}/events`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name GetCategoryPlaylistsV2
		 * @summary return the related paged playlists of category
		 * @request GET:/ott/v2/categories/{id}/playlists
		 * @secure
		 */
		getCategoryPlaylistsV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedPlaylistCardAlt, Exception>({
				path: `/ott/v2/categories/${id}/playlists`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name GetCategorySubCategoriesVideosV2
		 * @summary return the related paged videos matching the category and subcategory
		 * @request GET:/ott/v2/categories/{id}/subcategories/{scatid}/videos
		 * @secure
		 */
		getCategorySubCategoriesVideosV2: (
			id: string,
			scatid: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
				/** @example "fr" */
				language?: string;
				/** @example "false" */
				overloadWithKontentData?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedVideoCardAlt, Exception>({
				path: `/ott/v2/categories/${id}/subcategories/${scatid}/videos`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts limit, before, and after query params
		 *
		 * @tags categories
		 * @name GetCategorySubCategoriesEventsV2
		 * @summary return the related paged events matching the category and subcategory
		 * @request GET:/ott/v2/categories/{id}/subcategories/{scatid}/events
		 * @secure
		 */
		getCategorySubCategoriesEventsV2: (
			id: string,
			scatid: string,
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedEventCardAlt, Exception>({
				path: `/ott/v2/categories/${id}/subcategories/${scatid}/events`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts expanding Categories, Videos and Events
		 *
		 * @tags subcategories
		 * @name GetSubCategoryByIdV2
		 * @summary return the subcategory data by id and its related entities
		 * @request GET:/ott/v2/subcategories/{id}
		 * @secure
		 */
		getSubCategoryByIdV2: (
			id: string,
			query?: {
				/** @min 1 */
				limit?: number;
				/** @example "[Video]" */
				expand?: string;
				/** @example "fr" */
				language?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<SubCategoryCard, Exception>({
				path: `/ott/v2/subcategories/${id}`,
				method: "GET",
				query: query,
				secure: true,
				format: "json",
				...params,
			}),

		/**
		 * @description accepts pagination parameters
		 *
		 * @tags competitions
		 * @name GetCompetitions
		 * @summary return competitions for the current account
		 * @request GET:/ott/kentico/competitions
		 */
		getCompetitions: (
			query?: {
				/** @min 1 */
				limit?: number;
				after?: string;
				before?: string;
			},
			params: RequestParams = {}
		) =>
			this.request<PagedCompetitionEntityArrayAlt, Exception>({
				path: `/ott/kentico/competitions`,
				method: "GET",
				query: query,
				format: "json",
				...params,
			}),
	};
}
