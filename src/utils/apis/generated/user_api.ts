/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface Health {
  status?: "OK" | "KO";
}

export type Error = string;

export interface Exception {
  status?: number;
  type?: string;
  message?: string;
}

export type FanLoginInput = Firebase | RSCA | ASSE | FFG;

export interface Firebase {
  vendorSSO: {
    /** @default "firebase" */
    name: "firebase";
    /** a JWT from firebase */
    token: string;
    userDetails?: {
      firstname?: string;
      lastname?: string;
      providerOrigin?: string;
    };
  };
}

export interface DeleteFirebaseAccount {
  /** A JWT from firebase */
  firebaseToken: string;
}

export interface RSCA {
  vendorSSO: {
    /** @default "rsca" */
    name: "rsca";
    /** a JWT from RSCA's SSO */
    token: string;
  };
}

export interface ASSE {
  vendorSSO: {
    /** @default "asse" */
    name: "asse";
    token: string;
    tokenMeta: string;
  };
}

export interface FFG {
  vendorSSO: {
    /** @default "ffgolf.tv" */
    name: "ffgolf.tv";
    /** a JWT from FFG's SSO */
    token: string;
  };
}

export interface FanFavoriteVideoRequestBody {
  /** @format uuid */
  VideoId: string;
}

export interface FanFavoritePlaylistRequestBody {
  /** @format uuid */
  PlaylistId: string;
}

export interface FanFavoriteCategoryRequestBody {
  /** @format uuid */
  CategoryId: string;
}

export interface FanLoginResponse {
  /** @format uuid */
  id: string;
  accessToken: string;
  refreshToken: string;
  /** @default "Bearer" */
  tokenType?: "Bearer";
  expiresIn?: number;
  action?: "updated" | "created";
}

export type FanLogoutResponse = string;

export interface UserLogin {
  username: string;
  password: string;
}

export interface UserLoginResponse {
  /** @format uuid */
  id: string;
  accessToken: string;
  /** @default "Bearer" */
  tokenType?: "Bearer";
  expiresIn?: number;
}

export type UserLogoutResponse = string;

export interface ProfilesIdArray {
  Profiles?: string[];
}

export interface ProfileClubBody {
  /** @format uuid */
  womenClubProfileId?: string;
  /** @format uuid */
  menClubProfileId?: string;
}

export interface Account {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  key: string;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  meta?: any;
  paymentConfig?: any;
  /** @format uuid */
  SportId?: string | null;
  cachingData?: any;
  sendMailOptions: any;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Modules?: Module[];
  Groups?: Group[];
  Fans?: Fan[];
}

export interface Fan {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  username?: string | null;
  /** @maxLength 255 */
  firstname?: string | null;
  /** @maxLength 255 */
  lastname?: string | null;
  gender?: "male" | "female" | null;
  /** @maxLength 255 */
  country?: string | null;
  /** @format int32 */
  birthYear?: number | null;
  /** @format date-time */
  birthdate?: string | null;
  /** @maxLength 255 */
  address?: string | null;
  /** @maxLength 255 */
  phone?: string | null;
  /** @maxLength 255 */
  imageUrl?: string | null;
  status?: "active" | "inactive" | null;
  /** @maxLength 255 */
  password?: string | null;
  /** @maxLength 2 */
  preferredLanguage?: string | null;
  /** @format date-time */
  deleteAt?: string | null;
  /** @maxLength 255 */
  resetPasswordToken?: string | null;
  /** @maxLength 255 */
  confirmationToken?: string | null;
  /** @maxLength 255 */
  abortToken?: string | null;
  meta?: any;
  /** @format uuid */
  menClubProfileId?: string | null;
  /** @format uuid */
  womenClubProfileId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Account?: Account;
  FanActivities?: FanActivity[];
  FanSessions?: FanSession[];
  Profiles?: Profile[];
  Fans_Profiles?: FansProfile[];
  FanFavoriteVideos?: FanFavoriteVideo[];
  FanFavoriteCategories?: FanFavoriteCategory[];
  FanFavoritePlaylists?: FanFavoritePlaylist[];
  /** @format uuid */
  AccountId?: string;
  Products?: Product[];
}

export interface FanActivity {
  /** @format uuid */
  id: string;
  type: "watch" | "bookmark" | "like";
  meta?: any;
  resourceType:
    | "event"
    | "article"
    | "video"
    | "challenger"
    | "competition"
    | "team"
    | "teammate"
    | "marker"
    | "message"
    | "tag"
    | "season"
    | "round";
  /** @format uuid */
  resourceId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Fan?: Fan;
}

export interface FanFavoriteCategory {
  /** @format uuid */
  FanId: string;
  /** @format uuid */
  CategoryId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface FanFavoritePlaylist {
  /** @format uuid */
  FanId: string;
  /** @format uuid */
  PlaylistId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface FanFavoriteVideo {
  /** @format uuid */
  FanId: string;
  /** @format uuid */
  VideoId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface FansProfile {
  /** @format uuid */
  id: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Fan?: Fan;
  Profile?: Profile;
}

export interface FanSession {
  /** @format uuid */
  id: string;
  /** @maxLength 384 */
  accessToken: string;
  /** @maxLength 384 */
  refreshToken: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Fan?: Fan;
}

export interface Group {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format int32 */
  accessLevel?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Accounts?: Account[];
  Roles?: Role[];
  Users?: User[];
}

export interface Module {
  /** @format uuid */
  id: string;
  /** @format int32 */
  code: number;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Accounts?: Account[];
}

export interface Profile {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  type?: "payment" | "marketing" | "men-club" | "women-club" | "club-subscriber" | null;
  category?: "communication" | "competition" | "favorite-men-club" | "favorite-women-club" | "premium" | null;
  /** @maxLength 255 */
  providerId?: string | null;
  retainOnLogin: boolean;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  /** @format uuid */
  AccountId: string;
}

export interface Role {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format int32 */
  accessLevel?: number | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Groups?: Group[];
}

export interface User {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  username: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  lastName?: string | null;
  /** @maxLength 255 */
  firstName?: string | null;
  /** @maxLength 255 */
  phone?: string | null;
  type?: "active" | "inactive" | "system" | null;
  /** @maxLength 255 */
  password?: string | null;
  /** @maxLength 255 */
  resetPasswordToken?: string | null;
  /** @maxLength 255 */
  confirmationToken?: string | null;
  meta?: any;
  oauth?: any;
  dashboard?: any;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  Groups?: Group[];
}

export interface AccountsModules {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface AccountsGroups {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface GroupsRole {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface GroupsUsers {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface FanWithoutRefs {
  /** @format uuid */
  id: string;
  /** @maxLength 255 */
  email: string;
  /** @maxLength 255 */
  username?: string | null;
  /** @maxLength 255 */
  firstname?: string | null;
  /** @maxLength 255 */
  lastname?: string | null;
  gender?: "male" | "female" | null;
  /** @maxLength 255 */
  country?: string | null;
  /** @format int32 */
  birthYear?: number | null;
  /** @format date-time */
  birthdate?: string | null;
  /** @maxLength 255 */
  address?: string | null;
  /** @maxLength 255 */
  phone?: string | null;
  /** @maxLength 255 */
  imageUrl?: string | null;
  status?: "active" | "inactive" | null;
  /** @maxLength 255 */
  password?: string | null;
  /** @maxLength 2 */
  preferredLanguage?: string | null;
  /** @format date-time */
  deleteAt?: string | null;
  /** @maxLength 255 */
  resetPasswordToken?: string | null;
  /** @maxLength 255 */
  confirmationToken?: string | null;
  /** @maxLength 255 */
  abortToken?: string | null;
  meta?: any;
  /** @format uuid */
  menClubProfileId?: string | null;
  /** @format uuid */
  womenClubProfileId?: string | null;
  /** @format uuid */
  AccountId: string;
}

export interface FanInputBody {
  /** @format uuid */
  id?: string;
  /** @maxLength 255 */
  email?: string;
  /** @maxLength 255 */
  username?: string | null;
  /** @maxLength 255 */
  firstname?: string | null;
  /** @maxLength 255 */
  lastname?: string | null;
  gender?: "male" | "female" | null;
  /** @maxLength 255 */
  country?: string | null;
  /** @format int32 */
  birthYear?: number | null;
  /** @format date-time */
  birthdate?: string | null;
  /** @maxLength 255 */
  address?: string | null;
  /** @maxLength 255 */
  phone?: string | null;
  /** @maxLength 255 */
  imageUrl?: string | null;
  status?: "active" | "inactive" | null;
  /** @maxLength 255 */
  password?: string | null;
  /** @maxLength 2 */
  preferredLanguage?: string | null;
  /** @format date-time */
  deleteAt?: string | null;
  /** @maxLength 255 */
  resetPasswordToken?: string | null;
  /** @maxLength 255 */
  confirmationToken?: string | null;
  /** @maxLength 255 */
  abortToken?: string | null;
  meta?: any;
  /** @format uuid */
  AccountId?: string;
}

export type ProfileArray = Profile[];

export interface Product {
  /** @default "Product" */
  _type: "Product";
  /** @format uuid */
  accountId: string;
  data?: {
    description?: string;
    weight?: number;
  };
  /** @default "Product" */
  entityType: "Product";
  /** @format uuid */
  id: string;
  name?: string;
  /** @format uuid */
  productId: string;
  isStandard?: boolean;
  /** @default "draft" */
  status: "draft" | "published" | "archived";
  /** @format date-time */
  created?: string;
  /** @format date-time */
  updated?: string;
  paymentOffers: PaymentOffer;
  profiles?: Profile[];
}

export interface PaymentOffer {
  entityType: "PaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  productId: string;
  /** @format uuid */
  paymentOfferId: string;
  /** @default "active" */
  status: "active" | "archived";
  provider: "stripe" | "google" | "apple";
  providerPaymentOfferId: string;
  name: string;
  data?: {
    isLimitedDuration?: boolean;
    population?: string;
    weight?: number;
    currency: string;
    interval?: "day" | "week" | "month" | "year";
    type: "one_time" | "recurring";
    price: number;
    intervalCount?: number;
  };
  _type: "PaymentOffer";
  /** @format date-time */
  created?: string;
  /** @format date-time */
  updated?: string;
  duration?: number;
  /** @format date-time */
  expiryDate?: string;
  associatedProfiles?: ProfilePaymentOffer[];
  isPPV: boolean;
}

export interface ProfileProduct {
  entityType: "ProfileProduct";
  _type?: "ProfileProduct";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  productId: string;
  data?: object;
}

export interface ProfilePaymentOffer {
  entityType: "ProfilePaymentOffer";
  _type?: "ProfilePaymentOffer";
  /** @format uuid */
  accountId: string;
  /** @format uuid */
  profileId: string;
  /** @format uuid */
  paymentOfferId: string;
  data?: object;
}

export interface AbortDeleteFan {
  /** @format uuid */
  abortToken: string;
}

export interface RefreshTokenBody {
  /**
   * @format application/jwt
   * @pattern ^([a-zA-Z0-9_=]+)\.([a-zA-Z0-9_=]+)\.([a-zA-Z0-9_\-\+\/=]+)$
   * @example "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************.PiUUnm7_XM7rbCg-8D2bR3v82bzvWFWuiMWc6JMi9xQ"
   */
  refreshToken?: string;
}

export interface Cursor {
  before?: string | null;
  after?: string | null;
}

export interface PaginatedFanFavoriteVideos {
  items?: FanFavoriteVideo[];
  cursor?: Cursor;
}

export interface PaginatedFanFavoriteCategories {
  items?: FanFavoriteCategory[];
  cursor?: Cursor;
}

export interface PaginatedFanFavoritePlaylists {
  items?: FanFavoritePlaylist[];
  cursor?: Cursor;
}

export type FanFavorite =
  | OptionalPaginatedFanFavoriteVideos
  | OptionalPaginatedFanFavoritePlaylists
  | OptionalPaginatedFanFavoriteCategories;

export type OptionalPaginatedFanFavoriteVideos = FanFavoriteVideo[] | PaginatedFanFavoriteVideos | PaginatedVideos;

export type OptionalPaginatedFanFavoritePlaylists =
  | FanFavoritePlaylist[]
  | PaginatedFanFavoritePlaylists
  | PaginatedPlaylists;

export type OptionalPaginatedFanFavoriteCategories =
  | FanFavoriteCategory[]
  | PaginatedFanFavoriteCategories
  | PaginatedCategories;

export type FanFavoriteItem = FanFavoriteVideo | FanFavoritePlaylist | FanFavoriteCategory;

export type FavoriteItemBody =
  | FanFavoriteVideoRequestBody
  | FanFavoritePlaylistRequestBody
  | FanFavoriteCategoryRequestBody;

export interface Video {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  description?: string | null;
  /** @format float */
  duration?: number | null;
  /** @maxLength 255 */
  poster?: string | null;
  Tags?: Tag[];
  /** @format date-time */
  publicationDate?: string | null;
  Categories?: Category[];
  VideoCategorySubCategories?: VideoCategorySubCategory[];
  /** @format int32 */
  playlistRank?: number | null;
  /** @format uuid */
  PlaylistId?: string | null;
  /** @format date-time */
  lastUpdated?: string | null;
}

export interface VideoCategorySubCategory {
  /** @format uuid */
  id?: string | null;
  /** @format uuid */
  VideoId?: string | null;
  /** @format uuid */
  CategoryId?: string | null;
  /** @format uuid */
  SubCategoryId?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  SubCategory?: SubCategory;
}

export interface Category {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  description?: string | null;
  /** @maxLength 255 */
  thumbnail?: string | null;
  /** @maxLength 255 */
  portraitThumbnail?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  VideoCategory?: VideoCategory;
}

export interface VideoCategory {
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  /** @format uuid */
  VideoId?: string | null;
  /** @format uuid */
  CategoryId?: string | null;
}

export interface SubCategory {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface Playlist {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  name: string;
  /** @maxLength 255 */
  accountId: string;
  /** @format uuid */
  CategoryId?: string | null;
  description?: string | null;
  /** @maxLength 255 */
  thumbnail?: string | null;
  /** @maxLength 255 */
  portraitThumbnail?: string | null;
  /** @maxLength 255 */
  heroImageDesktop?: string | null;
  /** @maxLength 255 */
  heroImageMobile?: string | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
  /** @format int32 */
  numberOfVideos?: number | null;
}

export interface Tag {
  /** @format uuid */
  id?: string | null;
  /** @maxLength 255 */
  tagType?: string | null;
  /** @maxLength 255 */
  name: string;
  options?: any;
  /** @format uuid */
  AccountId: string;
  placeholders?: string[] | null;
  /** @format date-time */
  createdAt: string;
  /** @format date-time */
  updatedAt: string;
}

export interface PaginatedVideos {
  items?: Video[];
  cursor?: Cursor;
}

export interface PaginatedPlaylists {
  items?: Playlist[];
  cursor?: Cursor;
}

export interface PaginatedCategories {
  items?: Category[];
  cursor?: Cursor;
}

import type { AxiosInstance, AxiosRequestConfig, AxiosResponse, HeadersDefaults, ResponseType } from "axios";
import axios from "axios";

export type QueryParamsType = Record<string | number, any>;

export interface FullRequestParams extends Omit<AxiosRequestConfig, "data" | "params" | "url" | "responseType"> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean;
  /** request path */
  path: string;
  /** content type of request body */
  type?: ContentType;
  /** query params */
  query?: QueryParamsType;
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType;
  /** request body */
  body?: unknown;
}

export type RequestParams = Omit<FullRequestParams, "body" | "method" | "query" | "path">;

export interface ApiConfig<SecurityDataType = unknown> extends Omit<AxiosRequestConfig, "data" | "cancelToken"> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void;
  secure?: boolean;
  format?: ResponseType;
}

export enum ContentType {
  Json = "application/json",
  FormData = "multipart/form-data",
  UrlEncoded = "application/x-www-form-urlencoded",
  Text = "text/plain",
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance;
  private securityData: SecurityDataType | null = null;
  private securityWorker?: ApiConfig<SecurityDataType>["securityWorker"];
  private secure?: boolean;
  private format?: ResponseType;

  constructor({ securityWorker, secure, format, ...axiosConfig }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({ ...axiosConfig, baseURL: axiosConfig.baseURL || "" });
    this.secure = secure;
    this.format = format;
    this.securityWorker = securityWorker;
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data;
  };

  protected mergeRequestParams(params1: AxiosRequestConfig, params2?: AxiosRequestConfig): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method);

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method && this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) || {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    };
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === "object" && formItem !== null) {
      return JSON.stringify(formItem);
    } else {
      return `${formItem}`;
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input;
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key];
      const propertyContent: any[] = property instanceof Array ? property : [property];

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File;
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem));
      }

      return formData;
    }, new FormData());
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === "boolean" ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {};
    const requestParams = this.mergeRequestParams(params, secureParams);
    const responseFormat = format || this.format || undefined;

    if (type === ContentType.FormData && body && body !== null && typeof body === "object") {
      body = this.createFormData(body as Record<string, unknown>);
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== "string") {
      body = JSON.stringify(body);
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { "Content-Type": type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    });
  };
}

/**
 * @title Users-service-api
 * @version 1.0.0
 *
 * This is the Users service OpenAPI 3.0 specification.
 *
 * Some useful links:
 *   - [The source API definition](/docs/swagger.json)
 */
export class UserAPI<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  auth = {
    /**
     * @description If the service is up and running correctly the response will be 'ok'
     *
     * @tags auth
     * @name GetAuthHealth
     * @summary Check the status of the service after the middlewares
     * @request GET:/auth/health
     */
    getAuthHealth: (params: RequestParams = {}) =>
      this.request<Health, Error>({
        path: `/auth/health`,
        method: "GET",
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags auth
     * @name LoginUser
     * @summary Allows a User of the backoffice to get a JWT token that can be used as authorization to access other resources
     * @request POST:/auth/login
     */
    loginUser: (data: UserLogin, params: RequestParams = {}) =>
      this.request<UserLoginResponse, Error | Exception>({
        path: `/auth/login`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags auth
     * @name LogoutUser
     * @summary Allows a User of the backoffice to invalidate his token
     * @request DELETE:/auth/logout
     * @secure
     */
    logoutUser: (params: RequestParams = {}) =>
      this.request<UserLogoutResponse, Error | Exception>({
        path: `/auth/logout`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * @description The body could have differents fields depending on the Customer's SSO, it allows to exchange a token from Firebase, RSCA, ASSE or FFG SSOs
     *
     * @tags auth
     * @name LoginFan
     * @summary Allows a Fan to get a JWT token that can be used as authorization to access other resources
     * @request POST:/auth/fan/login
     */
    loginFan: (
      data: FanLoginInput,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanLoginResponse, Error | Exception>({
        path: `/auth/fan/login`,
        method: "POST",
        query: query,
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags auth
     * @name LogoutFan
     * @summary Invalidates a JWT token created previously with login
     * @request DELETE:/auth/fan/logout
     * @secure
     */
    logoutFan: (params: RequestParams = {}) =>
      this.request<FanLogoutResponse, Error | Exception>({
        path: `/auth/fan/logout`,
        method: "DELETE",
        secure: true,
        ...params,
      }),

    /**
     * No description
     *
     * @tags auth
     * @name RefreshTokenFan
     * @summary Exchange a refresh token by a new valid access token
     * @request POST:/auth/fan/refresh-token
     */
    refreshTokenFan: (data: RefreshTokenBody, params: RequestParams = {}) =>
      this.request<FanLoginResponse, Error | Exception>({
        path: `/auth/fan/refresh-token`,
        method: "POST",
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),
  };
  health = {
    /**
     * @description If the service is up and running correctly the response will be 'OK'
     *
     * @name GetHealth
     * @summary Check the status of the service before the middlewares
     * @request GET:/health
     */
    getHealth: (params: RequestParams = {}) =>
      this.request<Health, Error>({
        path: `/health`,
        method: "GET",
        format: "json",
        ...params,
      }),
  };
  platform = {
    /**
     * No description
     *
     * @tags fans
     * @name GetFanByEmail
     * @summary Returns whether the fan exists or not
     * @request GET:/platform/fans
     */
    getFanByEmail: (
      query: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /**
         * filter by email
         * @format email
         */
        email: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<void, Error | Exception>({
        path: `/platform/fans`,
        method: "GET",
        query: query,
        ...params,
      }),

    /**
     * No description
     *
     * @tags fans
     * @name GetFanMe
     * @summary Returns all the Fan's Data, including profiles, favorites and products
     * @request GET:/platform/fans/me
     * @secure
     */
    getFanMe: (
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** expand related entities. ex: [Product,Profile,FanFavoriteVideo,FanFavoriteCategory,FanFavoritePlaylist] */
        expand?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Fan, Error | Exception>({
        path: `/platform/fans/me`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags fans
     * @name UpdateFanMe
     * @summary Returns all the Fan's Data, including profiles, favorites and products
     * @request PUT:/platform/fans/me
     * @secure
     */
    updateFanMe: (
      data: FanInputBody,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanWithoutRefs, Error | Exception>({
        path: `/platform/fans/me`,
        method: "PUT",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This method also updates all the fan subscriptions to be cancelled at the period end, the fan should be removed after the delay period is reached. It works only for Firebase based SSOs
     *
     * @tags fans
     * @name DeleteFanMe
     * @summary Schedule the fan for deletion after predefined delay, default to 15 days
     * @request DELETE:/platform/fans/me
     * @secure
     */
    deleteFanMe: (
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanWithoutRefs, Error | Exception>({
        path: `/platform/fans/me`,
        method: "DELETE",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to set there is a profile club or payment it is omitted.
     *
     * @tags fans
     * @name UpdateFanMeProfiles
     * @summary Set the marketing profiles to the current fan
     * @request PUT:/platform/fans/me/profiles
     * @secure
     */
    updateFanMeProfiles: (
      data: ProfilesIdArray,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Fan, Error | Exception>({
        path: `/platform/fans/me/profiles`,
        method: "PUT",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to add there is a profile club or payment it is omitted.
     *
     * @tags fans
     * @name PatchFanMeProfiles
     * @summary Add new marketing profiles to the Fan
     * @request PATCH:/platform/fans/me/profiles
     * @secure
     */
    patchFanMeProfiles: (
      data: ProfilesIdArray,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Fan, Error | Exception>({
        path: `/platform/fans/me/profiles`,
        method: "PATCH",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint does not allow setting profiles of type payment or club. If in the list of profiles Ids to add there is a profile club or payment it is omitted.
     *
     * @tags fans
     * @name RemoveFanMeProfiles
     * @summary Add new marketing profiles to the Fan
     * @request DELETE:/platform/fans/me/profiles
     * @secure
     */
    removeFanMeProfiles: (
      query: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** The list of profiles to detach to a Fan */
        Profiles: string[];
      },
      params: RequestParams = {},
    ) =>
      this.request<Fan, Error | Exception>({
        path: `/platform/fans/me/profiles`,
        method: "DELETE",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * @description This endpoint does not allow setting profiles of type payment or marketing. If in the list of profiles Ids to set there is a profile marketing or payment it is omitted. Only 1 profile club men and 1 profile club women can be set, and they cannot be changed after that
     *
     * @tags fans
     * @name UpdateFanMeProfilesClub
     * @summary Set the club profiles to the current fan
     * @request PUT:/platform/fans/me/profiles/clubs
     * @deprecated
     * @secure
     */
    updateFanMeProfilesClub: (
      data: ProfileClubBody,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<Fan, Error | Exception>({
        path: `/platform/fans/me/profiles/clubs`,
        method: "PUT",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags fans
     * @name AbortFanDelete
     * @summary Abort the fan deletion if it is make before the account is definitely removed
     * @request POST:/platform/fans/me/abort
     */
    abortFanDelete: (
      data: AbortDeleteFan,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<FanWithoutRefs, Error | Exception>({
        path: `/platform/fans/me/abort`,
        method: "POST",
        query: query,
        body: data,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags fans/firebase
     * @name RemoveFirebaseFanAccount
     * @summary Remove this fan from firebase only if it doesn't exists in the internal storage
     * @request POST:/platform/fans/firebase/delete
     */
    removeFirebaseFanAccount: (
      data: DeleteFirebaseAccount,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
      },
      params: RequestParams = {},
    ) =>
      this.request<void, Error | Exception>({
        path: `/platform/fans/firebase/delete`,
        method: "POST",
        query: query,
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags favorites
     * @name GetFanFavorites
     * @summary Returns the fan's favorites by type (Videos, Categories, Playlists)
     * @request GET:/platform/favorites
     * @secure
     */
    getFanFavorites: (
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /**
         * limit the number of items in the response, allowing getting paginated items
         * @min 1
         * @max 99
         */
        limit?: number;
        /**
         * when using limit, allows getting the previous page
         * @format byte
         */
        before?: string;
        /**
         * when using limit, allows getting the next page
         * @format byte
         */
        after?: string;
        /** if true it fetches aussi the entity data from main-api */
        aggregate?: "true" | "false";
        /** The type of favorite being processed */
        type?: "video" | "playlist" | "category";
      },
      params: RequestParams = {},
    ) =>
      this.request<FanFavorite, Error | Exception>({
        path: `/platform/favorites`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags favorites
     * @name AddFanFavorite
     * @summary Add a fan favorite item by type
     * @request POST:/platform/favorites
     * @secure
     */
    addFanFavorite: (
      data: FavoriteItemBody,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** The type of favorite being processed */
        type?: "video" | "playlist" | "category";
      },
      params: RequestParams = {},
    ) =>
      this.request<FanFavoriteItem, Error | Exception>({
        path: `/platform/favorites`,
        method: "POST",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags favorites
     * @name DeleteFanFavorite
     * @summary Remove a fan favorite item by type
     * @request DELETE:/platform/favorites
     * @secure
     */
    deleteFanFavorite: (
      data: FavoriteItemBody,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** The type of favorite being processed */
        type?: "video" | "playlist" | "category";
      },
      params: RequestParams = {},
    ) =>
      this.request<void, Error | Exception>({
        path: `/platform/favorites`,
        method: "DELETE",
        query: query,
        body: data,
        secure: true,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags favorites
     * @name GetFanFavoriteById
     * @summary Returns the fan's favorites by type (Videos, Categories, Playlists)
     * @request GET:/platform/favorites/{itemId}
     * @secure
     */
    getFanFavoriteById: (
      itemId: string,
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** The type of favorite being processed */
        type?: "video" | "playlist" | "category";
      },
      params: RequestParams = {},
    ) =>
      this.request<FanFavoriteItem, Error | Exception>({
        path: `/platform/favorites/${itemId}`,
        method: "GET",
        query: query,
        secure: true,
        format: "json",
        ...params,
      }),

    /**
     * No description
     *
     * @tags profiles
     * @name GetProfiles
     * @summary Returns the list of profiles for this account, this list can be filtered by type & category
     * @request GET:/platform/profiles
     */
    getProfiles: (
      query?: {
        /** The customer's account key passed as query param, if it is not already set in the headers */
        accountKey?: string;
        /** filter profiles by type */
        type?: "payment" | "marketing" | "men-club" | "women-club" | "club-subscriber";
        /** filter profiles by category */
        category?: "communication" | "competition" | "favorite-men-club" | "favorite-women-club" | "premium";
      },
      params: RequestParams = {},
    ) =>
      this.request<ProfileArray, Error | Exception>({
        path: `/platform/profiles`,
        method: "GET",
        query: query,
        format: "json",
        ...params,
      }),
  };
}
