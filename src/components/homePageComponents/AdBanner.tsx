import React from "react";
import {
	StyleSheet,
	TouchableOpacity,
	Image,
	Linking,
	Platform,
	Dimensions,
} from "react-native";

interface AdBannerProps {
	imageUrl: string;
	mobileImageUrl: string;
	redirectionTarget: string;
}

const AdBanner: React.FC<AdBannerProps> = ({
	imageUrl,
	mobileImageUrl,
	redirectionTarget,
}) => {
	const windowWidth = Dimensions.get("window").width;
	const heightMultiplier = 0.12; // 0.08

	const handlePress = async () => {
		if (redirectionTarget) {
			try {
				await Linking.openURL(redirectionTarget);
			} catch (error) {
				console.error("Error opening URL:", error);
			}
		}
	};

	return (
		<TouchableOpacity
			style={[styles.container, { backgroundColor: "#4B0082" }]}
			onPress={handlePress}
			activeOpacity={0.9}
			disabled={true} // #TODO disable for now - redirect throws error on tv app
		>
			<Image
				source={{ uri: imageUrl }}
				style={[
					styles.image,
					{
						height: windowWidth * heightMultiplier,
						width: windowWidth,
					},
				]}
				resizeMode="cover"
			/>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		width: "100%",
		marginVertical: 0,
		alignItems: "center",
		paddingHorizontal: 0,
	},
	image: {
		backgroundColor: "transparent",
	},
});

export default AdBanner;
