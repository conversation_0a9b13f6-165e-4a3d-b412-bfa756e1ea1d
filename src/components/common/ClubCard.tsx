import React from "react";
import {
	View,
	Text,
	Image,
	TouchableOpacity,
	StyleSheet,
	StyleProp,
	ViewStyle,
} from "react-native";
import { scale } from "../../utils/helpers/dimensionScale.helper";
import {
	optimizeImageUrl,
	PRESETS,
} from "../../utils/helpers/imageOptimization.helper";

// Component for displaying club logos in a rectangular card format

interface ClubCardProps {
	id: string;
	title: string;
	imageUrl?: string;
	onPress: (id: string, type?: string, eventData?: any) => void;
	style?: StyleProp<ViewStyle>;
	size?: number;
	showTitle?: boolean;
}

/**
 * ClubCard Component
 *
 * Displays a club logo in a rectangular format with optional title
 * Used in carousels for club sections
 */
const ClubCard: React.FC<ClubCardProps> = ({
	id,
	title,
	imageUrl,
	onPress,
	style,
	size = scale(240),
	showTitle = false,
}) => {
	// Optimize image URL if available
	const optimizedImageUrl = imageUrl
		? optimizeImageUrl(imageUrl, PRESETS.CLUB_LOGO)
		: null;

	// Debug logs removed for production

	return (
		<TouchableOpacity
			style={[styles.container, style, { opacity: 0.7 }]}
			onPress={() =>
				onPress(id, "club", {
					name: title,
					poster: imageUrl,
					// Add club-specific data for ClubDetailsPage navigation
					clubId: id,
					clubName: title,
					clubImage: imageUrl,
				})
			}
			activeOpacity={1}
			accessible={true}
			accessibilityLabel={`Club: ${title}`}
			accessibilityRole="button"
		>
			<View
				style={[
					styles.imageContainer,
					{
						width: size * 0.85, // Slightly wider for better proportions
						height: size * 1.4, // Taller height for more prominent club images
						borderRadius: scale(10),
					},
				]}
			>
				{optimizedImageUrl ? (
					<Image
						source={{ uri: optimizedImageUrl }}
						style={styles.image}
						resizeMode="cover" // Use cover to fill the entire card without white space
						testID={`club-image-${id}`}
						onError={(error) =>
							console.error(
								`Image load error for ${title}:`,
								error.nativeEvent.error
							)
						}
					/>
				) : (
					<View style={styles.placeholderContainer}>
						<Text style={styles.placeholderText}>
							{title.substring(0, 2).toUpperCase()}
						</Text>
						<Text style={styles.placeholderSubtext}>No Image</Text>
					</View>
				)}
			</View>

			{showTitle && (
				<Text
					style={styles.title}
					numberOfLines={2}
				>
					{title}
				</Text>
			)}
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		alignItems: "center",
		marginHorizontal: scale(8), // Increased horizontal margin for better spacing
	},
	imageContainer: {
		overflow: "hidden",
		backgroundColor: "transparent", // Transparent background to let logo colors show through
		borderWidth: 0, // Remove border for cleaner look
		justifyContent: "center",
		alignItems: "center",
		padding: 0, // Remove any padding
		margin: 0, // Remove any margin
	},
	image: {
		width: "100%",
		height: "100%",
		backgroundColor: "transparent", // Remove white background
		padding: 0, // Remove any padding
		margin: 0, // Remove any margin
	},
	placeholderContainer: {
		width: "100%",
		height: "100%",
		justifyContent: "center",
		alignItems: "center",
	},
	placeholderText: {
		color: "white",
		fontSize: scale(24),
		fontWeight: "bold",
	},
	placeholderSubtext: {
		color: "rgba(255, 255, 255, 0.7)",
		fontSize: scale(12),
		marginTop: scale(6), // Increased margin
	},
	title: {
		fontSize: scale(18),
		fontWeight: "500",
		color: "white",
		marginTop: scale(1),
		textAlign: "center",
		width: scale(160),
	},
});

export default React.memo(ClubCard);
